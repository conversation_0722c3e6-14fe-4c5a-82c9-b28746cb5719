package services

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/company/cdh/apps/order-service/events"
	"github.com/company/cdh/apps/order-service/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockOrderRepository is a mock implementation of OrderRepository
type MockOrderRepository struct {
	mock.Mock
}

func (m *MockOrderRepository) CreateOrder(req *models.OrderRequest) (*models.Order, error) {
	args := m.Called(req)
	return args.Get(0).(*models.Order), args.Error(1)
}

func (m *MockOrderRepository) GetOrderByID(id int) (*models.Order, error) {
	args := m.Called(id)
	return args.Get(0).(*models.Order), args.Error(1)
}

func (m *MockOrderRepository) GetOrderByNumber(orderNumber string) (*models.Order, error) {
	args := m.Called(orderNumber)
	return args.Get(0).(*models.Order), args.Error(1)
}

func (m *MockOrderRepository) UpdateOrderStatus(id int, status models.OrderStatus) error {
	args := m.Called(id, status)
	return args.Error(0)
}

// MockProducer is a mock implementation of Producer
type MockProducer struct {
	mock.Mock
}

func (m *MockProducer) PublishOrderCreated(event *events.OrderCreatedEvent) error {
	args := m.Called(event)
	return args.Error(0)
}

func (m *MockProducer) Close() error {
	args := m.Called()
	return args.Error(0)
}

func TestOrderService_CreateOrder_Success(t *testing.T) {
	// Setup mocks
	mockRepo := new(MockOrderRepository)
	mockProducer := new(MockProducer)

	// Create service
	service := NewOrderService(mockRepo, mockProducer)

	// Test data
	req := &models.OrderRequest{
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       99.99,
	}

	expectedOrder := &models.Order{
		ID:          123,
		OrderNumber: "ORD-123456",
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       99.99,
		Status:      models.OrderStatusPending,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Set up expectations
	mockRepo.On("CreateOrder", req).Return(expectedOrder, nil)
	mockProducer.On("PublishOrderCreated", mock.AnythingOfType("*events.OrderCreatedEvent")).Return(nil)

	// Execute
	result, err := service.CreateOrder(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, expectedOrder, result)
	mockRepo.AssertExpectations(t)
	mockProducer.AssertExpectations(t)
}

func TestOrderService_CreateOrder_DatabaseError(t *testing.T) {
	// Setup mocks
	mockRepo := new(MockOrderRepository)
	mockProducer := new(MockProducer)

	// Create service
	service := NewOrderService(mockRepo, mockProducer)

	// Test data
	req := &models.OrderRequest{
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       99.99,
	}

	// Set up expectations
	mockRepo.On("CreateOrder", req).Return((*models.Order)(nil), errors.New("database error"))

	// Execute
	result, err := service.CreateOrder(context.Background(), req)

	// Assertions
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to create order in database")
	mockRepo.AssertExpectations(t)
	// Producer should not be called if database fails
	mockProducer.AssertNotCalled(t, "PublishOrderCreated")
}

func TestOrderService_CreateOrder_EventPublishingError(t *testing.T) {
	// Setup mocks
	mockRepo := new(MockOrderRepository)
	mockProducer := new(MockProducer)

	// Create service
	service := NewOrderService(mockRepo, mockProducer)

	// Test data
	req := &models.OrderRequest{
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       99.99,
	}

	expectedOrder := &models.Order{
		ID:          123,
		OrderNumber: "ORD-123456",
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       99.99,
		Status:      models.OrderStatusPending,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Set up expectations
	mockRepo.On("CreateOrder", req).Return(expectedOrder, nil)
	mockProducer.On("PublishOrderCreated", mock.AnythingOfType("*events.OrderCreatedEvent")).Return(errors.New("kafka error"))

	// Execute
	result, err := service.CreateOrder(context.Background(), req)

	// Assertions - Order creation should still succeed even if event publishing fails
	assert.NoError(t, err)
	assert.Equal(t, expectedOrder, result)
	mockRepo.AssertExpectations(t)
	mockProducer.AssertExpectations(t)
}

func TestOrderService_GetOrderByID_Success(t *testing.T) {
	// Setup mocks
	mockRepo := new(MockOrderRepository)
	mockProducer := new(MockProducer)

	// Create service
	service := NewOrderService(mockRepo, mockProducer)

	// Test data
	orderID := 123
	expectedOrder := &models.Order{
		ID:          orderID,
		OrderNumber: "ORD-123456",
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       99.99,
		Status:      models.OrderStatusPending,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Set up expectations
	mockRepo.On("GetOrderByID", orderID).Return(expectedOrder, nil)

	// Execute
	result, err := service.GetOrderByID(context.Background(), orderID)

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, expectedOrder, result)
	mockRepo.AssertExpectations(t)
}

func TestOrderService_GetOrderByID_Error(t *testing.T) {
	// Setup mocks
	mockRepo := new(MockOrderRepository)
	mockProducer := new(MockProducer)

	// Create service
	service := NewOrderService(mockRepo, mockProducer)

	// Test data
	orderID := 999

	// Set up expectations
	mockRepo.On("GetOrderByID", orderID).Return((*models.Order)(nil), errors.New("order not found"))

	// Execute
	result, err := service.GetOrderByID(context.Background(), orderID)

	// Assertions
	assert.Error(t, err)
	assert.Nil(t, result)
	mockRepo.AssertExpectations(t)
}
