package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestQueryFilters_Validate(t *testing.T) {
	tests := []struct {
		name    string
		filters QueryFilters
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid filters with all parameters",
			filters: QueryFilters{
				DateFrom:       timePtr(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
				DateTo:         timePtr(time.Date(2024, 12, 31, 0, 0, 0, 0, time.UTC)),
				OrderReference: "ORDER-123",
				Status:         "processed",
				Page:           1,
				Limit:          20,
			},
			wantErr: false,
		},
		{
			name: "valid filters with minimal parameters",
			filters: QueryFilters{
				Page:  1,
				Limit: 10,
			},
			wantErr: false,
		},
		{
			name: "invalid page - gets corrected",
			filters: QueryFilters{
				Page:  0,
				Limit: 20,
			},
			wantErr: false,
		},
		{
			name: "invalid limit - gets corrected",
			filters: QueryFilters{
				Page:  1,
				Limit: 0,
			},
			wantErr: false,
		},
		{
			name: "date_from after date_to",
			filters: QueryFilters{
				DateFrom: timePtr(time.Date(2024, 12, 31, 0, 0, 0, 0, time.UTC)),
				DateTo:   timePtr(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
				Page:     1,
				Limit:    20,
			},
			wantErr: true,
			errMsg:  "date_from cannot be after date_to",
		},
		{
			name: "invalid status",
			filters: QueryFilters{
				Status: "invalid_status",
				Page:   1,
				Limit:  20,
			},
			wantErr: true,
			errMsg:  "invalid status value",
		},
		{
			name: "valid status - pending",
			filters: QueryFilters{
				Status: "pending",
				Page:   1,
				Limit:  20,
			},
			wantErr: false,
		},
		{
			name: "valid status - requires_attention",
			filters: QueryFilters{
				Status: "requires_attention",
				Page:   1,
				Limit:  20,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.filters.Validate()

			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
				// Check that defaults are applied
				assert.GreaterOrEqual(t, tt.filters.Page, 1)
				assert.GreaterOrEqual(t, tt.filters.Limit, 1)
				assert.LessOrEqual(t, tt.filters.Limit, 100)
			}
		})
	}
}

func TestFinancialEntry_ToFinancialRecordResponse(t *testing.T) {
	now := time.Now()
	entry := &FinancialEntry{
		ID:              1,
		OrderID:         "ORDER-123",
		TransactionID:   "TXN-123",
		RevenueAmount:   100.00,
		TaxAmount:       10.00,
		Currency:        "MYR",
		PaymentMethod:   "credit_card",
		TransactionType: "sale",
		Description:     "Test transaction",
		CreatedAt:       now,
		UpdatedAt:       now,
	}

	response := entry.ToFinancialRecordResponse()

	assert.Equal(t, entry.ID, response.ID)
	assert.Equal(t, entry.OrderID, response.OrderID)
	assert.Equal(t, entry.TransactionID, response.TransactionID)
	assert.Equal(t, entry.RevenueAmount, response.RevenueAmount)
	assert.Equal(t, entry.TaxAmount, response.TaxAmount)
	assert.Equal(t, 110.00, response.TotalAmount) // Revenue + Tax
	assert.Equal(t, entry.Currency, response.Currency)
	assert.Equal(t, entry.PaymentMethod, response.PaymentMethod)
	assert.Equal(t, entry.TransactionType, response.TransactionType)
	assert.Equal(t, entry.Description, response.Description)
	assert.Equal(t, "pending", response.Status) // Default status
	assert.Nil(t, response.ProcessedAt)
	assert.Empty(t, response.ProcessingNotes)
	assert.Equal(t, entry.CreatedAt, response.CreatedAt)
	assert.Equal(t, entry.UpdatedAt, response.UpdatedAt)
}

func TestPaginationMetadata(t *testing.T) {
	pagination := PaginationMetadata{
		Page:        2,
		Limit:       20,
		Total:       100,
		TotalPages:  5,
		HasNext:     true,
		HasPrevious: true,
	}

	assert.Equal(t, 2, pagination.Page)
	assert.Equal(t, 20, pagination.Limit)
	assert.Equal(t, 100, pagination.Total)
	assert.Equal(t, 5, pagination.TotalPages)
	assert.True(t, pagination.HasNext)
	assert.True(t, pagination.HasPrevious)
}

func TestStatusUpdateRequest(t *testing.T) {
	request := StatusUpdateRequest{
		Status: "processed",
		Notes:  "Successfully processed by external system",
	}

	assert.Equal(t, "processed", request.Status)
	assert.Equal(t, "Successfully processed by external system", request.Notes)
}

func TestStatusUpdateResponse(t *testing.T) {
	now := time.Now()
	response := StatusUpdateResponse{
		ID:              1,
		Status:          "completed",
		ProcessedAt:     now,
		ProcessingNotes: "Processing completed successfully",
		UpdatedAt:       now,
	}

	assert.Equal(t, 1, response.ID)
	assert.Equal(t, "completed", response.Status)
	assert.Equal(t, now, response.ProcessedAt)
	assert.Equal(t, "Processing completed successfully", response.ProcessingNotes)
	assert.Equal(t, now, response.UpdatedAt)
}

func TestErrorResponse(t *testing.T) {
	errorResp := ErrorResponse{
		Error:   "Bad Request",
		Message: "Invalid input parameters",
		Code:    400,
		Details: map[string]string{
			"field": "validation error",
		},
	}

	assert.Equal(t, "Bad Request", errorResp.Error)
	assert.Equal(t, "Invalid input parameters", errorResp.Message)
	assert.Equal(t, 400, errorResp.Code)
	assert.Equal(t, "validation error", errorResp.Details["field"])
}

func TestFinancialRecordsListResponse(t *testing.T) {
	now := time.Now()
	entry := &FinancialEntry{
		ID:              1,
		OrderID:         "ORDER-123",
		TransactionID:   "TXN-123",
		RevenueAmount:   100.00,
		TaxAmount:       10.00,
		Currency:        "MYR",
		PaymentMethod:   "credit_card",
		TransactionType: "sale",
		Description:     "Test transaction",
		CreatedAt:       now,
		UpdatedAt:       now,
	}

	response := FinancialRecordsListResponse{
		Data: []FinancialRecordResponse{
			entry.ToFinancialRecordResponse(),
		},
		Pagination: PaginationMetadata{
			Page:        1,
			Limit:       20,
			Total:       1,
			TotalPages:  1,
			HasNext:     false,
			HasPrevious: false,
		},
	}

	assert.Len(t, response.Data, 1)
	assert.Equal(t, 1, response.Data[0].ID)
	assert.Equal(t, 1, response.Pagination.Page)
	assert.Equal(t, 1, response.Pagination.Total)
}

// Helper function to create time pointer
func timePtr(t time.Time) *time.Time {
	return &t
}

func TestNewAuditTrailEntry(t *testing.T) {
	recordID := 123
	oldStatus := "pending"
	newStatus := "completed"
	notes := "Status updated by admin"
	changedBy := "<EMAIL>"
	ipAddress := "***********"
	userAgent := "Mozilla/5.0"

	entry := NewAuditTrailEntry(recordID, &oldStatus, newStatus, &notes, changedBy, &ipAddress, &userAgent)

	assert.Equal(t, recordID, entry.RecordID)
	assert.Equal(t, &oldStatus, entry.OldStatus)
	assert.Equal(t, newStatus, entry.NewStatus)
	assert.Equal(t, &notes, entry.Notes)
	assert.Equal(t, changedBy, entry.ChangedBy)
	assert.Equal(t, &ipAddress, entry.IPAddress)
	assert.Equal(t, &userAgent, entry.UserAgent)
	assert.WithinDuration(t, time.Now(), entry.ChangedAt, time.Second)
}

func TestAuditTrailEntryValidate(t *testing.T) {
	tests := []struct {
		name    string
		entry   *AuditTrailEntry
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid entry",
			entry: &AuditTrailEntry{
				RecordID:  123,
				NewStatus: "completed",
				ChangedBy: "<EMAIL>",
			},
			wantErr: false,
		},
		{
			name: "invalid record_id - zero",
			entry: &AuditTrailEntry{
				RecordID:  0,
				NewStatus: "completed",
				ChangedBy: "<EMAIL>",
			},
			wantErr: true,
			errMsg:  "record_id must be greater than 0",
		},
		{
			name: "missing new_status",
			entry: &AuditTrailEntry{
				RecordID:  123,
				NewStatus: "",
				ChangedBy: "<EMAIL>",
			},
			wantErr: true,
			errMsg:  "new_status is required",
		},
		{
			name: "missing changed_by",
			entry: &AuditTrailEntry{
				RecordID:  123,
				NewStatus: "completed",
				ChangedBy: "",
			},
			wantErr: true,
			errMsg:  "changed_by is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.entry.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.errMsg, err.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidationError(t *testing.T) {
	err := &ValidationError{
		Field:   "test_field",
		Message: "test message",
	}

	assert.Equal(t, "test message", err.Error())
}
