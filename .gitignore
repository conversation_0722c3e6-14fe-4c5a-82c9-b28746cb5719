# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work.sum

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
.dockerignore

# Kubernetes
*.kubeconfig

# Temporary files
tmp/
temp/

# Build artifacts
dist/
build/
target/

# Database
*.db
*.sqlite
*.sqlite3

# Redis dump file
dump.rdb

# Application specific
config/local.yaml
config/development.yaml
config/production.yaml
!config/example.yaml

# Local development files
.ai/debug-log.md
scripts/dev/local-*.ps1
infra/k8s/dev/local-*.yaml
infra/k8s/dev/*-local.yaml

# Kubernetes local development
*.kubeconfig.local
kustomization.yaml.local

# Docker local development
docker-compose.override.yml
.docker/

# Local certificates and keys
*.pem
*.key
*.crt
!*.example.pem
!*.example.key
!*.example.crt
