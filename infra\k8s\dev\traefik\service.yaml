---
apiVersion: v1
kind: Service
metadata:
  name: traefik
  namespace: cdh-dev
  labels:
    app: traefik
spec:
  type: LoadBalancer
  ports:
    - name: web
      port: 80
      targetPort: web
      protocol: TCP
    - name: websecure
      port: 443
      targetPort: websecure
      protocol: TCP
    - name: admin
      port: 8080
      targetPort: admin
      protocol: TCP
  selector:
    app: traefik
---
apiVersion: v1
kind: Service
metadata:
  name: traefik-dashboard
  namespace: cdh-dev
  labels:
    app: traefik
    component: dashboard
spec:
  type: ClusterIP
  ports:
    - name: dashboard
      port: 8080
      targetPort: admin
      protocol: TCP
  selector:
    app: traefik
