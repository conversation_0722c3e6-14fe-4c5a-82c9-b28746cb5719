package errors

import (
	"database/sql"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestValidationError(t *testing.T) {
	t.Run("Create validation error", func(t *testing.T) {
		err := NewValidationError("test field", "test message")

		assert.Error(t, err)
		assert.Contains(t, err.<PERSON>r(), "validation error")
		assert.Contains(t, err.<PERSON>(), "test field")
		assert.Contains(t, err.<PERSON>(), "test message")
	})

	t.Run("Validation error implements error interface", func(t *testing.T) {
		var err error = NewValidationError("field", "message")
		assert.NotNil(t, err)
	})

	t.Run("Validation error with empty field", func(t *testing.T) {
		err := NewValidationError("", "test message")

		assert.Error(t, err)
		assert.Contains(t, err.<PERSON>r(), "validation error")
		assert.Contains(t, err.<PERSON>(), "test message")
	})

	t.Run("Validation error with empty message", func(t *testing.T) {
		err := NewValidationError("test field", "")

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "validation error")
		assert.Contains(t, err.Error(), "test field")
	})
}

func TestDatabaseError(t *testing.T) {
	t.Run("Create database error with wrapped error", func(t *testing.T) {
		originalErr := sql.ErrNoRows
		err := NewDatabaseError("test operation", originalErr)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database error during test operation")
		assert.Contains(t, err.Error(), originalErr.Error())
	})

	t.Run("Database error implements error interface", func(t *testing.T) {
		var err error = NewDatabaseError("operation", sql.ErrConnDone)
		assert.NotNil(t, err)
	})

	t.Run("Database error with nil wrapped error", func(t *testing.T) {
		err := NewDatabaseError("test operation", nil)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database error during test operation")
	})

	t.Run("Database error unwrapping", func(t *testing.T) {
		originalErr := sql.ErrTxDone
		err := NewDatabaseError("test operation", originalErr)

		unwrapped := errors.Unwrap(err)
		assert.Equal(t, originalErr, unwrapped)
	})
}

func TestKafkaError(t *testing.T) {
	t.Run("Create kafka error with wrapped error", func(t *testing.T) {
		originalErr := errors.New("connection failed")
		err := NewKafkaError("test operation", originalErr)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "kafka error during test operation")
		assert.Contains(t, err.Error(), originalErr.Error())
	})

	t.Run("Kafka error implements error interface", func(t *testing.T) {
		var err error = NewKafkaError("operation", errors.New("test"))
		assert.NotNil(t, err)
	})

	t.Run("Kafka error with nil wrapped error", func(t *testing.T) {
		err := NewKafkaError("test operation", nil)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "kafka error during test operation")
	})

	t.Run("Kafka error unwrapping", func(t *testing.T) {
		originalErr := errors.New("broker unavailable")
		err := NewKafkaError("test operation", originalErr)

		unwrapped := errors.Unwrap(err)
		assert.Equal(t, originalErr, unwrapped)
	})
}

func TestPredefinedErrors(t *testing.T) {
	t.Run("ErrInventoryNotFound", func(t *testing.T) {
		assert.Error(t, ErrInventoryNotFound)
		assert.Contains(t, ErrInventoryNotFound.Error(), "inventory item not found")
	})

	t.Run("ErrInsufficientStock", func(t *testing.T) {
		assert.Error(t, ErrInsufficientStock)
		assert.Contains(t, ErrInsufficientStock.Error(), "insufficient stock")
	})

	t.Run("ErrInvalidQuantity", func(t *testing.T) {
		assert.Error(t, ErrInvalidQuantity)
		assert.Contains(t, ErrInvalidQuantity.Error(), "invalid quantity")
	})
}

func TestErrorTypes(t *testing.T) {
	t.Run("ValidationError type assertion", func(t *testing.T) {
		err := NewValidationError("field", "message")

		var validationErr ValidationError
		assert.True(t, errors.As(err, &validationErr))
		assert.Equal(t, "field", validationErr.Field)
		assert.Equal(t, "message", validationErr.Message)
	})

	t.Run("DatabaseError type assertion", func(t *testing.T) {
		originalErr := sql.ErrNoRows
		err := NewDatabaseError("operation", originalErr)

		var dbErr DatabaseError
		assert.True(t, errors.As(err, &dbErr))
		assert.Equal(t, "operation", dbErr.Operation)
		assert.Equal(t, originalErr, dbErr.Err)
	})

	t.Run("KafkaError type assertion", func(t *testing.T) {
		originalErr := errors.New("test error")
		err := NewKafkaError("operation", originalErr)

		var kafkaErr KafkaError
		assert.True(t, errors.As(err, &kafkaErr))
		assert.Equal(t, "operation", kafkaErr.Operation)
		assert.Equal(t, originalErr, kafkaErr.Err)
	})
}

func TestErrorChaining(t *testing.T) {
	t.Run("Chain validation and database errors", func(t *testing.T) {
		validationErr := NewValidationError("sku", "cannot be empty")
		dbErr := NewDatabaseError("create inventory", validationErr)

		assert.Error(t, dbErr)
		assert.Contains(t, dbErr.Error(), "database error during create inventory")
		assert.Contains(t, dbErr.Error(), "validation error")

		// Should be able to unwrap to get the validation error
		var validationErrUnwrapped ValidationError
		assert.True(t, errors.As(dbErr, &validationErrUnwrapped))
	})

	t.Run("Chain kafka and database errors", func(t *testing.T) {
		dbErr := NewDatabaseError("save order", sql.ErrConnDone)
		kafkaErr := NewKafkaError("process message", dbErr)

		assert.Error(t, kafkaErr)
		assert.Contains(t, kafkaErr.Error(), "kafka error during process message")
		assert.Contains(t, kafkaErr.Error(), "database error during save order")

		// Should be able to unwrap to get the database error
		var dbErrUnwrapped DatabaseError
		assert.True(t, errors.As(kafkaErr, &dbErrUnwrapped))
	})
}
