package middleware

import (
	"context"
	"crypto/subtle"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/company/cdh/apps/finance-service/models"
	"github.com/golang-jwt/jwt/v5"
)

// AuthMiddleware handles authentication and authorization
type AuthMiddleware struct {
	apiKeys    map[string]*models.APIKeyInfo
	jwtSecret  []byte
	rateLimits map[string]*models.RateLimitInfo
	mu         sync.RWMutex // Protects rateLimits map from concurrent access
}

// NewAuthMiddleware creates a new authentication middleware
func NewAuthMiddleware(jwtSecret string) *AuthMiddleware {
	return &AuthMiddleware{
		apiKeys:    make(map[string]*models.APIKeyInfo),
		jwtSecret:  []byte(jwtSecret),
		rateLimits: make(map[string]*models.RateLimitInfo),
	}
}

// AddAPIKey adds an API key to the middleware
func (am *AuthMiddleware) AddAPIKey(apiKey *models.APIKeyInfo) {
	am.apiKeys[apiKey.Key] = apiKey
}

// AuthenticateAPIKey validates API key authentication
func (am *AuthMiddleware) AuthenticateAPIKey(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get API key from header
		apiKey := r.Header.Get("X-API-Key")
		if apiKey == "" {
			am.writeUnauthorizedResponse(w, "Missing API key")
			return
		}

		// Validate API key
		keyInfo, exists := am.apiKeys[apiKey]
		if !exists {
			am.writeUnauthorizedResponse(w, "Invalid API key")
			return
		}

		// Check if API key is active
		if !keyInfo.IsActive {
			am.writeUnauthorizedResponse(w, "API key is inactive")
			return
		}

		// Check if API key has expired
		if keyInfo.ExpiresAt != nil && time.Now().After(*keyInfo.ExpiresAt) {
			am.writeUnauthorizedResponse(w, "API key has expired")
			return
		}

		// Create auth context
		authCtx := &models.AuthContext{
			APIKey:      apiKey,
			ClientID:    keyInfo.ClientID,
			Permissions: keyInfo.Permissions,
			IPAddress:   am.getClientIP(r),
			UserAgent:   r.Header.Get("User-Agent"),
		}

		// Add auth context to request context
		ctx := context.WithValue(r.Context(), "auth", authCtx)
		r = r.WithContext(ctx)

		// Update last used timestamp (in production, this would update the database)
		now := time.Now()
		keyInfo.LastUsedAt = &now

		next(w, r)
	}
}

// AuthenticateJWT validates JWT token authentication
func (am *AuthMiddleware) AuthenticateJWT(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get JWT token from Authorization header
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			am.writeUnauthorizedResponse(w, "Missing authorization header")
			return
		}

		// Check Bearer token format
		if !strings.HasPrefix(authHeader, "Bearer ") {
			am.writeUnauthorizedResponse(w, "Invalid authorization header format")
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")

		// Parse and validate JWT token
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			// Validate signing method
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return am.jwtSecret, nil
		})

		if err != nil {
			am.writeUnauthorizedResponse(w, "Invalid JWT token: "+err.Error())
			return
		}

		if !token.Valid {
			am.writeUnauthorizedResponse(w, "Invalid JWT token")
			return
		}

		// Extract claims
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			am.writeUnauthorizedResponse(w, "Invalid JWT claims")
			return
		}

		// Validate expiration
		if exp, ok := claims["exp"].(float64); ok {
			if time.Now().Unix() > int64(exp) {
				am.writeUnauthorizedResponse(w, "JWT token has expired")
				return
			}
		}

		// Extract user information
		userID, _ := claims["sub"].(string)
		clientID, _ := claims["client_id"].(string)
		permissions := []string{}
		if perms, ok := claims["permissions"].([]interface{}); ok {
			for _, perm := range perms {
				if permStr, ok := perm.(string); ok {
					permissions = append(permissions, permStr)
				}
			}
		}

		// Create auth context
		authCtx := &models.AuthContext{
			UserID:      userID,
			ClientID:    clientID,
			Permissions: permissions,
			ExpiresAt:   time.Unix(int64(claims["exp"].(float64)), 0),
			IPAddress:   am.getClientIP(r),
			UserAgent:   r.Header.Get("User-Agent"),
		}

		// Add auth context to request context
		ctx := context.WithValue(r.Context(), "auth", authCtx)
		r = r.WithContext(ctx)

		next(w, r)
	}
}

// RateLimit implements rate limiting middleware
func (am *AuthMiddleware) RateLimit(maxRequests int, windowSeconds int) func(http.HandlerFunc) http.HandlerFunc {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// Get client identifier (IP address or API key)
			clientKey := am.getClientIP(r)
			if apiKey := r.Header.Get("X-API-Key"); apiKey != "" {
				clientKey = apiKey
			}

			now := time.Now()

			// Thread-safe rate limit check and update
			am.mu.Lock()
			rateLimitInfo, exists := am.rateLimits[clientKey]
			if !exists || now.Sub(rateLimitInfo.WindowStart) > time.Duration(windowSeconds)*time.Second {
				// Create new window
				rateLimitInfo = &models.RateLimitInfo{
					Key:          clientKey,
					RequestCount: 0,
					WindowStart:  now,
					WindowSize:   windowSeconds,
					MaxRequests:  maxRequests,
					ResetTime:    now.Add(time.Duration(windowSeconds) * time.Second),
				}
				am.rateLimits[clientKey] = rateLimitInfo
			}

			// Check rate limit
			if rateLimitInfo.RequestCount >= maxRequests {
				am.mu.Unlock()
				am.writeRateLimitResponse(w, rateLimitInfo)
				return
			}

			// Increment request count
			rateLimitInfo.RequestCount++
			am.mu.Unlock()

			// Add rate limit headers
			w.Header().Set("X-RateLimit-Limit", fmt.Sprintf("%d", maxRequests))
			w.Header().Set("X-RateLimit-Remaining", fmt.Sprintf("%d", maxRequests-rateLimitInfo.RequestCount))
			w.Header().Set("X-RateLimit-Reset", fmt.Sprintf("%d", rateLimitInfo.ResetTime.Unix()))

			next(w, r)
		}
	}
}

// RequirePermission checks if the authenticated user has the required permission
func (am *AuthMiddleware) RequirePermission(permission string) func(http.HandlerFunc) http.HandlerFunc {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// Get auth context
			authCtx, ok := r.Context().Value("auth").(*models.AuthContext)
			if !ok {
				am.writeForbiddenResponse(w, "Authentication required")
				return
			}

			// Check permission
			hasPermission := false
			for _, perm := range authCtx.Permissions {
				if perm == permission || perm == "*" {
					hasPermission = true
					break
				}
			}

			if !hasPermission {
				am.writeForbiddenResponse(w, fmt.Sprintf("Permission required: %s", permission))
				return
			}

			next(w, r)
		}
	}
}

// CORS middleware for handling Cross-Origin Resource Sharing
func (am *AuthMiddleware) CORS(allowedOrigins []string, allowedMethods []string, allowedHeaders []string) func(http.HandlerFunc) http.HandlerFunc {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			origin := r.Header.Get("Origin")

			// Check if origin is allowed
			originAllowed := false
			for _, allowedOrigin := range allowedOrigins {
				if allowedOrigin == "*" || allowedOrigin == origin {
					originAllowed = true
					break
				}
			}

			if originAllowed {
				w.Header().Set("Access-Control-Allow-Origin", origin)
			}

			w.Header().Set("Access-Control-Allow-Methods", strings.Join(allowedMethods, ", "))
			w.Header().Set("Access-Control-Allow-Headers", strings.Join(allowedHeaders, ", "))
			w.Header().Set("Access-Control-Max-Age", "86400") // 24 hours

			// Handle preflight requests
			if r.Method == http.MethodOptions {
				w.WriteHeader(http.StatusOK)
				return
			}

			next(w, r)
		}
	}
}

// Helper methods

func (am *AuthMiddleware) getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header first
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		ips := strings.Split(xff, ",")
		return strings.TrimSpace(ips[0])
	}

	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}

	// Fall back to RemoteAddr
	ip := r.RemoteAddr
	if colon := strings.LastIndex(ip, ":"); colon != -1 {
		ip = ip[:colon]
	}
	return ip
}

func (am *AuthMiddleware) writeUnauthorizedResponse(w http.ResponseWriter, message string) {
	errorResponse := models.ErrorResponse{
		Error:   "Unauthorized",
		Message: message,
		Code:    http.StatusUnauthorized,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusUnauthorized)
	json.NewEncoder(w).Encode(errorResponse)
}

func (am *AuthMiddleware) writeForbiddenResponse(w http.ResponseWriter, message string) {
	errorResponse := models.ErrorResponse{
		Error:   "Forbidden",
		Message: message,
		Code:    http.StatusForbidden,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusForbidden)
	json.NewEncoder(w).Encode(errorResponse)
}

func (am *AuthMiddleware) writeRateLimitResponse(w http.ResponseWriter, rateLimitInfo *models.RateLimitInfo) {
	errorResponse := models.ErrorResponse{
		Error:   "Too Many Requests",
		Message: "Rate limit exceeded",
		Code:    http.StatusTooManyRequests,
		Details: map[string]string{
			"limit":    fmt.Sprintf("%d", rateLimitInfo.MaxRequests),
			"window":   fmt.Sprintf("%d seconds", rateLimitInfo.WindowSize),
			"reset_at": rateLimitInfo.ResetTime.Format(time.RFC3339),
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Retry-After", fmt.Sprintf("%d", int(time.Until(rateLimitInfo.ResetTime).Seconds())))
	w.WriteHeader(http.StatusTooManyRequests)
	json.NewEncoder(w).Encode(errorResponse)
}

// SecureCompare performs a constant-time comparison of two strings
func SecureCompare(a, b string) bool {
	return subtle.ConstantTimeCompare([]byte(a), []byte(b)) == 1
}
