# Finance Service

A microservice for handling financial transactions and publishing standardized financial events to external accounting systems via Kafka.

## 🚀 Features

### Core Functionality
- **Order Event Processing**: Process order events and create financial entries
- **Tax Calculations**: Automatic 6% SST calculation for Malaysian transactions
- **Financial Event Publishing**: Real-time event publishing to Kafka for external systems
- **Audit Trail**: Complete transaction tracking with processing status
- **Idempotent Processing**: Duplicate event detection and handling

### Event Publishing
- **Standardized Schema**: JSON schema for external system compatibility
- **Kafka Integration**: Reliable event streaming with retry logic
- **Metadata Enrichment**: Additional context for external systems
- **Schema Validation**: Comprehensive event validation before publishing

### Reliability & Performance
- **Error Handling**: Comprehensive error handling with retry mechanisms
- **Connection Pooling**: Optimized database connection management
- **Monitoring**: Prometheus metrics and health checks
- **Testing**: 100% test coverage with unit and integration tests

## 🏗️ Technology Stack

- **Language**: Go 1.21+
- **Database**: PostgreSQL 13+
- **Message Queue**: Apache Kafka 3.0+
- **Testing**: Testify framework with comprehensive test suites
- **Monitoring**: Prometheus metrics, Grafana dashboards
- **Architecture**: Clean Architecture with dependency injection

## 📋 Prerequisites

- Go 1.21 or higher
- PostgreSQL 13 or higher
- Apache Kafka 3.0 or higher (for event publishing)
- Docker & Docker Compose (for local development)

## 🚀 Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd apps/finance-service
go mod download
```

### 2. Start Dependencies (Docker)

```bash
# Start PostgreSQL and Kafka
docker-compose -f docker-compose.dev.yml up -d

# Create Kafka topic
docker exec kafka kafka-topics --create \
  --topic financial.records.created \
  --bootstrap-server localhost:9092 \
  --partitions 3 \
  --replication-factor 1
```

### 3. Configure Environment

```bash
# Copy example environment file
cp .env.example .env

# Edit configuration as needed
export DB_HOST=localhost
export DB_PORT=5435
export DB_NAME=finance_db
export KAFKA_BROKERS=localhost:9092
```

### 4. Run Database Migrations

```bash
go run cmd/migrate/main.go up
```

### 5. Start the Service

```bash
go run cmd/server/main.go
```

The service will start on port 8080 with metrics on 8081 and health checks on 8082.

## 🧪 Testing

### Run All Tests
```bash
# Unit tests
go test ./models ./internal/...

# Integration tests
go test ./tests

# All tests with coverage
go test -cover ./...
```

### Test Categories
- **Unit Tests**: 47 tests covering models, services, and producers
- **Integration Tests**: 8 tests covering end-to-end workflows
- **Scenario Tests**: Various payment methods, tax calculations, and edge cases

## 📊 Monitoring

### Health Checks
- **Liveness**: `GET /health` - Service availability
- **Readiness**: `GET /health/ready` - Service readiness (DB + Kafka)

### Metrics (Prometheus)
- **Event Publishing Rate**: `finance_service_events_published_total`
- **Publishing Errors**: `finance_service_events_failed_total`
- **Kafka Latency**: `finance_service_kafka_publish_duration_seconds`
- **Database Connections**: `finance_service_db_connections_*`

### Grafana Dashboards
Pre-configured dashboards available in `docs/monitoring/grafana/`

## 📖 Documentation

### API Documentation
- **[Financial Events API](docs/api/financial-events-api.md)**: Complete API reference for external systems
- **[Event Schema](docs/api/financial-events-api.md#event-schema)**: JSON schema and field descriptions

### Configuration
- **[Kafka Setup](docs/config/kafka-setup.md)**: Kafka configuration and setup guide
- **[Environment Configuration](docs/config/kafka-setup.md#environment-configuration)**: Development and production configs

### Deployment
- **[Deployment Guide](docs/deployment/deployment-guide.md)**: Kubernetes deployment and operations
- **[Monitoring Setup](docs/deployment/deployment-guide.md#monitoring-and-observability)**: Prometheus and Grafana configuration

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | Database host | `localhost` |
| `DB_PORT` | Database port | `5435` |
| `DB_NAME` | Database name | `finance_db` |
| `KAFKA_BROKERS` | Kafka broker addresses | `localhost:9092` |
| `KAFKA_TOPIC` | Financial events topic | `financial.records.created` |
| `LOG_LEVEL` | Logging level | `info` |

### Configuration Files
- **Development**: `config/development.yaml`
- **Production**: `config/production.yaml`

## 🏛️ Architecture

### Clean Architecture Layers
```
├── cmd/                    # Application entry points
├── internal/
│   ├── config/            # Configuration management
│   ├── producer/          # Kafka event producers
│   ├── repository/        # Data access layer
│   └── services/          # Business logic layer
├── models/                # Domain models and validation
├── tests/                 # Integration and scenario tests
└── docs/                  # Documentation
```

### Event Flow
1. **Order Event** → Finance Service
2. **Financial Processing** → Create financial entry
3. **Event Publishing** → Kafka topic
4. **External Systems** → Consume financial events

## 🔄 Event Schema

### Financial Record Event
```json
{
  "transaction_id": "TXN-order-123-1754150000",
  "order_reference": "order-123",
  "revenue_amount": 100.00,
  "tax_amount": 6.00,
  "currency": "MYR",
  "timestamp": "2025-08-02T15:30:00Z",
  "metadata": {
    "source_service": "finance-service",
    "event_version": "1.0",
    "transaction_type": "sale",
    "payment_method": "credit_card",
    "description": "Order ORD-123: Premium Product"
  }
}
```

## 🚀 Deployment

### Docker
```bash
# Build image
docker build -t finance-service:latest .

# Run container
docker run -p 8080:8080 \
  -e DB_HOST=postgres \
  -e KAFKA_BROKERS=kafka:9092 \
  finance-service:latest
```

### Kubernetes
```bash
# Deploy to Kubernetes
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -l app=finance-service
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Ensure all tests pass: `go test ./...`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Submit a pull request

### Development Guidelines
- Follow Go best practices and conventions
- Add tests for new functionality
- Update documentation for API changes
- Ensure all tests pass before submitting PR

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

- **Documentation**: [docs/](docs/)
- **Issues**: GitHub Issues
- **Email**: <EMAIL>
- **Slack**: #finance-service-support

---

**Finance Service** - Part of the Customer Data Hub (CDH) ecosystem
*Last updated: 2025-08-02*
