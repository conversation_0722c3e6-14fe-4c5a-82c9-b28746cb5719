package handlers

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"github.com/company/cdh/apps/inventory-service/internal/services"
)

// InventoryQueryResponse represents the response for inventory queries
type InventoryQueryResponse struct {
	Items []InventoryItem `json:"items"`
	Count int             `json:"count"`
}

// InventoryItem represents an inventory item in the response
type InventoryItem struct {
	SKU               string  `json:"sku"`
	ProductName       string  `json:"product_name"`
	StockQuantity     int     `json:"stock_quantity"`
	ReservedQuantity  int     `json:"reserved_quantity"`
	AvailableQuantity int     `json:"available_quantity"`
	UnitPrice         float64 `json:"unit_price"`
}

// InventoryHandler handles inventory-related HTTP requests
type InventoryHandler struct {
	service services.InventoryService
}

// NewInventoryHandler creates a new inventory handler
func NewInventoryHandler(service services.InventoryService) *InventoryHandler {
	return &InventoryHandler{
		service: service,
	}
}

// GetInventory handles GET /inventory requests
func (h *InventoryHandler) GetInventory(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse SKU parameter
	skuParam := r.URL.Query().Get("sku")
	if skuParam == "" {
		http.Error(w, "SKU parameter is required", http.StatusBadRequest)
		return
	}

	// Split comma-separated SKUs and trim whitespace
	skuList := strings.Split(skuParam, ",")
	var skus []string
	for _, sku := range skuList {
		trimmed := strings.TrimSpace(sku)
		if trimmed != "" {
			skus = append(skus, trimmed)
		}
	}

	if len(skus) == 0 {
		http.Error(w, "At least one valid SKU is required", http.StatusBadRequest)
		return
	}

	// Validate SKU count (prevent abuse)
	if len(skus) > 100 {
		http.Error(w, "Too many SKUs requested (max 100)", http.StatusBadRequest)
		return
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
	defer cancel()

	// Query inventory items
	inventories, err := h.service.GetInventoryBySKUs(ctx, skus)
	if err != nil {
		http.Error(w, "Failed to query inventory", http.StatusInternalServerError)
		return
	}

	// Convert to response format
	items := make([]InventoryItem, len(inventories))
	for i, inv := range inventories {
		items[i] = InventoryItem{
			SKU:               inv.SKU,
			ProductName:       inv.ProductName,
			StockQuantity:     inv.StockQuantity,
			ReservedQuantity:  inv.ReservedQuantity,
			AvailableQuantity: inv.AvailableQuantity,
			UnitPrice:         inv.UnitPrice,
		}
	}

	response := InventoryQueryResponse{
		Items: items,
		Count: len(items),
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}
