package repository

import (
	"github.com/company/cdh/apps/finance-service/models"
)

// FinanceRepository defines the interface for financial data operations
type FinanceRepository interface {
	// Financial Entry operations
	CreateFinancialEntry(entry *models.FinancialEntry) error
	GetFinancialEntryByTransactionID(transactionID string) (*models.FinancialEntry, error)
	GetFinancialEntriesByOrderID(orderID string) ([]*models.FinancialEntry, error)

	// Financial Transaction operations
	CreateFinancialTransaction(transaction *models.FinancialTransaction) error
	GetFinancialTransactionByEventID(eventID string) (*models.FinancialTransaction, error)
	UpdateFinancialTransaction(transaction *models.FinancialTransaction) error

	// API operations for external platforms
	GetFinancialRecords(filters *models.QueryFilters) ([]*models.FinancialEntry, int, error)
	GetFinancialRecordByID(id int) (*models.FinancialEntry, error)
	UpdateFinancialRecordStatus(id int, status string, notes string) error

	// Audit trail operations
	CreateAuditTrailEntry(entry *models.AuditTrailEntry) error
	GetAuditTrailByRecordID(recordID int) ([]*models.AuditTrailEntry, error)
}
