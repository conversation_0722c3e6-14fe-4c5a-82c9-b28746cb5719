# CDH Development Environment Cleanup Script
# This script cleans up development resources

param(
    [switch]$All,
    [switch]$Deployments,
    [switch]$Services,
    [switch]$Pods,
    [string]$ResourceName
)

Write-Host "CDH Development Environment Cleanup" -ForegroundColor Yellow
Write-Host "===================================" -ForegroundColor Yellow

# Ensure we're in the correct namespace
$currentNamespace = kubectl config view --minify --output 'jsonpath={..namespace}' 2>$null
if ($currentNamespace -ne "cdh-dev") {
    Write-Host "WARNING: Current namespace is '$currentNamespace', switching to 'cdh-dev'" -ForegroundColor Yellow
    kubectl config set-context --current --namespace=cdh-dev
}

if ($ResourceName) {
    Write-Host "Cleaning up specific resource: $ResourceName" -ForegroundColor Cyan
    kubectl delete all -l app=$ResourceName
    Write-Host "✓ Cleaned up resources for: $ResourceName" -ForegroundColor Green
}
elseif ($All) {
    Write-Host "Cleaning up ALL resources in cdh-dev namespace..." -ForegroundColor Red
    Write-Host "This will delete all deployments, services, pods, etc." -ForegroundColor Red
    
    $confirmation = Read-Host "Are you sure? (y/N)"
    if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
        kubectl delete all --all -n cdh-dev
        Write-Host "✓ All resources cleaned up" -ForegroundColor Green
    } else {
        Write-Host "Cleanup cancelled" -ForegroundColor Yellow
    }
}
elseif ($Deployments) {
    Write-Host "Cleaning up deployments..." -ForegroundColor Cyan
    kubectl delete deployments --all
    Write-Host "✓ All deployments deleted" -ForegroundColor Green
}
elseif ($Services) {
    Write-Host "Cleaning up services..." -ForegroundColor Cyan
    kubectl delete services --all
    Write-Host "✓ All services deleted" -ForegroundColor Green
}
elseif ($Pods) {
    Write-Host "Cleaning up pods..." -ForegroundColor Cyan
    kubectl delete pods --all
    Write-Host "✓ All pods deleted" -ForegroundColor Green
}
else {
    Write-Host "Usage:" -ForegroundColor Green
    Write-Host "  .\cleanup-env.ps1 -All                    # Delete all resources"
    Write-Host "  .\cleanup-env.ps1 -Deployments            # Delete all deployments"
    Write-Host "  .\cleanup-env.ps1 -Services               # Delete all services"
    Write-Host "  .\cleanup-env.ps1 -Pods                   # Delete all pods"
    Write-Host "  .\cleanup-env.ps1 -ResourceName <name>    # Delete specific app resources"
    Write-Host ""
    Write-Host "Current resources in cdh-dev namespace:" -ForegroundColor Cyan
    kubectl get all
}
