# 5. Source Code Repository Structure (Monorepo)
```plaintext
/
├── apps/
│   ├── admin-ui/        # Admin backend frontend defined in Epic 4
│   ├── finance-service/ # Finance & Tax Service (Go)
│   ├── inventory-service/ # Inventory Service (Go)
│   ├── order-service/   # Order Service (Go)
│   └── user-service/    # User & Permissions Service (Go)
├── infra/
│   └── k8s/             # Kubernetes deployment configuration files (YAML)
├── packages/
│   └── shared/          # Shared Go code/type definitions
├── .github/
│   └── workflows/       # CI/CD pipeline definitions
├── go.work              # Go Workspace configuration file
└── README.md            # Project README
```

