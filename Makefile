# CDH Platform Makefile

.PHONY: help build test clean lint fmt vet deps docker-build docker-run

# Default target
help:
	@echo "Available targets:"
	@echo "  build        - Build all services"
	@echo "  test         - Run all tests"
	@echo "  clean        - Clean build artifacts"
	@echo "  lint         - Run linter"
	@echo "  fmt          - Format code"
	@echo "  vet          - Run go vet"
	@echo "  deps         - Download dependencies"
	@echo "  docker-build - Build Docker images"
	@echo "  docker-run   - Run services with Docker Compose"

# Build all services
build:
	@echo "Building CDH platform..."
	go build -v ./...

# Run all tests
test:
	@echo "Running tests..."
	go test -v ./...

# Run tests with coverage
test-coverage:
	@echo "Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	go clean ./...
	rm -f coverage.out coverage.html

# Run linter (requires golangci-lint)
lint:
	@echo "Running linter..."
	golangci-lint run

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Run go vet
vet:
	@echo "Running go vet..."
	go vet ./...

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	go mod download
	go mod tidy

# Build Docker images (placeholder for future services)
docker-build:
	@echo "Docker build targets will be added as services are developed"

# Run with Docker Compose (placeholder)
docker-run:
	@echo "Docker Compose configuration will be added as services are developed"

# Development setup
dev-setup: deps
	@echo "Setting up development environment..."
	go work sync
