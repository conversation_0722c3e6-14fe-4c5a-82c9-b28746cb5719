package main

import (
	"log"
	"net/http"

	"github.com/company/cdh/apps/order-service/database"
	"github.com/company/cdh/apps/order-service/events"
	"github.com/company/cdh/apps/order-service/handlers"
	"github.com/company/cdh/apps/order-service/internal/config"
	"github.com/company/cdh/apps/order-service/services"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database connection
	if err := database.Connect(&cfg.Database); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer database.Close()

	// Run database migrations
	if err := database.RunMigrations(database.DB); err != nil {
		log.Fatalf("Failed to run migrations: %v", err)
	}

	// Initialize Kafka producer
	producer, err := events.NewKafkaProducer(&cfg.Kafka)
	if err != nil {
		log.Fatalf("Failed to create Kafka producer: %v", err)
	}
	defer producer.Close()

	// Initialize repository and service
	repo := database.NewOrderRepository(database.DB)
	orderService := services.NewOrderService(repo, producer)

	// Initialize handlers
	orderHandler := handlers.NewOrderHandler(orderService)

	// Set up HTTP routes
	mux := http.NewServeMux()

	// Health check endpoint
	mux.HandleFunc("/health", handlers.HealthHandler)

	// Order endpoints
	mux.HandleFunc("/orders", orderHandler.OrdersHandler)
	mux.HandleFunc("/orders/", orderHandler.OrderByIDHandler)

	log.Printf("Order Service starting on port %s", cfg.Server.Port)
	log.Fatal(http.ListenAndServe(":"+cfg.Server.Port, mux))
}
