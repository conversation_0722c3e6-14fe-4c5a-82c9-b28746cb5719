# Central Data Hub (CDH) Platform

## Overview

The Central Data Hub (CDH) is a microservices-based platform designed to serve as the core backend for POS, WMS, and e-commerce systems. It provides unified data management and business processing capabilities while ensuring compliance with the LHDN MyInvois e-invoicing system.

## Goals

- **Compliance**: Achieve 100% automated compliance with the LHDN MyInvois system for e-invoicing
- **Efficiency**: Automate the order-to-finance reconciliation process to reduce manual work
- **Data Accuracy**: Consolidate omni-channel inventory data to eliminate data silos
- **System Performance**: Provide a high-performance, highly available unified API entry point
- **Scalability**: Build a modular microservices foundation for future feature additions

## Technology Stack

| Category | Technology | Purpose |
|----------|------------|---------|
| **Language** | Go | High performance, excellent concurrency model |
| **Database** | PostgreSQL | Reliable relational database, independent per service |
| **Cache** | Redis | Hot data caching for improved response times |
| **Message Queue** | Kafka | Event streaming and asynchronous communication |
| **API Gateway** | Traefik | Cloud-native routing and service discovery |
| **Containerization** | Docker | Standardized application packaging |
| **Orchestration** | Kubernetes | Automated deployment and scaling |
| **Monitoring** | Prometheus, Grafana | Performance metrics and visualization |
| **Logging** | EFK Stack | Centralized log management |

## Project Structure

This project follows a **Monorepo** pattern with the following structure:

```
/
├── apps/                    # Individual microservices
│   ├── user-service/        # User & Permissions Service (Go)
│   ├── order-service/       # Order Service (Go)
│   ├── inventory-service/   # Inventory Service (Go)
│   ├── finance-service/     # Finance & Tax Service (Go)
│   └── admin-ui/           # Admin backend frontend (Future)
├── packages/               # Shared libraries and utilities
│   └── shared/            # Shared Go code/type definitions
├── infra/                 # Infrastructure configuration
│   └── k8s/              # Kubernetes deployment files
├── scripts/              # Build and deployment scripts
├── configs/              # Configuration files
├── docs/                 # Documentation
├── .github/              # CI/CD pipeline definitions
│   └── workflows/
├── go.work              # Go Workspace configuration
└── README.md           # This file
```

## Getting Started

### Prerequisites

- Go 1.21 or later
- Docker and Docker Compose
- Kubernetes cluster (for deployment)
- Git

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd CDH
   ```

2. **Initialize Go workspace**
   ```bash
   go work init
   ```

3. **Install dependencies**
   ```bash
   go mod download
   ```

4. **Run tests**
   ```bash
   make test
   ```

### Development Guidelines

- Each microservice is developed independently in the `apps/` directory
- Shared code and utilities go in the `packages/` directory
- Follow Go best practices and coding standards
- Write comprehensive tests for all functionality using the testutils package
- Use Docker for local development and testing
- Follow the established Git workflow and branching strategy

### Testing Conventions

- Unit tests should be placed alongside source files with `_test.go` suffix
- Use the `packages/testutils` package for common testing utilities
- Integration tests should be in separate `integration_test.go` files
- Aim for high test coverage (>80%) for all services
- Run tests with `make test` or `make test-coverage` for coverage reports

## Architecture

The CDH platform uses an event-driven microservices architecture:

- **API Gateway (Traefik)**: Single entry point for all external requests
- **User Service**: Authentication, authorization, and user management
- **Order Service**: Order processing and management
- **Inventory Service**: Real-time inventory tracking and management
- **Finance Service**: Financial processing and LHDN MyInvois integration

Services communicate asynchronously via Kafka for loose coupling and high reliability.

## Development Workflow

1. Stories are created and approved by the Scrum Master
2. Developers implement stories following the defined tasks
3. All code must pass tests and code review
4. Deployment is automated via CI/CD pipeline
5. Monitoring and logging ensure system health

## Contributing

1. Follow the established coding standards
2. Write tests for all new functionality
3. Update documentation as needed
4. Submit pull requests for review
5. Ensure all CI/CD checks pass

## CI/CD Pipeline

The project uses GitHub Actions for continuous integration and deployment. The pipeline automatically:

1. **Tests**: Runs all Go tests and linting on every push/PR
2. **Builds**: Creates optimized Docker images using multi-stage builds
3. **Publishes**: Pushes images to GitHub Container Registry (ghcr.io)
4. **Deploys**: Ready for deployment to Kubernetes clusters

### Pipeline Workflow

```yaml
# Triggered on: push to main, pull requests
1. Test Job:
   - Set up Go 1.21
   - Run go test ./...
   - Run golangci-lint

2. Build & Push Job:
   - Build Docker image
   - Push to ghcr.io/[repository]/hello-world
   - Tag with branch name and latest
```

### Local Development Deployment

After the CI pipeline builds and pushes images, deploy to your local Kubernetes cluster:

```bash
# Deploy to local cluster
kubectl apply -f infra/k8s/dev/hello-world/

# Access the application
kubectl port-forward service/hello-world-service 8080:8080

# Test endpoints
curl http://localhost:8080/health
curl http://localhost:8080/
```

### Troubleshooting

**Common Issues:**

1. **Image Pull Errors**: Ensure you're logged into ghcr.io
   ```bash
   docker login ghcr.io -u [username]
   ```

2. **Port Forward Fails**: Check if service is running
   ```bash
   kubectl get pods -l app=hello-world
   kubectl logs -l app=hello-world
   ```

3. **Tests Failing**: Run tests locally first
   ```bash
   go test ./apps/hello-world/... -v
   ```

## License

[License information to be added]

## Contact

[Contact information to be added]
