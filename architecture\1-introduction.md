# 1. Introduction
This document outlines the overall project architecture for the Central Data Hub (CDH). It is a microservices system based on Go, following cloud-native principles. Its primary goal is to serve as the core backend for future POS, WMS, and e-commerce systems, providing unified data management and business processing capabilities, and ensuring compliance with the LHDN MyInvois system.

### Change Log
| Date       | Version | Description           | Author             |
| :--------- | :------ | :-------------------- | :----------------- |
| 2025-08-01 | 1.0     | Initial Architecture Design | <PERSON>, Architect |

