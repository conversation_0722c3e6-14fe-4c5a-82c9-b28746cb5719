package database

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/company/cdh/apps/order-service/internal/config"
)

func TestConnect(t *testing.T) {
	t.Run("Invalid configuration", func(t *testing.T) {
		cfg := &config.DatabaseConfig{
			Host:     "invalid-host-that-does-not-exist",
			Port:     "5432",
			User:     "invalid_user",
			Password: "invalid_pass",
			DBName:   "invalid_db",
			SSLMode:  "disable",
		}

		err := Connect(cfg)

		// Should return an error for invalid configuration
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to ping database")
	})

	t.Run("Empty configuration", func(t *testing.T) {
		cfg := &config.DatabaseConfig{}

		err := Connect(cfg)

		// Should return an error for empty configuration
		assert.Error(t, err)
		// Could be either "failed to open database" or "failed to ping database" depending on the driver
		assert.True(t,
			strings.Contains(err.<PERSON><PERSON><PERSON>(), "failed to open database") ||
				strings.Contains(err.<PERSON>rror(), "failed to ping database"),
			"Expected error to contain 'failed to open database' or 'failed to ping database', got: %s", err.Error())
	})
}

func TestClose(t *testing.T) {
	t.Run("Close with nil DB", func(t *testing.T) {
		// Ensure DB is nil
		originalDB := DB
		DB = nil
		defer func() { DB = originalDB }()

		err := Close()
		assert.NoError(t, err)
	})

	t.Run("Close with valid DB", func(t *testing.T) {
		// This test would require a real database connection
		// In a real environment, you would use testcontainers or similar
		// For now, we'll test the nil case above

		// Note: Integration tests would be needed for full database testing
		// Example integration test structure:
		//
		// if testing.Short() {
		//     t.Skip("Skipping integration test")
		// }
		//
		// cfg := &config.DatabaseConfig{
		//     Host:     "localhost",
		//     Port:     "5433",
		//     User:     "test_user",
		//     Password: "test_pass",
		//     DBName:   "test_db",
		//     SSLMode:  "disable",
		// }
		//
		// err := Connect(cfg)
		// require.NoError(t, err)
		//
		// err = Close()
		// assert.NoError(t, err)
	})
}

// Note: For integration tests with a real database, you would need:
// 1. A test database instance (e.g., using testcontainers)
// 2. Test migrations
// 3. Cleanup procedures
//
// Example integration test structure:
//
// func TestConnect_Integration(t *testing.T) {
//     if testing.Short() {
//         t.Skip("Skipping integration test")
//     }
//
//     // Setup test database container
//     cfg := &config.DatabaseConfig{
//         Host:     "localhost",
//         Port:     "5433",
//         User:     "test_user",
//         Password: "test_pass",
//         DBName:   "test_db",
//         SSLMode:  "disable",
//     }
//
//     err := Connect(cfg)
//     require.NoError(t, err)
//     defer Close()
//
//     // Test connection
//     err = DB.Ping()
//     assert.NoError(t, err)
//
//     // Test connection pool settings
//     stats := DB.Stats()
//     assert.Equal(t, 25, stats.MaxOpenConnections)
// }
