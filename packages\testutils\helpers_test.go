package testutils

import (
	"testing"
)

func TestTestHelper_AssertNoError(t *testing.T) {
	helper := NewTestHelper(t)

	// Test with no error
	helper.AssertNoError(nil)

	// Note: Testing error cases would cause test failure,
	// so we only test the happy path in unit tests
}

func TestTestHelper_AssertEqual(t *testing.T) {
	helper := NewTestHelper(t)

	// Test equal values
	helper.AssertEqual(1, 1)
	helper.AssertEqual("test", "test")
	helper.AssertEqual(true, true)
}

func TestTestHelper_AssertNotNil(t *testing.T) {
	helper := NewTestHelper(t)

	// Test non-nil values
	helper.AssertNotNil("not nil")
	helper.AssertNotNil([]int{1, 2, 3})
	helper.AssertNotNil(map[string]int{"key": 1})
}

func TestNewTestHelper(t *testing.T) {
	helper := NewTestHelper(t)

	if helper == nil {
		t.Error("NewTestHelper should not return nil")
	}

	if helper.t != t {
		t.Error("TestHelper should store the testing.T instance")
	}
}

// Example of how to use TestHelper in actual tests
func TestTestHelper_UsageExample(t *testing.T) {
	helper := NewTestHelper(t)

	// Example function that might return an error
	err := someFunction()
	helper.AssertNoError(err, "someFunction should not return an error")

	// Example comparison
	result := calculateSomething()
	expected := 42
	helper.AssertEqual(expected, result, "calculation should return expected value")
}

// Mock functions for example
func someFunction() error {
	return nil
}

func calculateSomething() int {
	return 42
}
