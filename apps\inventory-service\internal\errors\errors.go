package errors

import "fmt"

// Common inventory service errors
var (
	ErrInventoryNotFound    = fmt.Errorf("inventory item not found")
	ErrInsufficientStock    = fmt.Errorf("insufficient stock")
	ErrInvalidSKU          = fmt.<PERSON><PERSON><PERSON>("invalid SKU")
	ErrInvalidQuantity     = fmt.Errorf("invalid quantity")
	ErrDatabaseOperation   = fmt.Errorf("database operation failed")
	ErrKafkaConsumer       = fmt.E<PERSON><PERSON>("kafka consumer error")
)

// ValidationError represents a validation error
type ValidationError struct {
	Field   string
	Message string
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error for field '%s': %s", e.Field, e.Message)
}

// NewValidationError creates a new validation error
func NewValidationError(field, message string) ValidationError {
	return ValidationError{
		Field:   field,
		Message: message,
	}
}

// DatabaseError wraps database-related errors
type DatabaseError struct {
	Operation string
	Err       error
}

func (e DatabaseError) Error() string {
	return fmt.Sprintf("database error during %s: %v", e.Operation, e.Err)
}

func (e DatabaseError) Unwrap() error {
	return e.Err
}

// NewDatabaseError creates a new database error
func NewDatabaseError(operation string, err error) DatabaseError {
	return DatabaseError{
		Operation: operation,
		Err:       err,
	}
}

// KafkaError wraps Kafka-related errors
type KafkaError struct {
	Operation string
	Err       error
}

func (e KafkaError) Error() string {
	return fmt.Sprintf("kafka error during %s: %v", e.Operation, e.Err)
}

func (e KafkaError) Unwrap() error {
	return e.Err
}

// NewKafkaError creates a new Kafka error
func NewKafkaError(operation string, err error) KafkaError {
	return KafkaError{
		Operation: operation,
		Err:       err,
	}
}