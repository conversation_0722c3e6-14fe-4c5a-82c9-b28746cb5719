# CDH Infrastructure

This directory contains infrastructure configuration and deployment manifests for the CDH (Customer Data Hub) project.

## Directory Structure

```
infra/
├── k8s/                    # Kubernetes manifests
│   └── dev/               # Development environment
│       ├── namespace-config.yaml      # Namespace, quotas, limits
│       ├── deployment-template.yaml   # Service deployment template
│       ├── configmap-template.yaml    # Configuration template
│       └── test-deployment.yaml       # Test deployment (temporary)
└── README.md              # This file
```

## Development Environment (k8s/dev/)

The development environment is configured for local development using Docker Desktop with Kubernetes.

### Core Configuration

- **Namespace**: `cdh-dev` - Isolated environment for development
- **Resource Quotas**: Configured limits for CPU, memory, and object counts
- **Default Limits**: Sensible defaults for container resource requests/limits

### Templates

Use the provided templates to create new service deployments:

1. **deployment-template.yaml**: Complete deployment with service and optional PVC
2. **configmap-template.yaml**: Configuration and secrets management

### Usage

1. **Setup Environment**:
   ```powershell
   # Run setup script
   .\scripts\dev\setup-env.ps1
   
   # Or manually apply
   kubectl apply -f infra/k8s/dev/namespace-config.yaml
   kubectl config set-context --current --namespace=cdh-dev
   ```

2. **Deploy a Service**:
   ```powershell
   # Copy template and customize
   cp infra/k8s/dev/deployment-template.yaml infra/k8s/dev/my-service.yaml
   
   # Edit the file to replace placeholders:
   # <SERVICE_NAME>, <IMAGE_NAME>, <PORT>, etc.
   
   # Apply the manifest
   kubectl apply -f infra/k8s/dev/my-service.yaml
   ```

3. **Cleanup**:
   ```powershell
   # Use cleanup script
   .\scripts\dev\cleanup-env.ps1 -All
   
   # Or manually delete
   kubectl delete all --all -n cdh-dev
   ```

### Template Placeholders

When using templates, replace these placeholders:

- `<SERVICE_NAME>`: Name of your service (e.g., user-api, order-service)
- `<COMPONENT_TYPE>`: Type of component (api, worker, frontend, database)
- `<IMAGE_NAME>`: Docker image name
- `<TAG>`: Image tag (usually latest for dev)
- `<PORT>`: Service port number
- `<BASE64_ENCODED_*>`: Base64 encoded secrets

### Best Practices

1. **Naming Convention**: Use kebab-case for resource names
2. **Labels**: Always include app, component, environment, and project labels
3. **Resources**: Set appropriate requests and limits for all containers
4. **Health Checks**: Include liveness and readiness probes
5. **Configuration**: Use ConfigMaps for configuration, Secrets for sensitive data
6. **Cleanup**: Remove test resources after validation

## Future Environments

Additional environments will be added as the project grows:

- `k8s/staging/` - Staging environment configuration
- `k8s/production/` - Production environment configuration
- `helm/` - Helm charts for complex deployments
- `terraform/` - Infrastructure as Code for cloud resources

## Security Notes

- Never commit real secrets or credentials
- Use Kubernetes Secrets for sensitive configuration
- Regularly rotate development credentials
- Keep resource quotas reasonable for local development

## Troubleshooting

Common issues and solutions:

1. **Resource Quota Exceeded**: Check current usage with `kubectl describe resourcequota`
2. **Pod Pending**: Check node resources and quotas
3. **Image Pull Errors**: Verify image name and registry access
4. **Service Not Accessible**: Check service selector labels match pod labels

For more detailed troubleshooting, see [docs/dev-environment.md](../docs/dev-environment.md).
