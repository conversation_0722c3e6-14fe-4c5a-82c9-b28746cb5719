---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: hello-world-route
  namespace: cdh-dev
  labels:
    app: hello-world
    component: routing
spec:
  entryPoints:
    - web
  routes:
    - match: PathPrefix(`/hello-world`)
      kind: Rule
      services:
        - name: hello-world-service
          port: 8080
    - match: Path(`/hello-world/health`)
      kind: Rule
      services:
        - name: hello-world-service
          port: 8080
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: hello-world-root-route
  namespace: cdh-dev
  labels:
    app: hello-world
    component: routing
spec:
  entryPoints:
    - web
  routes:
    - match: Path(`/health`)
      kind: Rule
      services:
        - name: hello-world-service
          port: 8080
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: inventory-service-route
  namespace: cdh-dev
  labels:
    app: inventory-service
    component: routing
spec:
  entryPoints:
    - web
  routes:
    - match: PathPrefix(`/inventory`)
      kind: Rule
      services:
        - name: inventory-service
          port: 8081
    - match: Path(`/health/live`)
      kind: Rule
      services:
        - name: inventory-service
          port: 8081
    - match: Path(`/health/ready`)
      kind: Rule
      services:
        - name: inventory-service
          port: 8081
