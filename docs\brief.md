# Project Brief: Central Data Hub (CDH) for Retail Integration

### 1. Executive Summary
This project aims to design and develop a cloud-native microservices platform named the "Central Data Hub (CDH)." The core objective of this platform is to integrate the company's existing Point of Sale (POS), Warehouse Management System (WMS), and e-commerce platforms to create a unified, real-time "single source of truth" for data. Through this platform, we will solve the problem of data silos, automate critical business processes—especially the mandatory LHDN MyInvois e-invoice integration—and establish a scalable, high-performance foundation for future business expansion.

### 2. Problem Statement
Currently, the company's core business systems (POS, WMS, e-commerce) operate independently, leading to several key pain points:
* **Data Silos & Inconsistency:** Inventory, sales, and customer data are scattered across different systems, resulting in data discrepancies that require significant manual reconciliation, which is inefficient and error-prone.
* **Lack of Real-Time Inventory Visibility:** There is no real-time view of stock levels across all channels, which can lead to online overselling or offline stockouts, impacting customer experience and sales opportunities.
* **Cumbersome Financial Processes:** Order and financial data are separate, making financial reconciliation and record generation a time-consuming and labor-intensive process.
* **Mandatory Compliance Risk:** The Malaysian government is set to enforce the LHDN MyInvois e-invoicing system. Under the current architecture, we cannot efficiently and automatically meet this mandatory regulatory requirement, posing a compliance risk.

### 3. Proposed Solution
We will adopt the proposed Go-based, cloud-native microservice architecture. This solution modularizes core functionalities, with each module running as an independent service that communicates via APIs and message queues.
* **Core Services:** An API Gateway will serve as a unified, secure entry point; a User & Permissions Service will manage unified identity and access control; an Inventory Service will act as the single source of truth for omni-channel inventory; an Order Service will centralize order processing; and a Finance & Tax Service will automate financial records and integrate with the LHDN MyInvois API for automated e-invoice processing.
* **Design Principles:** The entire system will follow stateless, event-driven, and API-first design principles to ensure high availability, fast response times, and future scalability.

### 4. Target Users
* **Business Operations Team:** Includes POS staff, warehouse managers, and e-commerce administrators who will benefit from streamlined processes and real-time, accurate data.
* **Finance & Management Team:** Requires accurate, unified business data for decision-making, financial reporting, and tax compliance.
* **System Integrations:** Existing POS, WMS, and e-commerce platforms will act as "users" of the system, interacting with the CDH via the API Gateway.

### 5. Goals & Success Metrics
* **Business Objectives:**
    * Achieve 100% automated compliance with the LHDN MyInvois system by the official deadline.
    * Reduce cross-channel inventory discrepancies by 90% within 3 months of platform launch.
    * Fully automate the order-to-financial-record process, reducing financial reconciliation time by 80%.
* **Key Performance Indicators (KPIs):**
    * E-invoice submission success rate to LHDN > 99.9%.
    * Average API Gateway response time < 200ms.
    * System uptime > 99.95%.

### 6. MVP (Minimum Viable Product) Scope
To address the core pain points and validate the architecture as quickly as possible, the MVP will focus on the following essential functions:
* **In-Scope:**
    1.  **API Gateway:** Implementing basic routing and security authentication.
    2.  **Order Service:** Capable of receiving and processing orders from at least one core channel (e.g., the e-commerce platform).
    3.  **Inventory Service:** Capable of accurately deducting inventory based on order information.
    4.  **Finance & Tax Service:** **Must** include the full integration with the LHDN MyInvois API for automated e-invoice handling.
    5.  **User Service:** Providing basic API access token authentication.
* **Out-of-Scope:**
    * Complex role and permission management system (MVP will use basic roles).
    * Advanced data analytics and visualization dashboards.
    * Non-core business services such as marketing or CRM.

### 7. Technical Considerations (Confirmed)
This project will strictly adhere to the provided technology choices:
* **Language:** Go
* **Database:** Independent PostgreSQL database for each microservice
* **Cache:** Redis
* **Message Queue:** Kafka
* **Containerization & Orchestration:** Docker & Kubernetes (K8s)
* **API Gateway:** Traefik
* **Monitoring & Logging:** Prometheus, Grafana, EFK Stack

### 8. Constraints & Assumptions
* **Constraints:**
    * The project timeline is constrained by the mandatory LHDN MyInvois compliance deadline.
    * The technology stack is fixed as per the confirmed list.
* **Assumptions:**
    * The APIs for the future POS, WMS, and e-commerce platforms will be available and well-documented.
    * The development team possesses or can acquire the necessary skills in Go and Kubernetes.

### 9. Risks & Open Questions
* **Primary Risk:** The integration with the LHDN MyInvois API may be more complex than anticipated. We need to obtain its technical documentation and conduct prototype testing as early as possible.
* **Open Questions:**
    * What is the exact deadline for the LHDN mandate?
    * What are the specific API specifications for the future systems that will connect to the CDH?