package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"time"

	_ "github.com/lib/pq"
)

// DB holds the database connection
var DB *sql.DB

// Config holds database configuration
type Config struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
}

// GetConfigFromEnv reads database configuration from environment variables
func GetConfigFromEnv() *Config {
	return &Config{
		Host:     getEnvOrDefault("DB_HOST", "localhost"),
		Port:     getEnvOrDefault("DB_PORT", "5432"),
		User:     getEnvOrDefault("DB_USER", "postgres"),
		Password: getEnvOrDefault("DB_PASSWORD", "postgres"),
		DBName:   getEnvOrDefault("DB_NAME", "user_db"),
		SSLMode:  getEnvOrDefault("DB_SSLMODE", "disable"),
	}
}

// getEnvOrDefault returns environment variable value or default if not set
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// Connect establishes a connection to the PostgreSQL database
func Connect() error {
	// Check if we're using Supabase
	supabaseURL := getEnvOrDefault("SUPABASE_DB_URL", "")
	if supabaseURL != "" {
		// Use Supabase connection string directly
		var err error
		DB, err = sql.Open("postgres", supabaseURL)
		if err != nil {
			return fmt.Errorf("failed to open database connection: %w", err)
		}
		log.Println("Using Supabase database connection")
	} else {
		// Default local PostgreSQL connection
		config := GetConfigFromEnv()

		// Build connection string
		connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
			config.Host, config.Port, config.User, config.Password, config.DBName, config.SSLMode)

		var err error
		DB, err = sql.Open("postgres", connStr)
		if err != nil {
			return fmt.Errorf("failed to open database connection: %w", err)
		}
		log.Printf("Using local PostgreSQL connection: %s", config.DBName)
	}

	// Configure connection pool for production use
	DB.SetMaxOpenConns(25)                 // Maximum number of open connections
	DB.SetMaxIdleConns(5)                  // Maximum number of idle connections
	DB.SetConnMaxLifetime(5 * time.Minute) // Maximum connection lifetime
	DB.SetConnMaxIdleTime(1 * time.Minute) // Maximum idle time

	// Test the connection
	if err := DB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Successfully connected to database")
	return nil
}

// Close closes the database connection
func Close() error {
	if DB != nil {
		return DB.Close()
	}
	return nil
}

// GetDB returns the database connection
func GetDB() *sql.DB {
	return DB
}
