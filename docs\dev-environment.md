# CDH Local Development Environment

This document describes the local development environment setup for the CDH (Customer Data Hub) project using Docker Desktop and Kubernetes.

## Overview

The CDH project uses a containerized development environment with:
- **Docker Desktop**: For container management and local Kubernetes cluster
- **Kubernetes**: For orchestration and microservices deployment
- **Namespace Isolation**: `cdh-dev` namespace for development work
- **Resource Management**: Configured quotas and limits for local development

## Prerequisites

- Windows 10/11 with WSL2 enabled
- Docker Desktop for Windows (latest stable version)
- Minimum 8GB RAM (16GB recommended)
- Minimum 4GB available disk space

## Environment Setup

### 1. Docker Desktop Installation

1. Download Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop)
2. Install with default settings
3. Ensure WSL2 backend is enabled
4. Allocate at least 4GB RAM to Docker in Settings > Resources

### 2. Enable Kubernetes

1. Open Docker Desktop
2. Go to Settings (gear icon)
3. Navigate to Kubernetes tab
4. Check "Enable Kubernetes"
5. Click "Apply & Restart"
6. Wait for Kubernetes to start (green indicator)

### 3. Verify Installation

```powershell
# Check Docker version
docker --version

# Test Docker functionality
docker run hello-world

# Check Kubernetes cluster
kubectl cluster-info
kubectl get nodes
```

## Development Namespace

The `cdh-dev` namespace is configured with:

### Resource Quotas
- **CPU Requests**: 2 cores total
- **Memory Requests**: 2GB total  
- **CPU Limits**: 4 cores total
- **Memory Limits**: 4GB total
- **Maximum Pods**: 10
- **Maximum Services**: 5
- **Maximum PVCs**: 3
- **Maximum Deployments**: 5

### Default Container Limits
- **Default CPU Limit**: 500m
- **Default Memory Limit**: 512Mi
- **Default CPU Request**: 100m
- **Default Memory Request**: 128Mi

## Daily Development Commands

### Namespace Management
```powershell
# Switch to development namespace
kubectl config set-context --current --namespace=cdh-dev

# Verify current namespace
kubectl config view --minify --output 'jsonpath={..namespace}'

# View namespace resources
kubectl get all
```

### Common Operations
```powershell
# Apply manifests
kubectl apply -f infra/k8s/dev/

# View pod logs
kubectl logs <pod-name>

# Port forward to service
kubectl port-forward service/<service-name> <local-port>:<service-port>

# Execute commands in pod
kubectl exec -it <pod-name> -- /bin/sh

# Delete resources
kubectl delete -f <manifest-file>
```

### Resource Monitoring
```powershell
# Check resource usage
kubectl top nodes
kubectl top pods

# View resource quotas
kubectl describe resourcequota cdh-dev-quota

# View limit ranges
kubectl describe limitrange cdh-dev-limits
```

## File Structure

```
CDH/
├── infra/
│   └── k8s/
│       └── dev/
│           ├── namespace-config.yaml    # Namespace, quotas, limits
│           └── test-deployment.yaml     # Test deployment template
├── scripts/
│   └── dev/                            # Development scripts
└── docs/
    └── dev-environment.md              # This documentation
```

## Environment Verification Checklist

- [ ] Docker Desktop is running
- [ ] Kubernetes cluster is healthy (`kubectl cluster-info`)
- [ ] Node is ready (`kubectl get nodes`)
- [ ] `cdh-dev` namespace exists (`kubectl get namespace cdh-dev`)
- [ ] Resource quotas are applied (`kubectl describe resourcequota cdh-dev-quota`)
- [ ] Default context is set to `cdh-dev` namespace
- [ ] Test deployment can be created and accessed
- [ ] Port forwarding works correctly

## Troubleshooting

### Docker Issues

**Problem**: Docker Desktop won't start
- **Solution**: Restart Docker Desktop service, check WSL2 is enabled
- **Command**: `Restart-Service docker`

**Problem**: "Docker daemon not running"
- **Solution**: Start Docker Desktop application
- **Check**: Docker icon in system tray should be green

### Kubernetes Issues

**Problem**: `kubectl` command not found
- **Solution**: Ensure Docker Desktop Kubernetes is enabled
- **Path**: kubectl is at `C:\Program Files\Docker\Docker\resources\bin\kubectl.exe`

**Problem**: "Unable to connect to server"
- **Solution**: Restart Kubernetes in Docker Desktop settings
- **Wait**: Allow 2-5 minutes for cluster to become ready

**Problem**: Pods stuck in Pending state
- **Solution**: Check resource quotas and node resources
- **Commands**: 
  ```powershell
  kubectl describe pod <pod-name>
  kubectl describe resourcequota cdh-dev-quota
  ```

### Namespace Issues

**Problem**: Resources not appearing
- **Solution**: Verify you're in the correct namespace
- **Command**: `kubectl config current-context && kubectl config view --minify --output 'jsonpath={..namespace}'`

**Problem**: Resource quota exceeded
- **Solution**: Delete unused resources or increase quotas
- **Commands**:
  ```powershell
  kubectl get all
  kubectl delete deployment <unused-deployment>
  ```

## Reset Environment

To reset the development environment to a clean state:

```powershell
# Delete all resources in cdh-dev namespace
kubectl delete all --all -n cdh-dev

# Or delete and recreate the namespace
kubectl delete namespace cdh-dev
kubectl apply -f infra/k8s/dev/namespace-config.yaml
kubectl config set-context --current --namespace=cdh-dev
```

## Next Steps

After setting up the local environment:

1. **Story 1.2**: Implement CI/CD Pipeline
2. **Deploy microservices**: Use this environment for local testing
3. **Integration testing**: Test service-to-service communication
4. **Performance testing**: Monitor resource usage and optimize

## Support

For issues not covered in this guide:
1. Check Docker Desktop logs in Settings > Troubleshoot
2. Review Kubernetes events: `kubectl get events --sort-by=.metadata.creationTimestamp`
3. Consult project documentation in `docs/` directory
