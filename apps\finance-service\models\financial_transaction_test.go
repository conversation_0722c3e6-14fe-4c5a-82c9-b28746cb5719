package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestFinancialTransaction_Validate(t *testing.T) {
	tests := []struct {
		name        string
		transaction FinancialTransaction
		wantErr     bool
		errMsg      string
	}{
		{
			name: "valid financial transaction",
			transaction: FinancialTransaction{
				OrderReference:   "order-123",
				EventID:          "event-123",
				ProcessingStatus: ProcessingStatusPending,
			},
			wantErr: false,
		},
		{
			name: "missing order reference",
			transaction: FinancialTransaction{
				EventID:          "event-123",
				ProcessingStatus: ProcessingStatusPending,
			},
			wantErr: true,
			errMsg:  "order_reference is required",
		},
		{
			name: "missing event ID",
			transaction: FinancialTransaction{
				OrderReference:   "order-123",
				ProcessingStatus: ProcessingStatusPending,
			},
			wantErr: true,
			errMsg:  "event_id is required",
		},
		{
			name: "invalid processing status",
			transaction: FinancialTransaction{
				OrderReference:   "order-123",
				EventID:          "event-123",
				ProcessingStatus: "invalid_status",
			},
			wantErr: true,
			errMsg:  "invalid processing_status",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.transaction.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestFinancialTransaction_MarkCompleted(t *testing.T) {
	transaction := FinancialTransaction{
		OrderReference:   "order-123",
		EventID:          "event-123",
		ProcessingStatus: ProcessingStatusPending,
	}

	financialEntryID := 42
	transaction.MarkCompleted(financialEntryID)

	assert.Equal(t, ProcessingStatusCompleted, transaction.ProcessingStatus)
	assert.Equal(t, &financialEntryID, transaction.FinancialEntryID)
	assert.NotNil(t, transaction.ProcessedAt)
	assert.Nil(t, transaction.ErrorMessage)
	assert.WithinDuration(t, time.Now(), *transaction.ProcessedAt, time.Second)
}

func TestFinancialTransaction_MarkFailed(t *testing.T) {
	transaction := FinancialTransaction{
		OrderReference:   "order-123",
		EventID:          "event-123",
		ProcessingStatus: ProcessingStatusPending,
	}

	errorMsg := "processing failed"
	transaction.MarkFailed(errorMsg)

	assert.Equal(t, ProcessingStatusFailed, transaction.ProcessingStatus)
	assert.Equal(t, &errorMsg, transaction.ErrorMessage)
	assert.NotNil(t, transaction.ProcessedAt)
	assert.WithinDuration(t, time.Now(), *transaction.ProcessedAt, time.Second)
}

func TestProcessingStatus_Constants(t *testing.T) {
	assert.Equal(t, ProcessingStatus("pending"), ProcessingStatusPending)
	assert.Equal(t, ProcessingStatus("completed"), ProcessingStatusCompleted)
	assert.Equal(t, ProcessingStatus("failed"), ProcessingStatusFailed)
	assert.Equal(t, ProcessingStatus("retrying"), ProcessingStatusRetrying)
}

func TestIsValidProcessingStatus(t *testing.T) {
	tests := []struct {
		status ProcessingStatus
		valid  bool
	}{
		{ProcessingStatusPending, true},
		{ProcessingStatusCompleted, true},
		{ProcessingStatusFailed, true},
		{ProcessingStatusRetrying, true},
		{"invalid", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(string(tt.status), func(t *testing.T) {
			result := isValidProcessingStatus(tt.status)
			assert.Equal(t, tt.valid, result)
		})
	}
}