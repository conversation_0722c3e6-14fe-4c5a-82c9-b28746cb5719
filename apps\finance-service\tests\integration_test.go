package tests

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/company/cdh/apps/finance-service/internal/services"
	"github.com/company/cdh/apps/finance-service/models"
)

// MockFinanceRepository for integration tests
type MockFinanceRepository struct {
	transactions map[string]*models.FinancialTransaction
	entries      []*models.FinancialEntry
	nextEntryID  int
}

func NewMockFinanceRepository() *MockFinanceRepository {
	return &MockFinanceRepository{
		transactions: make(map[string]*models.FinancialTransaction),
		entries:      make([]*models.FinancialEntry, 0),
		nextEntryID:  1,
	}
}

func (m *MockFinanceRepository) CreateFinancialEntry(entry *models.FinancialEntry) error {
	entry.ID = m.nextEntryID
	m.nextEntryID++
	entry.CreatedAt = time.Now()
	entry.UpdatedAt = time.Now()
	m.entries = append(m.entries, entry)
	return nil
}

func (m *MockFinanceRepository) GetFinancialEntryByTransactionID(transactionID string) (*models.FinancialEntry, error) {
	for _, entry := range m.entries {
		if entry.TransactionID == transactionID {
			return entry, nil
		}
	}
	return nil, assert.AnError
}

func (m *MockFinanceRepository) GetFinancialEntriesByOrderID(orderID string) ([]*models.FinancialEntry, error) {
	var result []*models.FinancialEntry
	for _, entry := range m.entries {
		if entry.OrderID == orderID {
			result = append(result, entry)
		}
	}
	return result, nil
}

func (m *MockFinanceRepository) CreateFinancialTransaction(transaction *models.FinancialTransaction) error {
	transaction.ID = len(m.transactions) + 1
	transaction.CreatedAt = time.Now()
	m.transactions[transaction.EventID] = transaction
	return nil
}

func (m *MockFinanceRepository) GetFinancialTransactionByEventID(eventID string) (*models.FinancialTransaction, error) {
	if transaction, exists := m.transactions[eventID]; exists {
		return transaction, nil
	}
	return nil, assert.AnError
}

func (m *MockFinanceRepository) UpdateFinancialTransaction(transaction *models.FinancialTransaction) error {
	m.transactions[transaction.EventID] = transaction
	return nil
}

// API operations for external platforms
func (m *MockFinanceRepository) GetFinancialRecords(filters *models.QueryFilters) ([]*models.FinancialEntry, int, error) {
	// Filter entries based on criteria
	var filteredEntries []*models.FinancialEntry

	for _, entry := range m.entries {
		// Apply order reference filter
		if filters.OrderReference != "" && entry.OrderID != filters.OrderReference {
			continue
		}

		// Apply status filter (for testing, assume all entries have "pending" status)
		if filters.Status != "" && filters.Status != "pending" {
			continue
		}

		// Apply date filters
		if filters.DateFrom != nil && entry.CreatedAt.Before(*filters.DateFrom) {
			continue
		}
		if filters.DateTo != nil && entry.CreatedAt.After(*filters.DateTo) {
			continue
		}

		filteredEntries = append(filteredEntries, entry)
	}

	total := len(filteredEntries)

	// Apply pagination
	start := (filters.Page - 1) * filters.Limit
	end := start + filters.Limit

	if start >= total {
		return []*models.FinancialEntry{}, total, nil
	}

	if end > total {
		end = total
	}

	return filteredEntries[start:end], total, nil
}

func (m *MockFinanceRepository) GetFinancialRecordByID(id int) (*models.FinancialEntry, error) {
	for _, entry := range m.entries {
		if entry.ID == id {
			return entry, nil
		}
	}
	return nil, assert.AnError
}

func (m *MockFinanceRepository) UpdateFinancialRecordStatus(id int, status string, notes string) error {
	for _, entry := range m.entries {
		if entry.ID == id {
			entry.UpdatedAt = time.Now()
			return nil
		}
	}
	return assert.AnError
}

// Audit trail operations (placeholder implementations)
func (m *MockFinanceRepository) CreateAuditTrailEntry(entry *models.AuditTrailEntry) error {
	// Placeholder implementation for testing
	return nil
}

func (m *MockFinanceRepository) GetAuditTrailByRecordID(recordID int) ([]*models.AuditTrailEntry, error) {
	// Placeholder implementation for testing
	return []*models.AuditTrailEntry{}, nil
}

// MockEventProducer for integration tests
type MockEventProducer struct {
	publishedEvents []*models.FinancialRecordEvent
}

func NewMockEventProducer() *MockEventProducer {
	return &MockEventProducer{
		publishedEvents: make([]*models.FinancialRecordEvent, 0),
	}
}

func (m *MockEventProducer) PublishFinancialRecord(ctx context.Context, event *models.FinancialRecordEvent) error {
	m.publishedEvents = append(m.publishedEvents, event)
	return nil
}

func (m *MockEventProducer) PublishFinancialRecordBatch(ctx context.Context, events []*models.FinancialRecordEvent) error {
	m.publishedEvents = append(m.publishedEvents, events...)
	return nil
}

func (m *MockEventProducer) Close() error {
	return nil
}

func (m *MockEventProducer) GetPublishedEvents() []*models.FinancialRecordEvent {
	return m.publishedEvents
}

// Integration test for complete order processing flow
func TestIntegration_CompleteOrderProcessingFlow(t *testing.T) {
	// Setup
	mockRepo := NewMockFinanceRepository()
	mockProducer := NewMockEventProducer()
	financeService := services.NewFinanceService(mockRepo, mockProducer)

	// Create test order event
	orderEvent := models.OrderCreatedEvent{
		EventID:     "event-integration-001",
		EventType:   "order.created",
		Timestamp:   time.Now(),
		OrderID:     "order-integration-001",
		OrderNumber: "ORD-INT-001",
		ProductInfo: "Integration Test Product",
		Quantity:    3,
		Price:       150.00,
		Status:      "confirmed",
		CreatedAt:   time.Now(),
	}

	eventJSON, err := json.Marshal(orderEvent)
	require.NoError(t, err)

	// Execute
	err = financeService.ProcessOrderEvent(eventJSON)

	// Verify
	assert.NoError(t, err)

	// Check financial entry was created
	entries, err := mockRepo.GetFinancialEntriesByOrderID("order-integration-001")
	require.NoError(t, err)
	require.Len(t, entries, 1)

	entry := entries[0]
	assert.Equal(t, "order-integration-001", entry.OrderID)
	assert.Equal(t, 450.00, entry.RevenueAmount) // 3 * 150.00
	assert.Equal(t, 27.00, entry.TaxAmount)      // 6% of 450.00
	assert.Equal(t, "MYR", entry.Currency)
	assert.Contains(t, entry.TransactionID, "TXN-order-integration-001-")

	// Check financial transaction was created and completed
	transaction, err := mockRepo.GetFinancialTransactionByEventID("event-integration-001")
	require.NoError(t, err)
	assert.Equal(t, "ORD-INT-001", transaction.OrderReference)
	assert.Equal(t, models.ProcessingStatusCompleted, transaction.ProcessingStatus)
	assert.Equal(t, entry.ID, *transaction.FinancialEntryID)

	// Check event was published
	publishedEvents := mockProducer.GetPublishedEvents()
	require.Len(t, publishedEvents, 1)

	publishedEvent := publishedEvents[0]
	assert.Equal(t, entry.TransactionID, publishedEvent.TransactionID)
	assert.Equal(t, "order-integration-001", publishedEvent.OrderReference)
	assert.Equal(t, 450.00, publishedEvent.RevenueAmount)
	assert.Equal(t, 27.00, publishedEvent.TaxAmount)
	assert.Equal(t, "MYR", publishedEvent.Currency)
	assert.Equal(t, "finance-service", publishedEvent.Metadata["source_service"])
	assert.Equal(t, "1.0", publishedEvent.Metadata["event_version"])
}

// Test different payment method scenarios
func TestIntegration_DifferentPaymentMethods(t *testing.T) {
	testCases := []struct {
		name            string
		orderID         string
		eventID         string
		orderNumber     string
		quantity        int
		price           float64
		expectedRevenue float64
		expectedTax     float64
	}{
		{
			name:            "Credit Card Payment",
			orderID:         "order-cc-001",
			eventID:         "event-cc-001",
			orderNumber:     "ORD-CC-001",
			quantity:        1,
			price:           100.00,
			expectedRevenue: 100.00,
			expectedTax:     6.00,
		},
		{
			name:            "Bank Transfer Payment",
			orderID:         "order-bt-001",
			eventID:         "event-bt-001",
			orderNumber:     "ORD-BT-001",
			quantity:        2,
			price:           75.50,
			expectedRevenue: 151.00,
			expectedTax:     9.06,
		},
		{
			name:            "E-Wallet Payment",
			orderID:         "order-ew-001",
			eventID:         "event-ew-001",
			orderNumber:     "ORD-EW-001",
			quantity:        5,
			price:           25.00,
			expectedRevenue: 125.00,
			expectedTax:     7.50,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup
			mockRepo := NewMockFinanceRepository()
			mockProducer := NewMockEventProducer()
			financeService := services.NewFinanceService(mockRepo, mockProducer)

			// Create test order event
			orderEvent := models.OrderCreatedEvent{
				EventID:     tc.eventID,
				EventType:   "order.created",
				Timestamp:   time.Now(),
				OrderID:     tc.orderID,
				OrderNumber: tc.orderNumber,
				ProductInfo: "Test Product",
				Quantity:    tc.quantity,
				Price:       tc.price,
				Status:      "confirmed",
				CreatedAt:   time.Now(),
			}

			eventJSON, err := json.Marshal(orderEvent)
			require.NoError(t, err)

			// Execute
			err = financeService.ProcessOrderEvent(eventJSON)
			require.NoError(t, err)

			// Verify financial calculations
			entries, err := mockRepo.GetFinancialEntriesByOrderID(tc.orderID)
			require.NoError(t, err)
			require.Len(t, entries, 1)

			entry := entries[0]
			assert.Equal(t, tc.expectedRevenue, entry.RevenueAmount)
			assert.Equal(t, tc.expectedTax, entry.TaxAmount)

			// Verify event was published with correct amounts
			publishedEvents := mockProducer.GetPublishedEvents()
			require.Len(t, publishedEvents, 1)

			publishedEvent := publishedEvents[0]
			assert.Equal(t, tc.expectedRevenue, publishedEvent.RevenueAmount)
			assert.Equal(t, tc.expectedTax, publishedEvent.TaxAmount)
		})
	}
}

// Test event schema compliance
func TestIntegration_EventSchemaCompliance(t *testing.T) {
	// Setup
	mockRepo := NewMockFinanceRepository()
	mockProducer := NewMockEventProducer()
	financeService := services.NewFinanceService(mockRepo, mockProducer)

	// Create test order event
	orderEvent := models.OrderCreatedEvent{
		EventID:     "event-schema-001",
		EventType:   "order.created",
		Timestamp:   time.Now(),
		OrderID:     "order-schema-001",
		OrderNumber: "ORD-SCHEMA-001",
		ProductInfo: "Schema Test Product",
		Quantity:    1,
		Price:       100.00,
		Status:      "confirmed",
		CreatedAt:   time.Now(),
	}

	eventJSON, err := json.Marshal(orderEvent)
	require.NoError(t, err)

	// Execute
	err = financeService.ProcessOrderEvent(eventJSON)
	require.NoError(t, err)

	// Verify published event schema compliance
	publishedEvents := mockProducer.GetPublishedEvents()
	require.Len(t, publishedEvents, 1)

	event := publishedEvents[0]

	// Verify all required fields are present
	assert.NotEmpty(t, event.TransactionID)
	assert.NotEmpty(t, event.OrderReference)
	assert.True(t, event.RevenueAmount >= 0)
	assert.True(t, event.TaxAmount >= 0)
	assert.NotEmpty(t, event.Currency)
	assert.False(t, event.Timestamp.IsZero())
	assert.NotNil(t, event.Metadata)

	// Verify metadata contains required fields
	assert.Equal(t, "finance-service", event.Metadata["source_service"])
	assert.Equal(t, "1.0", event.Metadata["event_version"])
	assert.NotEmpty(t, event.Metadata["transaction_type"])

	// Verify event can be serialized and deserialized
	eventJSON, err = event.ToJSON()
	require.NoError(t, err)

	deserializedEvent, err := models.FromJSON(eventJSON)
	require.NoError(t, err)
	assert.Equal(t, event.TransactionID, deserializedEvent.TransactionID)
	assert.Equal(t, event.OrderReference, deserializedEvent.OrderReference)

	// Verify event passes validation
	err = event.Validate()
	assert.NoError(t, err)
}
