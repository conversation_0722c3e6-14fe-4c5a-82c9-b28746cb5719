package services

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/company/cdh/apps/finance-service/models"
)

// MockFinanceRepository is a mock implementation of FinanceRepository
type MockFinanceRepository struct {
	mock.Mock
}

func (m *MockFinanceRepository) CreateFinancialEntry(entry *models.FinancialEntry) error {
	args := m.Called(entry)
	return args.Error(0)
}

func (m *MockFinanceRepository) GetFinancialEntryByTransactionID(transactionID string) (*models.FinancialEntry, error) {
	args := m.Called(transactionID)
	return args.Get(0).(*models.FinancialEntry), args.Error(1)
}

func (m *MockFinanceRepository) GetFinancialEntriesByOrderID(orderID string) ([]*models.FinancialEntry, error) {
	args := m.Called(orderID)
	return args.Get(0).([]*models.FinancialEntry), args.Error(1)
}

func (m *MockFinanceRepository) CreateFinancialTransaction(transaction *models.FinancialTransaction) error {
	args := m.Called(transaction)
	return args.Error(0)
}

func (m *MockFinanceRepository) GetFinancialTransactionByEventID(eventID string) (*models.FinancialTransaction, error) {
	args := m.Called(eventID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.FinancialTransaction), args.Error(1)
}

func (m *MockFinanceRepository) UpdateFinancialTransaction(transaction *models.FinancialTransaction) error {
	args := m.Called(transaction)
	return args.Error(0)
}

// API operations for external platforms
func (m *MockFinanceRepository) GetFinancialRecords(filters *models.QueryFilters) ([]*models.FinancialEntry, int, error) {
	args := m.Called(filters)
	return args.Get(0).([]*models.FinancialEntry), args.Int(1), args.Error(2)
}

func (m *MockFinanceRepository) GetFinancialRecordByID(id int) (*models.FinancialEntry, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.FinancialEntry), args.Error(1)
}

func (m *MockFinanceRepository) UpdateFinancialRecordStatus(id int, status string, notes string) error {
	args := m.Called(id, status, notes)
	return args.Error(0)
}

// Audit trail operations
func (m *MockFinanceRepository) CreateAuditTrailEntry(entry *models.AuditTrailEntry) error {
	args := m.Called(entry)
	return args.Error(0)
}

func (m *MockFinanceRepository) GetAuditTrailByRecordID(recordID int) ([]*models.AuditTrailEntry, error) {
	args := m.Called(recordID)
	return args.Get(0).([]*models.AuditTrailEntry), args.Error(1)
}

// MockFinancialEventProducer is a mock implementation of FinancialEventProducerInterface
type MockFinancialEventProducer struct {
	mock.Mock
}

func (m *MockFinancialEventProducer) PublishFinancialRecord(ctx context.Context, event *models.FinancialRecordEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *MockFinancialEventProducer) PublishFinancialRecordBatch(ctx context.Context, events []*models.FinancialRecordEvent) error {
	args := m.Called(ctx, events)
	return args.Error(0)
}

func (m *MockFinancialEventProducer) Close() error {
	args := m.Called()
	return args.Error(0)
}

func TestNewFinanceService(t *testing.T) {
	mockRepo := &MockFinanceRepository{}
	mockProducer := &MockFinancialEventProducer{}
	service := NewFinanceService(mockRepo, mockProducer)

	assert.NotNil(t, service)
	assert.Implements(t, (*FinanceServiceInterface)(nil), service)
}

func TestFinanceService_ProcessOrderEvent_Success(t *testing.T) {
	mockRepo := &MockFinanceRepository{}
	mockProducer := &MockFinancialEventProducer{}
	service := NewFinanceService(mockRepo, mockProducer)

	// Create test order event
	orderEvent := models.OrderCreatedEvent{
		EventID:     "event-123",
		EventType:   "order.created",
		Timestamp:   time.Now(),
		OrderID:     "order-123",
		OrderNumber: "ORD-001",
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       100.00,
		Status:      "confirmed",
		CreatedAt:   time.Now(),
	}

	eventJSON, _ := json.Marshal(orderEvent)

	// Mock expectations
	mockRepo.On("GetFinancialTransactionByEventID", "event-123").Return(nil, errors.New("not found")).Once()
	mockRepo.On("CreateFinancialTransaction", mock.AnythingOfType("*models.FinancialTransaction")).Return(nil).Once()
	mockRepo.On("CreateFinancialEntry", mock.AnythingOfType("*models.FinancialEntry")).Return(nil).Once()
	mockProducer.On("PublishFinancialRecord", mock.Anything, mock.AnythingOfType("*models.FinancialRecordEvent")).Return(nil).Once()
	mockRepo.On("UpdateFinancialTransaction", mock.AnythingOfType("*models.FinancialTransaction")).Return(nil).Once()

	err := service.ProcessOrderEvent(eventJSON)

	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
	mockProducer.AssertExpectations(t)
}

func TestFinanceService_ProcessOrderEvent_DuplicateEvent(t *testing.T) {
	mockRepo := &MockFinanceRepository{}
	mockProducer := &MockFinancialEventProducer{}
	service := NewFinanceService(mockRepo, mockProducer)

	// Create test order event
	orderEvent := models.OrderCreatedEvent{
		EventID:     "event-123",
		EventType:   "order.created",
		OrderID:     "order-123",
		OrderNumber: "ORD-001",
		Quantity:    1,
		Price:       100.00,
	}

	eventJSON, _ := json.Marshal(orderEvent)

	// Mock existing transaction
	existingTransaction := &models.FinancialTransaction{
		ID:               1,
		EventID:          "event-123",
		ProcessingStatus: models.ProcessingStatusCompleted,
	}

	mockRepo.On("GetFinancialTransactionByEventID", "event-123").Return(existingTransaction, nil).Once()

	err := service.ProcessOrderEvent(eventJSON)

	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
}

func TestFinanceService_ProcessOrderEvent_InvalidJSON(t *testing.T) {
	mockRepo := &MockFinanceRepository{}
	mockProducer := &MockFinancialEventProducer{}
	service := NewFinanceService(mockRepo, mockProducer)

	invalidJSON := []byte(`{"invalid": "json"`)

	err := service.ProcessOrderEvent(invalidJSON)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to parse order event")
}

func TestFinanceService_ProcessOrderEvent_CreateTransactionError(t *testing.T) {
	mockRepo := &MockFinanceRepository{}
	mockProducer := &MockFinancialEventProducer{}
	service := NewFinanceService(mockRepo, mockProducer)

	orderEvent := models.OrderCreatedEvent{
		EventID:     "event-123",
		EventType:   "order.created",
		OrderID:     "order-123",
		OrderNumber: "ORD-001",
		Quantity:    1,
		Price:       100.00,
	}

	eventJSON, _ := json.Marshal(orderEvent)

	mockRepo.On("GetFinancialTransactionByEventID", "event-123").Return(nil, errors.New("not found")).Once()
	mockRepo.On("CreateFinancialTransaction", mock.AnythingOfType("*models.FinancialTransaction")).Return(errors.New("database error")).Once()

	err := service.ProcessOrderEvent(eventJSON)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to create financial transaction")
	mockRepo.AssertExpectations(t)
}

func TestFinanceService_ProcessOrderEvent_CreateEntryError(t *testing.T) {
	mockRepo := &MockFinanceRepository{}
	mockProducer := &MockFinancialEventProducer{}
	service := NewFinanceService(mockRepo, mockProducer)

	orderEvent := models.OrderCreatedEvent{
		EventID:     "event-123",
		EventType:   "order.created",
		OrderID:     "order-123",
		OrderNumber: "ORD-001",
		Quantity:    1,
		Price:       100.00,
	}

	eventJSON, _ := json.Marshal(orderEvent)

	mockRepo.On("GetFinancialTransactionByEventID", "event-123").Return(nil, errors.New("not found")).Once()
	mockRepo.On("CreateFinancialTransaction", mock.AnythingOfType("*models.FinancialTransaction")).Return(nil).Once()
	mockRepo.On("CreateFinancialEntry", mock.AnythingOfType("*models.FinancialEntry")).Return(errors.New("database error")).Once()
	mockRepo.On("UpdateFinancialTransaction", mock.AnythingOfType("*models.FinancialTransaction")).Return(nil).Once()

	err := service.ProcessOrderEvent(eventJSON)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to save financial entry")
	mockRepo.AssertExpectations(t)
}

func TestFinanceService_generateFinancialEntry(t *testing.T) {
	mockRepo := &MockFinanceRepository{}
	mockProducer := &MockFinancialEventProducer{}
	service := &financeService{financeRepo: mockRepo, eventProducer: mockProducer}

	orderEvent := &models.OrderCreatedEvent{
		OrderID:     "order-123",
		OrderNumber: "ORD-001",
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       50.00,
	}

	entry, err := service.generateFinancialEntry(orderEvent)

	assert.NoError(t, err)
	assert.NotNil(t, entry)
	assert.Equal(t, "order-123", entry.OrderID)
	assert.Contains(t, entry.TransactionID, "TXN-order-123-")
	assert.Equal(t, 100.00, entry.RevenueAmount) // 2 * 50.00
	assert.Equal(t, 6.00, entry.TaxAmount)       // 6% of 100.00
	assert.Equal(t, "MYR", entry.Currency)
	assert.Equal(t, "unknown", entry.PaymentMethod)
	assert.Equal(t, "sale", entry.TransactionType)
	assert.Contains(t, entry.Description, "ORD-001")
	assert.Contains(t, entry.Description, "Test Product")
}

func TestFinanceService_PublishFinancialEvent_Success(t *testing.T) {
	mockRepo := &MockFinanceRepository{}
	mockProducer := &MockFinancialEventProducer{}
	service := NewFinanceService(mockRepo, mockProducer)

	financialEntry := &models.FinancialEntry{
		ID:              1,
		OrderID:         "order-123",
		TransactionID:   "txn-123",
		RevenueAmount:   100.00,
		TaxAmount:       6.00,
		Currency:        "MYR",
		PaymentMethod:   "credit_card",
		TransactionType: "sale",
		Description:     "Test transaction",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	mockProducer.On("PublishFinancialRecord", mock.Anything, mock.AnythingOfType("*models.FinancialRecordEvent")).Return(nil).Once()

	err := service.PublishFinancialEvent(financialEntry)

	assert.NoError(t, err)
	mockProducer.AssertExpectations(t)
}

func TestFinanceService_PublishFinancialEvent_Error(t *testing.T) {
	mockRepo := &MockFinanceRepository{}
	mockProducer := &MockFinancialEventProducer{}
	service := NewFinanceService(mockRepo, mockProducer)

	financialEntry := &models.FinancialEntry{
		ID:              1,
		OrderID:         "order-123",
		TransactionID:   "txn-123",
		RevenueAmount:   100.00,
		TaxAmount:       6.00,
		Currency:        "MYR",
		PaymentMethod:   "credit_card",
		TransactionType: "sale",
		Description:     "Test transaction",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	mockProducer.On("PublishFinancialRecord", mock.Anything, mock.AnythingOfType("*models.FinancialRecordEvent")).Return(errors.New("producer error")).Once()

	err := service.PublishFinancialEvent(financialEntry)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to publish financial record event")
	mockProducer.AssertExpectations(t)
}
