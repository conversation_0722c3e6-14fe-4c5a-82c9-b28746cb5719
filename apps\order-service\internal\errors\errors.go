package errors

import (
	"errors"
	"fmt"
)

// Common error types
var (
	ErrOrderNotFound     = errors.New("order not found")
	ErrInvalidOrderID    = errors.New("invalid order ID")
	ErrInvalidJSON       = errors.New("invalid JSON")
	ErrValidationFailed  = errors.New("validation failed")
	ErrDatabaseOperation = errors.New("database operation failed")
)

// ValidationError represents a validation error with details
type ValidationError struct {
	Field   string
	Message string
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("%s: %s", e.Field, e.Message)
}

// NewValidationError creates a new validation error
func NewValidationError(field, message string) ValidationError {
	return ValidationError{
		Field:   field,
		Message: message,
	}
}

// DatabaseError represents a database operation error
type DatabaseError struct {
	Operation string
	Err       error
}

func (e DatabaseError) Error() string {
	return fmt.Sprintf("database %s failed: %v", e.Operation, e.Err)
}

func (e DatabaseError) Unwrap() error {
	return e.Err
}

// NewDatabaseError creates a new database error
func NewDatabaseError(operation string, err error) DatabaseError {
	return DatabaseError{
		Operation: operation,
		Err:       err,
	}
}