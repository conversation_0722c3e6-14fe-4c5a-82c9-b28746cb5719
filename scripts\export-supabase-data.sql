-- Export script for migrating user data from Supabase to independent database
-- Run this script against the Supabase database to export existing user data

-- Export users table data
\copy (SELECT id, username, email, password_hash, created_at, updated_at FROM users ORDER BY id) TO 'users_export.csv' WITH CSV HEADER;

-- Alternative: Generate INSERT statements for manual migration
-- SELECT 
--     'INSERT INTO users (id, username, email, password_hash, created_at, updated_at) VALUES (' ||
--     id || ', ' ||
--     quote_literal(username) || ', ' ||
--     quote_literal(email) || ', ' ||
--     quote_literal(password_hash) || ', ' ||
--     quote_literal(created_at::text) || ', ' ||
--     quote_literal(updated_at::text) || ');'
-- FROM users
-- ORDER BY id;