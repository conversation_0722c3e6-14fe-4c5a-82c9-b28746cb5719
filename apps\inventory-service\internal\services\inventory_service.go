package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/go-redis/redis/v8"

	"github.com/company/cdh/apps/inventory-service/internal/repository"
	"github.com/company/cdh/apps/inventory-service/models"
)

// InventoryService defines the interface for inventory query operations
type InventoryService interface {
	// GetInventoryBySKUs retrieves inventory information for multiple SKUs
	GetInventoryBySKUs(ctx context.Context, skus []string) ([]*models.Inventory, error)

	// GetInventoryBySKU retrieves inventory information for a single SKU
	GetInventoryBySKU(ctx context.Context, sku string) (*models.Inventory, error)

	// UpdateInventory updates inventory and invalidates cache
	UpdateInventory(ctx context.Context, inventory *models.Inventory) error

	// ReserveStock reserves stock and invalidates cache
	ReserveStock(ctx context.Context, sku string, quantity int) error

	// ReleaseStock releases stock and invalidates cache
	ReleaseStock(ctx context.Context, sku string, quantity int) error

	// DeductStock deducts stock and invalidates cache
	DeductStock(ctx context.Context, sku string, quantity int) error
}

// inventoryService implements the InventoryService interface
type inventoryService struct {
	repo        repository.InventoryRepository
	redisClient *redis.Client
	cacheTTL    time.Duration
}

// NewInventoryService creates a new inventory service
func NewInventoryService(repo repository.InventoryRepository, redisClient *redis.Client) InventoryService {
	return &inventoryService{
		repo:        repo,
		redisClient: redisClient,
		cacheTTL:    5 * time.Minute, // 5 minutes cache TTL
	}
}

// GetInventoryBySKUs retrieves inventory information for multiple SKUs
func (s *inventoryService) GetInventoryBySKUs(ctx context.Context, skus []string) ([]*models.Inventory, error) {
	if len(skus) == 0 {
		return []*models.Inventory{}, nil
	}

	// Validate SKUs
	for i, sku := range skus {
		if sku == "" {
			return nil, fmt.Errorf("SKU at index %d is empty", i)
		}
	}

	// Try to get from cache first (cache-aside pattern)
	var cachedInventories []*models.Inventory
	var missedSKUs []string

	if s.redisClient != nil {
		cachedInventories, missedSKUs = s.getFromCache(ctx, skus)
	} else {
		missedSKUs = skus
	}

	// If we have cache misses, query repository
	var dbInventories []*models.Inventory
	if len(missedSKUs) > 0 {
		var err error
		dbInventories, err = s.repo.GetBySKUs(ctx, missedSKUs)
		if err != nil {
			return nil, fmt.Errorf("failed to query inventories: %w", err)
		}

		// Cache the results from database
		if s.redisClient != nil {
			s.cacheInventories(ctx, dbInventories)
		}
	}

	// Combine cached and database results
	allInventories := append(cachedInventories, dbInventories...)

	// Calculate available quantities for all items
	for _, inventory := range allInventories {
		inventory.CalculateAvailableQuantity()
	}

	return allInventories, nil
}

// GetInventoryBySKU retrieves inventory information for a single SKU
func (s *inventoryService) GetInventoryBySKU(ctx context.Context, sku string) (*models.Inventory, error) {
	if sku == "" {
		return nil, fmt.Errorf("SKU cannot be empty")
	}

	// Query repository
	inventory, err := s.repo.GetBySKU(ctx, sku)
	if err != nil {
		return nil, fmt.Errorf("failed to query inventory for SKU %s: %w", sku, err)
	}

	// Calculate available quantity
	inventory.CalculateAvailableQuantity()

	return inventory, nil
}

// getFromCache retrieves inventories from Redis cache
func (s *inventoryService) getFromCache(ctx context.Context, skus []string) ([]*models.Inventory, []string) {
	var cachedInventories []*models.Inventory
	var missedSKUs []string

	for _, sku := range skus {
		cacheKey := s.getCacheKey(sku)
		cachedData, err := s.redisClient.Get(ctx, cacheKey).Result()

		if err == redis.Nil {
			// Cache miss
			missedSKUs = append(missedSKUs, sku)
		} else if err != nil {
			// Redis error, treat as cache miss
			missedSKUs = append(missedSKUs, sku)
		} else {
			// Cache hit, deserialize
			var inventory models.Inventory
			if err := json.Unmarshal([]byte(cachedData), &inventory); err != nil {
				// Deserialization error, treat as cache miss
				missedSKUs = append(missedSKUs, sku)
			} else {
				cachedInventories = append(cachedInventories, &inventory)
			}
		}
	}

	return cachedInventories, missedSKUs
}

// cacheInventories stores inventories in Redis cache
func (s *inventoryService) cacheInventories(ctx context.Context, inventories []*models.Inventory) {
	for _, inventory := range inventories {
		cacheKey := s.getCacheKey(inventory.SKU)
		data, err := json.Marshal(inventory)
		if err != nil {
			log.Printf("Failed to marshal inventory for caching SKU=%s: %v", inventory.SKU, err)
			continue
		}

		// Set with TTL, ignore errors (cache is optional)
		s.redisClient.Set(ctx, cacheKey, data, s.cacheTTL)
	}
}

// getCacheKey generates a cache key for an inventory SKU
func (s *inventoryService) getCacheKey(sku string) string {
	return fmt.Sprintf("inventory:%s", sku)
}

// InvalidateCache removes inventory from cache (for updates)
func (s *inventoryService) InvalidateCache(ctx context.Context, sku string) error {
	if s.redisClient == nil {
		return nil
	}

	cacheKey := s.getCacheKey(sku)
	return s.redisClient.Del(ctx, cacheKey).Err()
}

// UpdateInventory updates inventory and invalidates cache
func (s *inventoryService) UpdateInventory(ctx context.Context, inventory *models.Inventory) error {
	// Update in repository
	err := s.repo.Update(ctx, inventory)
	if err != nil {
		return fmt.Errorf("failed to update inventory: %w", err)
	}

	// Invalidate cache
	if invalidateErr := s.InvalidateCache(ctx, inventory.SKU); invalidateErr != nil {
		log.Printf("Failed to invalidate cache for SKU=%s after update: %v", inventory.SKU, invalidateErr)
	}

	return nil
}

// ReserveStock reserves stock and invalidates cache
func (s *inventoryService) ReserveStock(ctx context.Context, sku string, quantity int) error {
	// Reserve in repository
	err := s.repo.ReserveStock(ctx, sku, quantity)
	if err != nil {
		return fmt.Errorf("failed to reserve stock: %w", err)
	}

	// Invalidate cache
	if invalidateErr := s.InvalidateCache(ctx, sku); invalidateErr != nil {
		log.Printf("Failed to invalidate cache for SKU=%s after reserve: %v", sku, invalidateErr)
	}

	return nil
}

// ReleaseStock releases stock and invalidates cache
func (s *inventoryService) ReleaseStock(ctx context.Context, sku string, quantity int) error {
	// Release in repository
	err := s.repo.ReleaseStock(ctx, sku, quantity)
	if err != nil {
		return fmt.Errorf("failed to release stock: %w", err)
	}

	// Invalidate cache
	if invalidateErr := s.InvalidateCache(ctx, sku); invalidateErr != nil {
		log.Printf("Failed to invalidate cache for SKU=%s after release: %v", sku, invalidateErr)
	}

	return nil
}

// DeductStock deducts stock and invalidates cache
func (s *inventoryService) DeductStock(ctx context.Context, sku string, quantity int) error {
	// Deduct in repository
	err := s.repo.DeductStock(ctx, sku, quantity)
	if err != nil {
		return fmt.Errorf("failed to deduct stock: %w", err)
	}

	// Invalidate cache
	if invalidateErr := s.InvalidateCache(ctx, sku); invalidateErr != nil {
		log.Printf("Failed to invalidate cache for SKU=%s after deduct: %v", sku, invalidateErr)
	}

	return nil
}
