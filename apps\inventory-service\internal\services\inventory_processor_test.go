package services

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/company/cdh/apps/inventory-service/internal/errors"
	"github.com/company/cdh/apps/inventory-service/models"
)

// MockInventoryRepository is a mock implementation of InventoryRepository
type MockInventoryRepository struct {
	mock.Mock
}

func (m *MockInventoryRepository) GetBySKU(ctx context.Context, sku string) (*models.Inventory, error) {
	args := m.Called(ctx, sku)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Inventory), args.Error(1)
}

func (m *MockInventoryRepository) GetBySKUs(ctx context.Context, skus []string) ([]*models.Inventory, error) {
	args := m.Called(ctx, skus)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.Inventory), args.Error(1)
}

func (m *MockInventoryRepository) Create(ctx context.Context, inventory *models.Inventory) error {
	args := m.Called(ctx, inventory)
	return args.Error(0)
}

func (m *MockInventoryRepository) Update(ctx context.Context, inventory *models.Inventory) error {
	args := m.Called(ctx, inventory)
	return args.Error(0)
}

func (m *MockInventoryRepository) ReserveStock(ctx context.Context, sku string, quantity int) error {
	args := m.Called(ctx, sku, quantity)
	return args.Error(0)
}

func (m *MockInventoryRepository) ReleaseStock(ctx context.Context, sku string, quantity int) error {
	args := m.Called(ctx, sku, quantity)
	return args.Error(0)
}

func (m *MockInventoryRepository) DeductStock(ctx context.Context, sku string, quantity int) error {
	args := m.Called(ctx, sku, quantity)
	return args.Error(0)
}

func (m *MockInventoryRepository) GetLowStockItems(ctx context.Context, threshold int) ([]*models.Inventory, error) {
	args := m.Called(ctx, threshold)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.Inventory), args.Error(1)
}

// MockInventoryFailureProducer is a mock implementation of InventoryFailureProducer
type MockInventoryFailureProducer struct {
	mock.Mock
}

func (m *MockInventoryFailureProducer) PublishInventoryFailure(orderID, sku string, requestedQty, availableQty int, failureType string) error {
	args := m.Called(orderID, sku, requestedQty, availableQty, failureType)
	return args.Error(0)
}

func (m *MockInventoryFailureProducer) Close() error {
	args := m.Called()
	return args.Error(0)
}

// MockDB is a mock implementation of sql.DB for testing
type MockDB struct {
	mock.Mock
}

func (m *MockDB) BeginTx(ctx context.Context, opts *sql.TxOptions) (*sql.Tx, error) {
	args := m.Called(ctx, opts)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*sql.Tx), args.Error(1)
}

func TestInventoryProcessor_ValidateOrderEvent(t *testing.T) {
	mockRepo := &MockInventoryRepository{}
	mockProducer := &MockInventoryFailureProducer{}
	processor := NewInventoryProcessor(mockRepo, nil, mockProducer)

	tests := []struct {
		name    string
		event   *models.OrderCreatedEvent
		wantErr bool
	}{
		{
			name:    "nil event",
			event:   nil,
			wantErr: true,
		},
		{
			name: "valid event",
			event: &models.OrderCreatedEvent{
				OrderID:    "order-123",
				CustomerID: "customer-456",
				Items: []models.OrderItem{
					{SKU: "LAPTOP-001", Quantity: 1, Price: 999.99},
				},
				Status:    "pending",
				CreatedAt: time.Now(),
			},
			wantErr: false,
		},
		{
			name: "empty order ID",
			event: &models.OrderCreatedEvent{
				OrderID:    "",
				CustomerID: "customer-456",
				Items: []models.OrderItem{
					{SKU: "LAPTOP-001", Quantity: 1, Price: 999.99},
				},
				Status:    "pending",
				CreatedAt: time.Now(),
			},
			wantErr: true,
		},
		{
			name: "empty items",
			event: &models.OrderCreatedEvent{
				OrderID:    "order-123",
				CustomerID: "customer-456",
				Items:      []models.OrderItem{},
				Status:     "pending",
				CreatedAt:  time.Now(),
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := processor.ValidateOrderEvent(tt.event)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestInventoryProcessor_ProcessOrderEvent_ProductNotFound(t *testing.T) {
	mockRepo := &MockInventoryRepository{}
	mockProducer := &MockInventoryFailureProducer{}
	processor := NewInventoryProcessor(mockRepo, nil, mockProducer)

	event := &models.OrderCreatedEvent{
		OrderID:    "order-123",
		CustomerID: "customer-456",
		Items: []models.OrderItem{
			{SKU: "NONEXISTENT-001", Quantity: 1, Price: 999.99},
		},
		Status:    "pending",
		CreatedAt: time.Now(),
	}

	// Mock repository to return product not found
	mockRepo.On("GetBySKU", mock.Anything, "NONEXISTENT-001").Return(nil, errors.ErrInventoryNotFound)

	// Mock producer to expect failure event
	mockProducer.On("PublishInventoryFailure", "order-123", "NONEXISTENT-001", 1, 0, "product_not_found").Return(nil)

	ctx := context.Background()
	err := processor.ProcessOrderEvent(ctx, event)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Product not found")
	mockRepo.AssertExpectations(t)
	mockProducer.AssertExpectations(t)
}

func TestInventoryProcessor_ProcessOrderEvent_InsufficientStock(t *testing.T) {
	mockRepo := &MockInventoryRepository{}
	mockProducer := &MockInventoryFailureProducer{}
	processor := NewInventoryProcessor(mockRepo, nil, mockProducer)

	event := &models.OrderCreatedEvent{
		OrderID:    "order-123",
		CustomerID: "customer-456",
		Items: []models.OrderItem{
			{SKU: "LAPTOP-001", Quantity: 5, Price: 999.99},
		},
		Status:    "pending",
		CreatedAt: time.Now(),
	}

	inventory := &models.Inventory{
		ID:                1,
		SKU:               "LAPTOP-001",
		ProductName:       "Gaming Laptop",
		StockQuantity:     10,
		ReservedQuantity:  8,
		AvailableQuantity: 2, // Only 2 available, but 5 requested
		UnitPrice:         999.99,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	// Mock repository to return inventory with insufficient stock
	mockRepo.On("GetBySKU", mock.Anything, "LAPTOP-001").Return(inventory, nil)

	// Mock producer to expect failure event
	mockProducer.On("PublishInventoryFailure", "order-123", "LAPTOP-001", 5, 2, "out_of_stock").Return(nil)

	ctx := context.Background()
	err := processor.ProcessOrderEvent(ctx, event)

	assert.Error(t, err)
	assert.Equal(t, errors.ErrInsufficientStock, err)
	mockRepo.AssertExpectations(t)
	mockProducer.AssertExpectations(t)
}

func TestInventoryProcessor_ProcessOrderEvent_Success(t *testing.T) {
	mockRepo := &MockInventoryRepository{}
	mockProducer := &MockInventoryFailureProducer{}
	processor := NewInventoryProcessor(mockRepo, nil, mockProducer)

	event := &models.OrderCreatedEvent{
		OrderID:    "order-123",
		CustomerID: "customer-456",
		Items: []models.OrderItem{
			{SKU: "LAPTOP-001", Quantity: 2, Price: 999.99},
			{SKU: "MOUSE-001", Quantity: 1, Price: 29.99},
		},
		Status:    "pending",
		CreatedAt: time.Now(),
	}

	// Mock inventory for first item
	inventory1 := &models.Inventory{
		ID:                1,
		SKU:               "LAPTOP-001",
		ProductName:       "Gaming Laptop",
		StockQuantity:     10,
		ReservedQuantity:  3,
		AvailableQuantity: 7, // Sufficient for 2 requested
		UnitPrice:         999.99,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	// Mock inventory for second item
	inventory2 := &models.Inventory{
		ID:                2,
		SKU:               "MOUSE-001",
		ProductName:       "Gaming Mouse",
		StockQuantity:     50,
		ReservedQuantity:  5,
		AvailableQuantity: 45, // Sufficient for 1 requested
		UnitPrice:         29.99,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	// Mock repository calls
	mockRepo.On("GetBySKU", mock.Anything, "LAPTOP-001").Return(inventory1, nil)
	mockRepo.On("GetBySKU", mock.Anything, "MOUSE-001").Return(inventory2, nil)
	mockRepo.On("DeductStock", mock.Anything, "LAPTOP-001", 2).Return(nil)
	mockRepo.On("DeductStock", mock.Anything, "MOUSE-001", 1).Return(nil)

	ctx := context.Background()
	err := processor.ProcessOrderEvent(ctx, event)

	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
	mockProducer.AssertExpectations(t)
}

func TestInventoryProcessor_ProcessOrderEvent_MultipleItems_PartialFailure(t *testing.T) {
	mockRepo := &MockInventoryRepository{}
	mockProducer := &MockInventoryFailureProducer{}
	processor := NewInventoryProcessor(mockRepo, nil, mockProducer)

	event := &models.OrderCreatedEvent{
		OrderID:    "order-123",
		CustomerID: "customer-456",
		Items: []models.OrderItem{
			{SKU: "LAPTOP-001", Quantity: 2, Price: 999.99},
			{SKU: "MOUSE-001", Quantity: 10, Price: 29.99}, // This will fail - insufficient stock
		},
		Status:    "pending",
		CreatedAt: time.Now(),
	}

	// Mock inventory for first item (sufficient stock)
	inventory1 := &models.Inventory{
		ID:                1,
		SKU:               "LAPTOP-001",
		ProductName:       "Gaming Laptop",
		StockQuantity:     10,
		ReservedQuantity:  3,
		AvailableQuantity: 7, // Sufficient for 2 requested
		UnitPrice:         999.99,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	// Mock inventory for second item (insufficient stock)
	inventory2 := &models.Inventory{
		ID:                2,
		SKU:               "MOUSE-001",
		ProductName:       "Gaming Mouse",
		StockQuantity:     50,
		ReservedQuantity:  45,
		AvailableQuantity: 5, // Insufficient for 10 requested
		UnitPrice:         29.99,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	// Mock repository calls - the processor will check both items before deducting any
	mockRepo.On("GetBySKU", mock.Anything, "LAPTOP-001").Return(inventory1, nil)
	mockRepo.On("GetBySKU", mock.Anything, "MOUSE-001").Return(inventory2, nil)

	// Mock producer to expect failure event for second item
	mockProducer.On("PublishInventoryFailure", "order-123", "MOUSE-001", 10, 5, "out_of_stock").Return(nil)

	// Note: DeductStock should not be called for any item since the transaction should fail early

	ctx := context.Background()
	err := processor.ProcessOrderEvent(ctx, event)

	assert.Error(t, err)
	assert.Equal(t, errors.ErrInsufficientStock, err)
	mockRepo.AssertExpectations(t)
	mockProducer.AssertExpectations(t)
}
