package handlers

import (
	"database/sql"
	"encoding/json"
	"log"
	"net/http"

	"github.com/company/cdh/apps/user-service/database"
	"github.com/company/cdh/apps/user-service/models"
	"golang.org/x/crypto/bcrypt"
)

// RegisterHandler handles user registration requests
func RegisterHandler(w http.ResponseWriter, r *http.Request) {
	// Set content type
	w.Header().Set("Content-Type", "application/json")

	// Only allow POST method
	if r.Method != http.MethodPost {
		respondWithError(w, http.StatusMethodNotAllowed, models.ErrorCodeMethodNotAllowed, "Only POST method is allowed")
		return
	}

	// Log registration attempt
	log.Printf("Registration attempt from %s", r.RemoteAddr)

	// Parse and validate request
	req, err := parseRegistrationRequest(r)
	if err != nil {
		log.Printf("Failed to parse registration request: %v", err)
		respondWithError(w, http.StatusBadRequest, models.ErrorCodeInvalidRequest, "Invalid JSON format")
		return
	}

	// Sanitize input
	req.Sanitize()

	// Validate request
	if err := req.Validate(); err != nil {
		log.Printf("Registration validation failed: %v", err)
		respondWithError(w, http.StatusBadRequest, models.ErrorCodeValidation, err.Error())
		return
	}

	// Hash password
	hashedPassword, err := hashPassword(req.Password)
	if err != nil {
		log.Printf("Failed to hash password: %v", err)
		respondWithError(w, http.StatusInternalServerError, models.ErrorCodeInternalError, "Failed to process registration")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		log.Printf("Database connection not available")
		respondWithError(w, http.StatusInternalServerError, models.ErrorCodeInternalError, "Database connection not available")
		return
	}

	// Check for existing user
	if exists, err := checkUserExists(db, req.Username, req.Email); err != nil {
		log.Printf("Database error checking existing user: %v", err)
		respondWithError(w, http.StatusInternalServerError, models.ErrorCodeInternalError, "Failed to check existing user")
		return
	} else if exists {
		log.Printf("Registration failed: username '%s' or email '%s' already exists", req.Username, req.Email)
		respondWithError(w, http.StatusConflict, models.ErrorCodeUserExists, "Username or email already exists")
		return
	}

	// Create user
	userID, err := createUser(db, req.Username, req.Email, hashedPassword)
	if err != nil {
		log.Printf("Failed to create user: %v", err)
		respondWithError(w, http.StatusInternalServerError, models.ErrorCodeInternalError, "Failed to create user")
		return
	}

	// Log successful registration
	log.Printf("User registered successfully: ID=%d, Username=%s, Email=%s", userID, req.Username, req.Email)

	// Return success response
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(models.UserRegistrationResponse{
		ID:       userID,
		Username: req.Username,
		Email:    req.Email,
		Message:  "User registered successfully",
	})
}

// parseRegistrationRequest parses the JSON request body
func parseRegistrationRequest(r *http.Request) (*models.UserRegistrationRequest, error) {
	var req models.UserRegistrationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		return nil, err
	}
	return &req, nil
}

// hashPassword hashes a password using bcrypt
func hashPassword(password string) (string, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedPassword), nil
}

// checkUserExists checks if a user with the given username or email already exists
func checkUserExists(db *sql.DB, username, email string) (bool, error) {
	var existingID int
	checkQuery := "SELECT id FROM users WHERE username = $1 OR email = $2"
	err := db.QueryRow(checkQuery, username, email).Scan(&existingID)

	if err == sql.ErrNoRows {
		return false, nil // User doesn't exist
	}
	if err != nil {
		return false, err // Database error
	}
	return true, nil // User exists
}

// createUser inserts a new user into the database
func createUser(db *sql.DB, username, email, passwordHash string) (int, error) {
	insertQuery := `
		INSERT INTO users (username, email, password_hash)
		VALUES ($1, $2, $3)
		RETURNING id`

	var userID int
	err := db.QueryRow(insertQuery, username, email, passwordHash).Scan(&userID)
	if err != nil {
		return 0, err
	}
	return userID, nil
}

// respondWithError sends a standardized error response
func respondWithError(w http.ResponseWriter, statusCode int, errorCode models.ErrorCode, message string) {
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(models.NewErrorResponse(errorCode, message))
}
