package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUserLoginRequestValidate(t *testing.T) {
	tests := []struct {
		name        string
		request     UserLoginRequest
		expectError bool
		errorField  string
	}{
		{
			name: "Valid request",
			request: UserLoginRequest{
				Username: "testuser",
				Password: "password123",
			},
			expectError: false,
		},
		{
			name: "Missing username",
			request: UserLoginRequest{
				Username: "",
				Password: "password123",
			},
			expectError: true,
			errorField:  "username",
		},
		{
			name: "Missing password",
			request: UserLoginRequest{
				Username: "testuser",
				Password: "",
			},
			expectError: true,
			errorField:  "password",
		},
		{
			name: "Both missing",
			request: UserLoginRequest{
				Username: "",
				Password: "",
			},
			expectError: true,
			errorField:  "username", // First validation error
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.request.Validate()

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" {
					validationErr, ok := err.(*ValidationError)
					assert.True(t, ok, "Expected ValidationError")
					assert.Equal(t, tt.errorField, validationErr.Field)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestUserLoginRequestSanitize(t *testing.T) {
	tests := []struct {
		name             string
		input            UserLoginRequest
		expectedUsername string
		expectedPassword string
	}{
		{
			name: "Trim and lowercase username",
			input: UserLoginRequest{
				Username: "  TestUser  ",
				Password: "password123",
			},
			expectedUsername: "testuser",
			expectedPassword: "password123",
		},
		{
			name: "Username with mixed case",
			input: UserLoginRequest{
				Username: "TestUSER",
				Password: "  password with spaces  ",
			},
			expectedUsername: "testuser",
			expectedPassword: "  password with spaces  ", // Password not trimmed
		},
		{
			name: "Already clean input",
			input: UserLoginRequest{
				Username: "testuser",
				Password: "password123",
			},
			expectedUsername: "testuser",
			expectedPassword: "password123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			request := tt.input
			request.Sanitize()

			assert.Equal(t, tt.expectedUsername, request.Username)
			assert.Equal(t, tt.expectedPassword, request.Password)
		})
	}
}

func TestNewErrorResponse(t *testing.T) {
	tests := []struct {
		name         string
		code         ErrorCode
		message      string
		expectedCode string
	}{
		{
			name:         "Authentication failed error",
			code:         ErrorCodeAuthenticationFailed,
			message:      "Invalid credentials",
			expectedCode: "authentication_failed",
		},
		{
			name:         "Validation error",
			code:         ErrorCodeValidation,
			message:      "Username is required",
			expectedCode: "validation_error",
		},
		{
			name:         "Internal error",
			code:         ErrorCodeInternalError,
			message:      "Database connection failed",
			expectedCode: "internal_error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			response := NewErrorResponse(tt.code, tt.message)

			assert.Equal(t, tt.expectedCode, response.Error)
			assert.Equal(t, tt.message, response.Message)
		})
	}
}

func TestValidationError(t *testing.T) {
	err := &ValidationError{
		Field:   "username",
		Message: "Username is required",
	}

	assert.Equal(t, "Username is required", err.Error())
}

func TestUserRegistrationRequestValidate(t *testing.T) {
	tests := []struct {
		name        string
		request     UserRegistrationRequest
		expectError bool
	}{
		{
			name: "Valid request",
			request: UserRegistrationRequest{
				Username: "testuser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			expectError: false,
		},
		{
			name: "Username too short",
			request: UserRegistrationRequest{
				Username: "ab",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			expectError: true,
		},
		{
			name: "Invalid email",
			request: UserRegistrationRequest{
				Username: "testuser",
				Email:    "invalid-email",
				Password: "password123",
			},
			expectError: true,
		},
		{
			name: "Password too short",
			request: UserRegistrationRequest{
				Username: "testuser",
				Email:    "<EMAIL>",
				Password: "short",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.request.Validate()

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestIsValidEmail(t *testing.T) {
	tests := []struct {
		name  string
		email string
		valid bool
	}{
		{
			name:  "Valid email",
			email: "<EMAIL>",
			valid: true,
		},
		{
			name:  "Valid email with subdomain",
			email: "<EMAIL>",
			valid: true,
		},
		{
			name:  "Missing @",
			email: "testexample.com",
			valid: false,
		},
		{
			name:  "Multiple @",
			email: "test@@example.com",
			valid: false,
		},
		{
			name:  "Missing domain",
			email: "test@",
			valid: false,
		},
		{
			name:  "Missing local part",
			email: "@example.com",
			valid: false,
		},
		{
			name:  "No dot in domain",
			email: "test@example",
			valid: false,
		},
		{
			name:  "Too short",
			email: "a@b",
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// We need to test the private function through a public interface
			request := UserRegistrationRequest{
				Username: "testuser",
				Email:    tt.email,
				Password: "password123",
			}

			err := request.Validate()
			if tt.valid {
				// If email is valid, there should be no email-related error
				if err != nil {
					validationErr, ok := err.(*ValidationError)
					assert.True(t, ok)
					assert.NotEqual(t, "email", validationErr.Field, "Should not have email validation error for valid email")
				}
			} else {
				// If email is invalid, there should be an email validation error
				assert.Error(t, err)
				validationErr, ok := err.(*ValidationError)
				assert.True(t, ok)
				assert.Equal(t, "email", validationErr.Field)
			}
		})
	}
}
