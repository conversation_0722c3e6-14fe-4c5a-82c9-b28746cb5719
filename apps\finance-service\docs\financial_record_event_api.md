# Financial Record Event API Documentation

## Overview

The CDH Finance Service publishes standardized financial events to enable external accounting systems to consume financial data without understanding CDH's internal data structures.

## Event Topic

**Kafka Topic**: `financial.records.created`

## Event Schema

### FinancialRecordEvent Structure

```json
{
  "transaction_id": "string",
  "order_reference": "string", 
  "revenue_amount": "number",
  "tax_amount": "number",
  "currency": "string",
  "timestamp": "string (ISO 8601)",
  "metadata": {
    "payment_method": "string",
    "transaction_type": "string",
    "description": "string",
    "source_service": "string",
    "event_version": "string"
  }
}
```

### Field Descriptions

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `transaction_id` | string | Yes | Unique identifier for the financial transaction |
| `order_reference` | string | Yes | Reference to the original order |
| `revenue_amount` | number | Yes | Revenue amount excluding tax (≥ 0) |
| `tax_amount` | number | Yes | Tax amount (≥ 0) |
| `currency` | string | Yes | ISO 4217 currency code (e.g., "MYR", "USD") |
| `timestamp` | string | Yes | ISO 8601 timestamp when record was created |
| `metadata` | object | Yes | Additional context information |

### Metadata Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `payment_method` | string | No | Payment method used (e.g., "credit_card", "bank_transfer") |
| `transaction_type` | string | No | Type of transaction (e.g., "sale", "refund") |
| `description` | string | No | Human-readable transaction description |
| `source_service` | string | Yes | Service that generated the record |
| `event_version` | string | Yes | Schema version (e.g., "1.0") |

## Example Event

```json
{
  "transaction_id": "txn-*********",
  "order_reference": "order-*********",
  "revenue_amount": 100.00,
  "tax_amount": 6.00,
  "currency": "MYR",
  "timestamp": "2023-12-25T10:30:00Z",
  "metadata": {
    "payment_method": "credit_card",
    "transaction_type": "sale",
    "description": "Product purchase - Widget A",
    "source_service": "finance-service",
    "event_version": "1.0"
  }
}
```

## Consumption Guidelines

### Kafka Consumer Configuration

- **Consumer Group**: Use a unique consumer group ID for your application
- **Auto Offset Reset**: Set to `earliest` to process all historical events
- **Enable Auto Commit**: Recommended for most use cases

### Error Handling

- Events are published with at-least-once delivery guarantee
- Implement idempotent processing using `transaction_id` as deduplication key
- Handle duplicate events gracefully

### Schema Evolution

- The `event_version` field indicates the schema version
- New optional fields may be added to `metadata` without version changes
- Breaking changes will increment the major version number
- Subscribe to schema change notifications for updates

## Integration Examples

### Java (Spring Kafka)

```java
@KafkaListener(topics = "financial.records.created")
public void handleFinancialRecord(String eventJson) {
    try {
        FinancialRecordEvent event = objectMapper.readValue(eventJson, FinancialRecordEvent.class);
        processFinancialRecord(event);
    } catch (Exception e) {
        log.error("Failed to process financial record event", e);
    }
}
```

### Python (kafka-python)

```python
from kafka import KafkaConsumer
import json

consumer = KafkaConsumer(
    'financial.records.created',
    bootstrap_servers=['localhost:9092'],
    value_deserializer=lambda x: json.loads(x.decode('utf-8'))
)

for message in consumer:
    event = message.value
    process_financial_record(event)
```

### Node.js (kafkajs)

```javascript
const { Kafka } = require('kafkajs');

const kafka = Kafka({ clientId: 'accounting-system', brokers: ['localhost:9092'] });
const consumer = kafka.consumer({ groupId: 'accounting-group' });

await consumer.subscribe({ topic: 'financial.records.created' });
await consumer.run({
  eachMessage: async ({ message }) => {
    const event = JSON.parse(message.value.toString());
    await processFinancialRecord(event);
  },
});
```

## Support

For technical support or schema questions, contact the CDH development team.

## Changelog

| Version | Date | Changes |
|---------|------|---------|
| 1.0 | 2025-08-02 | Initial schema release |
