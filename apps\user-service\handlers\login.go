package handlers

import (
	"database/sql"
	"encoding/json"
	"log"
	"net/http"

	"github.com/company/cdh/apps/user-service/auth"
	"github.com/company/cdh/apps/user-service/database"
	"github.com/company/cdh/apps/user-service/models"
	"golang.org/x/crypto/bcrypt"
)

// LoginHandler handles user login requests
func LoginHandler(w http.ResponseWriter, r *http.Request) {
	// Set content type
	w.Header().Set("Content-Type", "application/json")

	// Only allow POST method
	if r.Method != http.MethodPost {
		respondWithError(w, http.StatusMethodNotAllowed, models.ErrorCodeMethodNotAllowed, "Only POST method is allowed")
		return
	}

	// Log login attempt
	log.Printf("Login attempt from %s", r.RemoteAddr)

	// Parse and validate request
	req, err := parseLoginRequest(r)
	if err != nil {
		log.Printf("Failed to parse login request: %v", err)
		respondWithError(w, http.StatusBadRequest, models.ErrorCodeInvalidRequest, "Invalid JSON format")
		return
	}

	// Sanitize input
	req.Sanitize()

	// Validate request
	if err := req.Validate(); err != nil {
		log.Printf("Login validation failed: %v", err)
		respondWithError(w, http.StatusBadRequest, models.ErrorCodeValidation, err.Error())
		return
	}

	// Authenticate user
	user, err := authenticateUser(req.Username, req.Password)
	if err != nil {
		log.Printf("Authentication failed for user %s: %v", req.Username, err)
		respondWithError(w, http.StatusUnauthorized, models.ErrorCodeAuthenticationFailed, "Invalid username or password")
		return
	}

	// Generate JWT token
	jwtConfig := auth.NewJWTConfig()
	token, err := jwtConfig.GenerateToken(user.ID, "user") // Default role for now
	if err != nil {
		log.Printf("Failed to generate JWT token for user %d: %v", user.ID, err)
		respondWithError(w, http.StatusInternalServerError, models.ErrorCodeInternalError, "Failed to generate authentication token")
		return
	}

	// Create response
	response := models.UserLoginResponse{
		Token:   token,
		UserID:  user.ID,
		Message: "Login successful",
	}

	// Send response
	w.WriteHeader(http.StatusOK)
	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("Failed to encode login response: %v", err)
		return
	}

	log.Printf("User %s (ID: %d) logged in successfully", user.Username, user.ID)
}

// parseLoginRequest parses the login request from HTTP request body
func parseLoginRequest(r *http.Request) (*models.UserLoginRequest, error) {
	var req models.UserLoginRequest

	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Strict JSON parsing

	if err := decoder.Decode(&req); err != nil {
		return nil, err
	}

	return &req, nil
}

// authenticateUser validates user credentials against the database
func authenticateUser(username, password string) (*models.User, error) {
	db := database.GetDB()
	if db == nil {
		return nil, &AuthenticationError{Message: "Database connection not available"}
	}

	// Query user by username
	query := `SELECT id, username, email, password_hash, created_at, updated_at
			  FROM users WHERE username = $1`

	var user models.User
	err := db.QueryRow(query, username).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.PasswordHash,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, &AuthenticationError{Message: "User not found"}
		}
		return nil, err
	}

	// Verify password
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password))
	if err != nil {
		return nil, &AuthenticationError{Message: "Invalid password"}
	}

	return &user, nil
}

// AuthenticationError represents an authentication error
type AuthenticationError struct {
	Message string
}

func (e *AuthenticationError) Error() string {
	return e.Message
}
