package consumer

import (
	"context"
	"encoding/json"
	"log"

	"github.com/IBM/sarama"

	"github.com/company/cdh/apps/inventory-service/internal/config"
	"github.com/company/cdh/apps/inventory-service/internal/errors"
	"github.com/company/cdh/apps/inventory-service/internal/services"
	"github.com/company/cdh/apps/inventory-service/models"
)

// KafkaConsumer represents a Kafka consumer for order events
type KafkaConsumer struct {
	consumer sarama.ConsumerGroup
	config   config.KafkaConfig
	handler  *OrderEventHandler
}

// OrderEventHandler handles order events
type OrderEventHandler struct {
	ready     chan bool
	processor services.OrderEventProcessor
}

// NewKafkaConsumer creates a new Kafka consumer
func NewKafkaConsumer(cfg config.KafkaConfig, processor services.OrderEventProcessor) (*KafkaConsumer, error) {
	config := sarama.NewConfig()
	config.Consumer.Group.Rebalance.Strategy = sarama.NewBalanceStrategyRoundRobin()
	config.Consumer.Offsets.Initial = sarama.OffsetOldest
	config.Consumer.Group.Session.Timeout = cfg.SessionTimeout
	config.Consumer.Group.Heartbeat.Interval = cfg.HeartbeatInterval

	consumer, err := sarama.NewConsumerGroup(cfg.Brokers, cfg.GroupID, config)
	if err != nil {
		return nil, errors.NewKafkaError("create consumer group", err)
	}

	handler := &OrderEventHandler{
		ready:     make(chan bool),
		processor: processor,
	}

	return &KafkaConsumer{
		consumer: consumer,
		config:   cfg,
		handler:  handler,
	}, nil
}

// Start starts the Kafka consumer
func (kc *KafkaConsumer) Start(ctx context.Context) error {
	topics := []string{kc.config.Topic}

	for {
		select {
		case <-ctx.Done():
			log.Println("Kafka consumer context cancelled")
			return nil
		default:
			if err := kc.consumer.Consume(ctx, topics, kc.handler); err != nil {
				log.Printf("Error from consumer: %v", err)
				return errors.NewKafkaError("consume messages", err)
			}
		}
	}
}

// Close closes the Kafka consumer
func (kc *KafkaConsumer) Close() error {
	return kc.consumer.Close()
}

// Setup is run at the beginning of a new session, before ConsumeClaim
func (h *OrderEventHandler) Setup(sarama.ConsumerGroupSession) error {
	close(h.ready)
	return nil
}

// Cleanup is run at the end of a session, once all ConsumeClaim goroutines have exited
func (h *OrderEventHandler) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim must start a consumer loop of ConsumerGroupClaim's Messages()
func (h *OrderEventHandler) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		select {
		case message := <-claim.Messages():
			if message == nil {
				return nil
			}

			log.Printf("Received message from topic %s, partition %d, offset %d",
				message.Topic, message.Partition, message.Offset)

			if err := h.processOrderEvent(session.Context(), message.Value); err != nil {
				log.Printf("Error processing order event: %v", err)
				// Continue processing other messages even if one fails
			} else {
				// Mark message as processed
				session.MarkMessage(message, "")
			}

		case <-session.Context().Done():
			return nil
		}
	}
}

// processOrderEvent processes an order created event
func (h *OrderEventHandler) processOrderEvent(ctx context.Context, data []byte) error {
	var event models.OrderCreatedEvent
	if err := json.Unmarshal(data, &event); err != nil {
		log.Printf("Failed to unmarshal order event: %v", err)
		return errors.NewKafkaError("unmarshal order event", err)
	}

	// Validate the event structure
	if err := h.processor.ValidateOrderEvent(&event); err != nil {
		log.Printf("Invalid order event: %v", err)
		return errors.NewKafkaError("validate order event", err)
	}

	log.Printf("Processing order event: OrderID=%s, CustomerID=%s, Items=%d",
		event.OrderID, event.CustomerID, len(event.Items))

	// Log each item in the order for debugging
	for i, item := range event.Items {
		log.Printf("  Item %d: SKU=%s, Quantity=%d, Price=%.2f",
			i+1, item.SKU, item.Quantity, item.Price)
	}

	// Process the order event to deduct inventory
	if err := h.processor.ProcessOrderEvent(ctx, &event); err != nil {
		log.Printf("Failed to process order event for order %s: %v", event.OrderID, err)
		return err
	}

	log.Printf("Order event processed successfully for order %s", event.OrderID)
	return nil
}
