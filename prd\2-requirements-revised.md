# 2. Requirements (Revised)

### Functional Requirements (FR)
* **FR1**: The API Gateway must provide a unified, secure, and well-documented API entry point for all **future** external systems (POS, WMS, e-commerce, etc.).
* **FR2**: The system must be able to manage roles and permissions for different systems (users), e.g., a POS terminal can only call APIs related to orders and inventory queries.
* **FR3**: The Inventory Service must act as the single source of truth for omni-channel inventory, providing atomic APIs for querying, adding, deducting, and locking stock.
* **FR4**: The Order Service must be able to uniformly process orders from all channels and publish an "Order Created" event via the message queue upon successful order creation.
* **FR5**: The Inventory Service must subscribe to the "Order Created" event and automatically and asynchronously deduct the corresponding inventory based on the order content.
* **FR6**: The Finance Service must subscribe to the "Order Created" event and automatically and asynchronously generate preliminary financial entries based on the order content.
* **FR7**: The Finance Service must provide standardized financial event publishing and REST API interfaces for external accounting platform integration, enabling separation of concerns between transactional data processing and tax compliance.
* **FR8**: The User & Permissions Service must provide functionalities for user registration, login, and API Key/JWT management.
* **FR9**: **All externally exposed APIs must have clear, accurate documentation with examples for use by future frontend and system developers.**

### Non-Functional Requirements (NFR)
* **NFR1**: **Performance**: All user-facing API response times should be under 500ms for 95% of requests, and the system must handle high concurrency during e-commerce sales peaks.
* **NFR2**: **Scalability**: Each microservice must be stateless and containerized (using Docker) to allow for independent horizontal scaling via Kubernetes.
* **NFR3**: **Reliability**: Communication between services should prioritize asynchronous message queues (e.g., Kafka) to achieve service decoupling and improve system fault tolerance and overall reliability.
* **NFR4**: **Data Isolation**: Each microservice must have its own independent database to ensure data model independence and service autonomy.
* **NFR5**: **Observability**: The system must provide comprehensive logging, monitoring, and alerting capabilities. Logs from all microservices should be centrally managed (e.g., using EFK), and key business metrics and system health should be monitored (e.g., using Prometheus & Grafana).
* **NFR6**: **Security**: The API Gateway must enforce HTTPS and provide basic authentication, authorization, rate limiting, and firewall functionalities. Sensitive data (like API keys, database passwords) must not be hardcoded.

