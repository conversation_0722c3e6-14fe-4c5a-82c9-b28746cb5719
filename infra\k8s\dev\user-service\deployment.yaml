apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: cdh-dev
  labels:
    app: user-service
    component: api
    environment: development
    project: cdh
spec:
  replicas: 1
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
        component: api
        environment: development
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 65532  # nonroot user from distroless image
        runAsGroup: 65532
        fsGroup: 65532
      containers:
      - name: user-service
        image: user-service:latest
        imagePullPolicy: Never  # Use local image for development
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ENVIRONMENT
          value: "development"
        - name: LOG_LEVEL
          value: "debug"
        - name: DB_HOST
          value: "localhost"
        - name: DB_PORT
          value: "5432"
        - name: DB_USER
          value: "postgres"
        - name: DB_PASSWORD
          value: "postgres"
        - name: DB_NAME
          value: "user_db"
        - name: DB_SSLMODE
          value: "disable"
        - name: JWT_SECRET_KEY
          value: "dev-secret-key-change-in-production"
        - name: JWT_EXPIRATION_HOURS
          value: "24"
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true  # Distroless image supports read-only filesystem
          runAsNonRoot: true
          runAsUser: 65532
          capabilities:
            drop:
            - ALL
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
