# 中央数据中心 (CDH) 产品需求文档 (PRD)

## 1. 目标与背景上下文

### 目标
* **合规性**：建立标准化财务数据基础，为未来与专业会计平台的集成做准备，支持灵活的税务合规解决方案。
* **效率提升**：自动化处理订单到财务的对账流程，显著减少人工操作时间。
* **数据准确性**：整合全渠道库存数据，消除数据孤岛，为所有系统提供单一、准确的库存数据来源。
* **系统性能**：为所有集成的外部系统（POS、WMS、电商平台）提供一个高性能、高可用的统一 API 入口。
* **可扩展性**：构建一个模块化的微服务基础，为未来增加新功能（如会员、营销服务）提供支持。

### 背景上下文
本项目旨在解决当前因 POS、WMS 和电商等核心业务系统相互独立而导致的数据不一致、运营效率低下等问题。通过创建一个中央数据中心 (CDH)，我们将建立一个全渠道数据的“单一真实来源”，从而优化库存管理、简化财务流程。

重要的架构决策是采用职责分离原则：CDH 专注于高效的交易数据处理，而将税务合规和电子发票等复杂功能交由专业的会计平台处理。这种设计确保了系统的可维护性和灵活性。此 PRD 将详细定义构建这一核心平台所需的功能与技术要求。

### 变更日志

| 日期 | 版本 | 描述 | 作者 |
| :--- | :--- | :--- | :--- |
| 2025-08-01 | 1.0 | 初始草稿创建 | John, PM |

## 2. 需求 (已修订)

### 功能性需求 (Functional Requirements)
* **FR1**: API 网关必须为**未来的** POS、WMS、电商等系统提供一个统一、安全、文档完备的 API 入口。
* **FR2**: 系统必须能管理不同系统（用户）的角色和权限，例如，POS 终端只能调用订单和库存查询相关的 API。
* **FR3**: 库存服务必须作为全渠道库存的唯一数据来源，提供原子性的库存查询、增加、扣减和锁定操作的 API。
* **FR4**: 订单服务必须能够统一处理来自所有渠道的订单，并在成功创建订单后，通过消息队列发布“新订单”事件。
* **FR5**: 库存服务必须订阅“新订单”事件，并根据订单内容自动、异步地扣减相应库存。
* **FR6**: 财务服务必须订阅“新订单”事件，并根据订单内容自动、异步地生成初步的财务分录。
* **FR7**: 财务服务必须提供标准化的财务事件发布和 REST API 接口，支持外部会计平台集成，实现交易数据处理与税务合规的职责分离。
* **FR8**: 用户与权限服务必须提供用户注册、登录和 API 密钥/JWT 管理的功能。
* **FR9**: **所有对外暴露的 API 都必须有清晰、准确且包含示例的文档，以供未来前端和系统开发者使用。**

### 非功能性需求 (Non-Functional Requirements)
* **NFR1**: **性能**: 所有面向用户的 API 响应时间在 95% 的情况下应低于 500ms，系统必须能处理电商大促期间的高并发请求。
* **NFR2**: **可扩展性**: 每个微服务都必须是无状态的，并被容器化（使用 Docker），以便通过 Kubernetes 进行独立的水平扩展。
* **NFR3**: **可靠性**: 服务间的通信应优先使用异步消息队列（如 Kafka），以实现服务解耦，提高系统的容错能力和整体可靠性。
* **NFR4**: **数据隔离**: 每个微服务必须拥有其独立的数据库，以确保数据模型的独立性和服务的自治性。
* **NFR5**: **可观测性**: 系统必须提供全面的日志、监控和警报能力。应集中管理所有微服务的日志（如使用 EFK），并对关键业务指标和系统健康状况进行监控（如使用 Prometheus & Grafana）。
* **NFR6**: **安全性**: API 网关必须强制执行 HTTPS，并提供基础的认证、授权、限流和防火墙功能。敏感数据（如 API 密钥、数据库密码）不得硬编码在代码中。

## 3. 用户界面设计目标

### 整体 UX (用户体验) 愿景
* 未来的前端应用应是**高效、直观且响应迅速**的。**“简易、方便、一看就明白的现代设计”** 将作为核心设计原则。对于 POS 和 WMS 这类高频操作的应用，重点是简化工作流程，减少点击次数；对于管理后台，重点是清晰的数据展示和易于操作的管理功能。

### 核心交互范式
* **数据驱动**：界面应实时反映来自 CDH 的数据（如库存、订单状态）。
* **任务导向**：为特定角色（如收银员、仓库管理员）设计高度优化的任务界面。
* **一致性**：所有基于 CDH 开发的前端应用，在涉及核心数据（如产品、订单）的展示和操作上，应遵循一致的模式。

### 核心屏幕与视图 (概念层面)
* **POS 销售终端界面**：用于快速创建订单。
* **WMS 库存管理仪表盘**：用于查看实时库存、处理出入库。
* **电商管理后台**：用于同步订单和库存。
* **CDH 系统管理后台**：用于管理用户、查看系统日志和 API 调用状态。

### 无障碍设计 (Accessibility)
* **目标标准**: WCAG AA。确保未来的前端应用对所有用户都可用，包括有视觉或运动障碍的用户。

### 品牌化
* **要求**: 暂无特定要求。未来的前端应用可以根据其具体定位独立设计品牌风格，但需要有预留位置展示公司 Logo。

### 目标设备与平台
* **平台**: 跨平台 (Cross-Platform)。
* **具体要求**:
    * **WMS 和管理后台**：优先为桌面浏览器设计，需兼容主流浏览器（Chrome, Firefox, Edge）。
    * **POS 系统**：可能需要适配触摸屏设备或平板电脑。
    * 所有应用都应考虑在平板设备上的可用性。

## 4. 技术假设

### 仓库结构 (Repository Structure)
* **单体仓库 (Monorepo)**。

### 服务架构 (Service Architecture)
* **微服务架构 (Microservices Architecture)**。

### 测试要求 (Testing Requirements)
* **单元测试 + 集成测试 (Unit + Integration)**。

### 其他技术假设和要求
* **开发语言**: Go
* **容器化技术**: Docker
* **容器编排**: Kubernetes (K8s)
* **数据库**: PostgreSQL (每个服务独立)
* **缓存**: Redis
* **消息队列**: Kafka
* **API 网关**: Traefik
* **监控与日志**: Prometheus, Grafana, EFK Stack

## 5. 史诗列表 (Epic List)

* **史诗 1: 平台基础与核心服务框架 (Platform Foundation & Core Service Framework)**
    * **目标**: 搭建项目的基本框架，包括使用 K8s 的 CI/CD 自动化部署流程、API 网关 (Traefik) 的配置，并上线第一个核心服务（用户与权限服务）以处理基础认证。
* **史诗 2: 核心交易流的实现 (Core Transactional Flow Implementation)**
    * **目标**: 开发订单服务和库存服务，实现通过 API 接收订单、通过消息队列 (Kafka) 异步更新库存的核心业务流程。
* **史诗 3: 财务数据基础与会计平台集成 (Financial Data Foundation & Accounting Platform Integration)**
    * **目标**: 开发财务服务，建立标准化的财务数据处理能力，创建财务事件流，为未来与专业会计平台的无缝集成奠定基础。
* **史诗 4: 平台可观测性与管理 (Platform Observability & Management)**
    * **目标**: 全面部署并配置监控 (Prometheus & Grafana) 和日志 (EFK) 系统，并创建一个简单的内部管理后台，用于查看系统状态和管理基本配置。

## 6. 史诗详情

### 史诗 1: 平台基础与核心服务框架
**史诗目标**: 此史诗的目标是为整个 CDH 平台搭建一个坚实、自动化且安全的开发与部署基础。在本史诗结束时，我们将拥有一个功能性的 CI/CD 管道、一个配置好的 API 网关，以及一个能够处理用户注册和认证的核心用户服务。这证明了我们技术架构的可行性，并为后续业务功能的开发铺平了道路。

#### 用户故事
**故事 1.1: 项目初始化与仓库设置**
* **作为一个** 开发者, **我想要** 一个标准化的、使用单体仓库 (Monorepo) 模式的项目结构, **以便于** 我能在一个统一的环境中管理所有微服务的代码和依赖。
* **验收标准**:
    1.  Git 仓库已创建。
    2.  仓库中包含符合我们技术选型（Go, Kafka, Traefik）的 Monorepo 目录结构。
    3.  根目录下的 `README.md` 文件已创建，并包含项目简介。

**故事 1.2: 基础 CI/CD 自动化部署管道**
* **作为一个** 平台团队, **我想要** 一个基础的持续集成/持续部署 (CI/CD) 管道, **以便于** 代码提交后能够自动构建、测试并部署一个“Hello World”应用到 Kubernetes 集群。
* **验收标准**:
    1.  CI/CD 配置文件已创建。
    2.  当代码推送到 `main` 分支时，管道能被自动触发。
    3.  管道能成功在 K8s 中部署一个简单的应用，并通过 URL 访问到。

**故事 1.3: API 网关 (Traefik) 初始部署与路由**
* **作为一个** 平台团队, **我想要** 在 Kubernetes 集群中部署并配置好 Traefik, **以便于** 它可以作为所有内部服务的唯一入口，并处理基础的 HTTP 路由。
* **验收标准**:
    1.  Traefik 已成功部署到 K8s 集群。
    2.  Traefik 的管理后台可以访问。
    3.  一个指向“健康检查”端点的基础路由规则已配置并可以正常工作。

**故事 1.4: 用户与权限服务框架搭建**
* **作为一个** 开发者, **我想要** 创建用户与权限服务的基本代码框架，并实现一个健康检查接口, **以便于** 我们可以通过 CI/CD 管道将其部署，并验证服务能通过 API 网关被访问。
* **验收标准**:
    1.  在 `apps/user-service` 目录下已创建 Go 项目结构。
    2.  服务包含一个 `/health` 端点，访问时返回 `{"status": "ok"}`。
    3.  服务的 Dockerfile 已创建，并能成功构建出 Docker 镜像。
    4.  该服务部署后，可以通过 API 网关的路径被成功访问。

**故事 1.5: 实现用户注册功能**
* **作为一个** 未来的系统开发者, **我想要** 调用一个用户注册的 API 端点, **以便于** 为未来的前端应用或系统创建一个新用户。
* **验收标准**:
    1.  用户服务提供一个 `POST /register` 端点。
    2.  该端点接收用户信息，并将加密后的密码和用户信息存入 PostgreSQL 数据库。
    3.  成功创建用户后，返回新用户的 ID。
    4.  如果用户名或邮箱已存在，返回相应的错误信息。

**故事 1.6: 实现用户认证 (JWT) 功能**
* **作为一个** 未来的系统用户, **我想要** 通过用户名和密码进行登录认证，并获取一个访问令牌, **以便于** 我能用这个令牌访问受保护的 API 资源。
* **验收标准**:
    1.  用户服务提供一个 `POST /login` 端点。
    2.  该端点验证用户凭据。
    3.  验证成功后，生成一个包含用户ID和角色的 JWT 并返回给客户端。
    4.  凭据错误时，返回认证失败的错误信息。

**故事 1.7: 实现基础的认证中间件**
* **作为一个** 开发者, **我想要** 一个可以集成到 API 网关或微服务中的认证中间件, **以便于** 保护特定的 API 端点，只允许持有有效 JWT 的请求访问。
* **验收标准**:
    1.  认证中间件已创建。
    2.  当一个受保护的端点被调用时，中间件会验证请求头中的 JWT。
    3.  JWT 有效时，请求被放行到目标服务。
    4.  JWT 无效或缺失时，返回 401 未授权的错误。

### 史诗 2: 核心交易流的实现
**史诗目标**: 此史诗的目标是构建 CDH 平台处理核心业务交易的能力。在本史诗结束时，系统将能够通过 API 接收订单，订单信息会通过 Kafka 可靠地传递给库存服务，并实时、准确地更新库存数量。这将验证我们事件驱动架构的核心模式，并为后续的财务整合奠定数据基础。

#### 用户故事
**故事 2.1: 订单服务框架搭建与 API 定义**
* **作为一个** 开发者, **我想要** 创建订单服务的基本代码框架，并定义用于创建和查询订单的 API 接口, **以便于** 未来的系统可以开始与订单服务进行交互。
* **验收标准**:
    1.  在 `apps/order-service` 目录下已创建 Go 项目结构及其 Dockerfile。
    2.  服务提供一个 `POST /orders` 端点用于创建订单，并提供一个 `GET /orders/{id}` 端点用于查询订单详情。
    3.  订单的数据模型已在代码中定义。
    4.  服务已连接到其独立的 PostgreSQL 数据库。

**故事 2.2: 实现订单创建与事件发布**
* **作为一个** 未来的系统开发者, **我想要** 在调用 `POST /orders` 端点成功创建一个订单后，系统能发布一个“新订单已创建”的事件, **以便于** 其他关心此事件的下游服务可以接收并处理它。
* **验收标准**:
    1.  当 `POST /orders` 成功处理一个请求时，订单数据被正确存入订单服务的数据库。
    2.  订单成功存储后，一个包含完整订单信息的事件被发布到 Kafka 的 `orders.created` 主题中。
    3.  API 调用成功后，返回创建的订单信息及订单 ID。

**故事 2.3: 库存服务框架搭建与 Kafka 消费者**
* **作为一个** 开发者, **我想要** 创建库存服务的基本框架，并实现一个 Kafka 消费者来监听订单事件, **以便于** 库存服务能够接收并准备处理来自订单服务的消息。
* **验收标准**:
    1.  在 `apps/inventory-service` 目录下已创建 Go 项目结构及其 Dockerfile。
    2.  服务已连接到其独立的 PostgreSQL 数据库，并定义了库存数据模型。
    3.  服务中已实现一个 Kafka 消费者，该消费者订阅了 `orders.created` 主题。
    4.  当有新消息时，服务能够成功接收并将其内容打印到日志中。

**故事 2.4: 实现基于订单事件的库存扣减逻辑**
* **作为一个** 库存管理者, **我想要** 系统在每次成功销售一个商品后，自动从总库存中扣减相应的数量, **以便于** 保证所有渠道看到的库存数量都是实时、准确的。
* **验收标准**:
    1.  库存服务的 Kafka 消费者在接收到“新订单已创建”事件后，能成功解析出订单中的商品 SKU 和数量。
    2.  服务会根据解析出的信息，在库存数据库中找到对应的商品并扣减库存量。
    3.  库存更新操作是原子性的。
    4.  如果某个商品库存不足，应记录一个失败事件。

**故事 2.5: 提供库存查询 API**
* **作为一个** 未来的 POS 或电商系统开发者, **我想要** 调用一个 API 来查询一个或多个商品的当前库存水平, **以便于** 在用户下单前展示准确的库存信息，避免超卖。
* **验收标准**:
    1.  库存服务提供一个 `GET /inventory?sku={sku1},{sku2}` 的 API 端点。
    2.  该端点能返回指定商品 SKU 的实时库存量。
    3.  API 响应速度快，引入 Redis 作为查询缓存以优化性能。

### 史诗 3: 财务数据基础与会计平台集成
**史诗目标**: 此史诗的目标是建立 CDH 的财务数据处理能力，创建标准化的财务事件流，为未来与专业会计平台的无缝集成奠定数据基础。在本史诗结束时，平台将能够监听订单事件，自动生成标准化财务记录，并通过事件发布和 API 接口支持外部会计系统的集成。

#### 用户故事
**故事 3.1: 财务服务框架搭建与基础财务记录**
* **作为一个** 开发者, **我想要** 创建财务服务的基本框架，并让它能消费 Kafka 中的订单事件来生成标准化财务记录, **以便于** 财务服务能够为未来会计平台集成提供清洁的财务数据。
* **验收标准**:
    1.  在 `apps/finance-service` 目录下已创建 Go 项目结构及其 Dockerfile。
    2.  服务已连接到其独立的 PostgreSQL 数据库，并定义了财务分录的数据模型。
    3.  服务中已实现一个 Kafka 消费者，该消费者订阅了 `orders.created` 主题。
    4.  当有新订单事件到达时，服务能够成功接收并生成基础财务记录（收入、税金等）。

**故事 3.2: 实现基础的财务分录自动化**
* **作为一个** 财务人员, **我想要** 系统在每一笔订单完成后，自动在财务模块生成对应的会计分录, **以便于** 减少手工记账的工作量，并保证账目的准确性。
* **验收标准**:
    1.  财务服务在处理“新订单已创建”事件后，能根据订单信息自动生成符合会计准则的财务分录。
    2.  生成的财务分录被正确地存储在财务数据库中。

**故事 3.3: 与 LHDN MyInvois API 对接**
* **作为一个** 系统, **我想要** 安全地连接到 LHDN MyInvois 的沙盒环境，并完成认证, **以便于** 为后续的电子发票上传和验证功能做好准备。
* **验收标准**:
    1.  财务服务中已实现一个 LHDN API 客户端模块。
    2.  系统能够使用安全的凭据成功向 LHDN MyInvois 沙盒环境发起认证请求。
    3.  认证成功后获取的访问令牌被妥善管理和刷新。

**故事 3.4: 实现电子发票的自动生成与上传**
* **作为一个** 合规官, **我想要** 系统能根据已确认的订单信息，自动生成符合 LHDN 规范的电子发票，并将其上传至 MyInvois 系统, **以便于** 确保每一笔交易都及时、准确地完成了税务合规操作。
* **验收标准**:
    1.  财务服务能够将内部订单数据转换为 LHDN MyInvois API 要求的指定格式。
    2.  转换后的数据通过 API 客户端成功上传到 LHDN MyInvois 沙盒系统。
    3.  上传成功后，系统会从 LHDN API 接收到一个唯一的发票标识符。
    4.  这个唯一的标识符以及发票的上传状态被存储在我们的财务数据库中。

**故事 3.5: 实现电子发票状态的查询与更新**
* **作为一个** 财务人员, **我想要** 系统能够查询已上传电子发票的最新状态, **以便于** 我能及时处理被拒绝的发票或确认所有发票都已成功验证。
* **验收标准**:
    1.  财务服务提供一个内部接口或定时任务，用于调用 LHDN API 查询指定电子发票的状态。
    2.  查询到的最新状态被更新到我们的数据库中。
    3.  如果发票被拒绝，系统应记录拒绝原因，并创建一个待处理任务。

### 史诗 4: 平台可观测性与管理
**史诗目标**: 此史诗的目标是为 CDH 平台建立全面的“可观测性”能力，并提供基础的管理功能。在本史诗结束时，我们将拥有一个集中的日志系统来追踪问题，一个实时的监控仪表盘来观察系统健康状况，以及一个简单的内部管理后台来管理用户和查看关键配置。

#### 用户故事
**故事 4.1: 部署集中式日志系统 (EFK Stack)**
* **作为一个** 运维工程师, **我想要** 一个能集中收集、存储和查询所有微服务日志的系统, **以便于** 在出现问题时，我能快速地追踪、定位和诊断故障。
* **验收标准**:
    1.  Elasticsearch, Fluentd, 和 Kibana (EFK) 已成功部署到 Kubernetes 集群中。
    2.  所有微服务的日志都被 Fluentd 自动收集并发送到 Elasticsearch。
    3.  开发者可以通过 Kibana 的 Web 界面查询所有日志。

**故事 4.2: 部署监控与警报系统 (Prometheus & Grafana)**
* **作为一个** 运维工程师, **我想要** 一个能够实时监控所有微服务关键性能指标的系统, **以便于** 我能直观地了解系统的健康状况，并在问题发生前收到预警。
* **验收标准**:
    1.  Prometheus 和 Grafana 已成功部署到 Kubernetes 集群中。
    2.  每个 Go 微服务都暴露了一个 `/metrics` 端点。
    3.  Prometheus 能够自动抓取这些指标数据。
    4.  Grafana 中已创建至少一个基础的仪表盘，展示所有服务的核心健康指标。

**故事 4.3: 创建内部管理后台框架**
* **作为一个** 系统管理员, **我想要** 一个基础的、受密码保护的 Web 应用作为内部管理后台, **以便于** 我有一个统一的界面来执行管理操作。
* **验收标准**:
    1.  一个简单的前端应用项目已创建。
    2.  该应用包含一个登录页面，管理员可以使用“管理员”角色的账户登录。
    3.  应用的基础布局已搭建完成。

**故事 4.4: 在管理后台中实现用户管理**
* **作为一个** 系统管理员, **我想要** 在管理后台中查看所有用户列表，并能创建新用户、分配角色, **以便于** 我能方便地管理平台的用户和权限。
* **验收标准**:
    1.  管理后台中有一个“用户管理”页面。
    2.  该页面通过调用用户服务的 API，展示了所有已注册用户的列表。
    3.  页面上有一个表单，可以用来创建新用户并为其分配角色。

## 7. 下一步
### 移交给 UX 专家 (UX Expert)
> “你好 Sally (UX Expert)，请审阅这份 PRD，特别是关于史诗 4 中提到的内部管理后台部分。请基于其中‘简易但现代’的设计原则，为这个后台创建一个基础的 UI/UX 规范 (`front-end-spec.md`)，重点关注用户管理页面的布局和交互流程。”

### 移交给架构师 (Architect)
> “你好 Winston (Architect)，这份 PRD 已经由产品侧最终确认。请基于这份文档中定义的所有功能性需求、非功能性需求、技术假设 以及完整的史诗和故事列表，开始创建详细的系统架构文档 (`architecture.md`)。请确保架构设计能够支持我们规划的每一个故事，并遵循已确认的技术选型。”