package repository

import (
	"context"

	"github.com/company/cdh/apps/inventory-service/models"
)

// InventoryRepository defines the interface for inventory data operations
type InventoryRepository interface {
	// GetBySKU retrieves an inventory item by SKU
	GetBySKU(ctx context.Context, sku string) (*models.Inventory, error)

	// GetBySKUs retrieves multiple inventory items by SKUs
	GetBySKUs(ctx context.Context, skus []string) ([]*models.Inventory, error)

	// Create creates a new inventory item
	Create(ctx context.Context, inventory *models.Inventory) error

	// Update updates an existing inventory item
	Update(ctx context.Context, inventory *models.Inventory) error

	// ReserveStock reserves stock for an order
	ReserveStock(ctx context.Context, sku string, quantity int) error

	// ReleaseStock releases reserved stock
	ReleaseStock(ctx context.Context, sku string, quantity int) error

	// DeductStock deducts stock from inventory
	DeductStock(ctx context.Context, sku string, quantity int) error

	// GetLowStockItems returns items with stock below threshold
	GetLowStockItems(ctx context.Context, threshold int) ([]*models.Inventory, error)
}
