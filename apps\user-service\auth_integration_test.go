package main

import (
	"os"
	"strings"
	"testing"

	"github.com/company/cdh/apps/user-service/auth"
	"github.com/company/cdh/apps/user-service/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestJWTAuthentication tests the complete JWT authentication implementation
func TestJWTAuthentication(t *testing.T) {
	// Set test environment variables
	os.Setenv("JWT_SECRET_KEY", "test-secret-key-for-development")
	os.Setenv("JWT_EXPIRATION_HOURS", "1")

	t.Log("🧪 Testing JWT Authentication Implementation")
	t.Log(strings.Repeat("=", 50))

	// Test 1: JWT Configuration
	t.Run("JWT Configuration", func(t *testing.T) {
		config := auth.NewJWTConfig()
		assert.NotEmpty(t, config.SecretKey, "JWT Secret Key should not be empty")
		assert.NotZero(t, config.ExpirationTime, "JWT Expiration should not be zero")
		t.Logf("✅ JWT Secret Key: %s", config.SecretKey)
		t.Logf("✅ JWT Expiration: %v", config.ExpirationTime)
	})

	// Test 2: JWT Token Generation
	t.Run("JWT Token Generation", func(t *testing.T) {
		config := auth.NewJWTConfig()
		userID := 12 // Test user ID
		token, err := config.GenerateToken(userID, "user")
		require.NoError(t, err, "Failed to generate token")
		assert.NotEmpty(t, token, "Generated token should not be empty")
		
		tokenPreview := token
		if len(token) > 50 {
			tokenPreview = token[:50] + "..."
		}
		t.Logf("✅ Generated JWT Token: %s", tokenPreview)
	})

	// Test 3: JWT Token Validation
	t.Run("JWT Token Validation", func(t *testing.T) {
		config := auth.NewJWTConfig()
		userID := 12
		token, err := config.GenerateToken(userID, "user")
		require.NoError(t, err, "Failed to generate token")
		
		claims, err := config.ValidateToken(token)
		require.NoError(t, err, "Failed to validate token")
		assert.Equal(t, userID, claims.UserID, "User ID should match")
		assert.Equal(t, "user", claims.Role, "Role should match")
		assert.Equal(t, "cdh-user-service", claims.Issuer, "Issuer should match")
		t.Logf("✅ Token validated - User ID: %d, Role: %s, Issuer: %s", claims.UserID, claims.Role, claims.Issuer)
	})

	// Test 4: Login Request Validation
	t.Run("Login Request Validation", func(t *testing.T) {
		// Valid request
		validRequest := models.UserLoginRequest{
			Username: "testuser",
			Password: "password",
		}
		validRequest.Sanitize()
		err := validRequest.Validate()
		require.NoError(t, err, "Valid request should pass validation")
		assert.Equal(t, "testuser", validRequest.Username, "Username should be sanitized")
		t.Logf("✅ Valid login request passed validation")
		t.Logf("✅ Sanitized username: %s", validRequest.Username)

		// Invalid request
		invalidRequest := models.UserLoginRequest{
			Username: "",
			Password: "password",
		}
		err = invalidRequest.Validate()
		assert.Error(t, err, "Invalid request should fail validation")
		t.Logf("✅ Invalid login request correctly failed validation: %v", err)
	})

	// Test 5: Error Response Creation
	t.Run("Error Response Creation", func(t *testing.T) {
		errorResponse := models.NewErrorResponse(models.ErrorCodeAuthenticationFailed, "Invalid credentials")
		assert.Equal(t, "authentication_failed", errorResponse.Error, "Error code should match")
		assert.Equal(t, "Invalid credentials", errorResponse.Message, "Error message should match")
		t.Logf("✅ Error response created: %s - %s", errorResponse.Error, errorResponse.Message)
	})

	t.Log("🎉 All JWT Authentication Tests Passed!")
	t.Log(strings.Repeat("=", 50))
	t.Log("✅ JWT token generation and validation working correctly")
	t.Log("✅ Login request validation working correctly")
	t.Log("✅ Error handling working correctly")
	t.Log("✅ Ready for integration testing with independent database")
}
