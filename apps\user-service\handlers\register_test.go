package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/company/cdh/apps/user-service/database"
	"github.com/company/cdh/apps/user-service/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"golang.org/x/crypto/bcrypt"
)

// setupTestDB sets up a test database connection using environment configuration
func setupTestDB(t *testing.T) {
	// Use environment-based database connection (supports both Supabase and local)
	if err := database.Connect(); err != nil {
		t.Skipf("Failed to connect to database: %v", err)
	}

	// Test the connection
	if err := database.GetDB().Ping(); err != nil {
		t.Skipf("Failed to ping database: %v", err)
	}

	// Run migrations for test
	if err := database.RunMigrations(); err != nil {
		t.Fatalf("Failed to run test migrations: %v", err)
	}
}

// cleanupTestDB cleans up test data
func cleanupTestDB(t *testing.T) {
	db := database.GetDB()
	if db != nil {
		// Clean up test users
		_, err := db.Exec("DELETE FROM users WHERE username LIKE 'test_%' OR email LIKE 'test_%'")
		if err != nil {
			t.Logf("Failed to cleanup test data: %v", err)
		}
	}
}

func TestRegisterHandler_MethodNotAllowed(t *testing.T) {
	req, err := http.NewRequest("GET", "/register", nil)
	require.NoError(t, err)

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(RegisterHandler)

	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusMethodNotAllowed, rr.Code)
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"))

	var response models.ErrorResponse
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "method_not_allowed", response.Error)
}

func TestRegisterHandler_InvalidJSON(t *testing.T) {
	req, err := http.NewRequest("POST", "/register", bytes.NewBufferString("invalid json"))
	require.NoError(t, err)

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(RegisterHandler)

	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusBadRequest, rr.Code)

	var response models.ErrorResponse
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "invalid_request", response.Error)
}

func TestRegisterHandler_ValidationErrors(t *testing.T) {
	testCases := []struct {
		name     string
		request  models.UserRegistrationRequest
		expected string
	}{
		{
			name:     "empty username",
			request:  models.UserRegistrationRequest{Username: "", Email: "<EMAIL>", Password: "password123"},
			expected: "Username is required",
		},
		{
			name:     "short username",
			request:  models.UserRegistrationRequest{Username: "ab", Email: "<EMAIL>", Password: "password123"},
			expected: "Username must be at least 3 characters long",
		},
		{
			name:     "empty email",
			request:  models.UserRegistrationRequest{Username: "testuser", Email: "", Password: "password123"},
			expected: "Email is required",
		},
		{
			name:     "empty password",
			request:  models.UserRegistrationRequest{Username: "testuser", Email: "<EMAIL>", Password: ""},
			expected: "Password is required",
		},
		{
			name:     "short password",
			request:  models.UserRegistrationRequest{Username: "testuser", Email: "<EMAIL>", Password: "123"},
			expected: "Password must be at least 8 characters long",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonData, err := json.Marshal(tc.request)
			require.NoError(t, err)

			req, err := http.NewRequest("POST", "/register", bytes.NewBuffer(jsonData))
			require.NoError(t, err)

			rr := httptest.NewRecorder()
			handler := http.HandlerFunc(RegisterHandler)

			handler.ServeHTTP(rr, req)

			assert.Equal(t, http.StatusBadRequest, rr.Code)

			var response models.ErrorResponse
			err = json.Unmarshal(rr.Body.Bytes(), &response)
			require.NoError(t, err)
			assert.Equal(t, "validation_error", response.Error)
			assert.Equal(t, tc.expected, response.Message)
		})
	}
}

func TestRegisterHandler_SuccessfulRegistration(t *testing.T) {
	setupTestDB(t)
	defer cleanupTestDB(t)

	request := models.UserRegistrationRequest{
		Username: "test_user_success",
		Email:    "<EMAIL>",
		Password: "password123",
	}

	jsonData, err := json.Marshal(request)
	require.NoError(t, err)

	req, err := http.NewRequest("POST", "/register", bytes.NewBuffer(jsonData))
	require.NoError(t, err)

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(RegisterHandler)

	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusCreated, rr.Code)

	var response models.UserRegistrationResponse
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Greater(t, response.ID, 0)
	assert.Equal(t, "test_user_success", response.Username)
	assert.Equal(t, "<EMAIL>", response.Email)
	assert.Equal(t, "User registered successfully", response.Message)

	// Verify password was hashed correctly
	db := database.GetDB()
	var storedHash string
	err = db.QueryRow("SELECT password_hash FROM users WHERE id = $1", response.ID).Scan(&storedHash)
	require.NoError(t, err)

	err = bcrypt.CompareHashAndPassword([]byte(storedHash), []byte("password123"))
	assert.NoError(t, err, "Password should be hashed correctly")
}

func TestRegisterHandler_DuplicateUsername(t *testing.T) {
	setupTestDB(t)
	defer cleanupTestDB(t)

	// First registration
	request1 := models.UserRegistrationRequest{
		Username: "test_duplicate_user",
		Email:    "<EMAIL>",
		Password: "password123",
	}

	jsonData1, err := json.Marshal(request1)
	require.NoError(t, err)

	req1, err := http.NewRequest("POST", "/register", bytes.NewBuffer(jsonData1))
	require.NoError(t, err)

	rr1 := httptest.NewRecorder()
	handler := http.HandlerFunc(RegisterHandler)
	handler.ServeHTTP(rr1, req1)

	assert.Equal(t, http.StatusCreated, rr1.Code)

	// Second registration with same username
	request2 := models.UserRegistrationRequest{
		Username: "test_duplicate_user",
		Email:    "<EMAIL>",
		Password: "password456",
	}

	jsonData2, err := json.Marshal(request2)
	require.NoError(t, err)

	req2, err := http.NewRequest("POST", "/register", bytes.NewBuffer(jsonData2))
	require.NoError(t, err)

	rr2 := httptest.NewRecorder()
	handler.ServeHTTP(rr2, req2)

	assert.Equal(t, http.StatusConflict, rr2.Code)

	var response models.ErrorResponse
	err = json.Unmarshal(rr2.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "user_exists", response.Error)
	assert.Equal(t, "Username or email already exists", response.Message)
}

func TestRegisterHandler_DuplicateEmail(t *testing.T) {
	setupTestDB(t)
	defer cleanupTestDB(t)

	// First registration
	request1 := models.UserRegistrationRequest{
		Username: "test_user_first",
		Email:    "<EMAIL>",
		Password: "password123",
	}

	jsonData1, err := json.Marshal(request1)
	require.NoError(t, err)

	req1, err := http.NewRequest("POST", "/register", bytes.NewBuffer(jsonData1))
	require.NoError(t, err)

	rr1 := httptest.NewRecorder()
	handler := http.HandlerFunc(RegisterHandler)
	handler.ServeHTTP(rr1, req1)

	assert.Equal(t, http.StatusCreated, rr1.Code)

	// Second registration with same email
	request2 := models.UserRegistrationRequest{
		Username: "test_user_second",
		Email:    "<EMAIL>",
		Password: "password456",
	}

	jsonData2, err := json.Marshal(request2)
	require.NoError(t, err)

	req2, err := http.NewRequest("POST", "/register", bytes.NewBuffer(jsonData2))
	require.NoError(t, err)

	rr2 := httptest.NewRecorder()
	handler.ServeHTTP(rr2, req2)

	assert.Equal(t, http.StatusConflict, rr2.Code)

	var response models.ErrorResponse
	err = json.Unmarshal(rr2.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "user_exists", response.Error)
	assert.Equal(t, "Username or email already exists", response.Message)
}

// TestRegisterHandler_ImprovedValidation tests the enhanced validation rules
func TestRegisterHandler_ImprovedValidation(t *testing.T) {
	setupTestDB(t)
	defer cleanupTestDB(t)

	tests := []struct {
		name           string
		requestBody    models.UserRegistrationRequest
		expectedStatus int
		expectedError  string
	}{
		{
			name: "Username too long",
			requestBody: models.UserRegistrationRequest{
				Username: "this_username_is_way_too_long_and_exceeds_fifty_characters_limit",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Username must be less than 50 characters",
		},
		{
			name: "Invalid email format",
			requestBody: models.UserRegistrationRequest{
				Username: "testuser",
				Email:    "invalid-email",
				Password: "password123",
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Email format is invalid",
		},
		{
			name: "Password too short",
			requestBody: models.UserRegistrationRequest{
				Username: "testuser",
				Email:    "<EMAIL>",
				Password: "short",
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Password must be at least 8 characters long",
		},
		{
			name: "Password too long",
			requestBody: models.UserRegistrationRequest{
				Username: "testuser",
				Email:    "<EMAIL>",
				Password: strings.Repeat("a", 129),
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Password must be less than 128 characters",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			body, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/register", bytes.NewBuffer(body))
			w := httptest.NewRecorder()

			RegisterHandler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response models.ErrorResponse
			err := json.NewDecoder(w.Body).Decode(&response)
			require.NoError(t, err)
			assert.Equal(t, tt.expectedError, response.Message)
		})
	}
}
