# 4. Core Service Module Descriptions

### API Gateway (Traefik)
* **Responsibility**: The single entry point for all external requests.
* **Core Functions**:
    * **Routing**: Automatically discovers and forwards requests to the correct backend microservice.
    * **Authentication & Authorization**: Validates JWTs to ensure only authorized users have access.
    * **Security**: Provides SSL/TLS termination, rate limiting, and basic firewall capabilities.

### User & Permissions Service (Go)
* **Responsibility**: Manages all users, roles, and permissions.
* **Core Functions**:
    * Registration, login, and information management for users/system accounts.
    * Generation and validation of API keys and JWTs.
    * Assignment of roles and permissions.
* **Database**: `user_db` (PostgreSQL)

### Order Service (Go)
* **Responsibility**: Uniformly processes and manages orders from all channels.
* **Core Functions**:
    * Provides a `POST /orders` API for creating orders.
    * Provides a `GET /orders/{id}` API for querying orders.
    * Publishes an "Order Created" message to the `orders.created` Kafka topic after an order is created.
* **Database**: `order_db` (PostgreSQL)

### Inventory Service (Go)
* **Responsibility**: The "single source of truth" for all inventory data.
* **Core Functions**:
    * Provides a `GET /inventory` API for querying stock.
    * Listens to the `orders.created` Kafka topic to receive messages and asynchronously deduct inventory.
* **Database**: `inventory_db` (PostgreSQL) + Redis Cache

### Finance Service (Go)
* **Responsibility**: Processes transactional financial data and provides standardized financial records for external accounting platform integration.
* **Core Functions**:
    * Listens to the `orders.created` Kafka topic to automatically generate standardized financial entries.
    * Publishes financial events to `financial.records.created` topic for consumption by external accounting platforms.
    * Provides REST API endpoints for accounting platforms to query financial data and submit processing status.
* **Database**: `finance_db` (PostgreSQL)

