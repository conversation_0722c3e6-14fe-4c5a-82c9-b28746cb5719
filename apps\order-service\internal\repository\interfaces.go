package repository

import "github.com/company/cdh/apps/order-service/models"

// OrderRepository defines the interface for order data operations
type OrderRepository interface {
	CreateOrder(req *models.OrderRequest) (*models.Order, error)
	GetOrderByID(id int) (*models.Order, error)
	GetOrderByNumber(orderNumber string) (*models.Order, error)
	UpdateOrderStatus(id int, status models.OrderStatus) error
}