{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://cdh.company.com/schemas/financial-record-event/v1.0", "title": "Financial Record Event", "description": "Standardized financial event schema for external accounting systems", "type": "object", "required": ["transaction_id", "order_reference", "revenue_amount", "tax_amount", "currency", "timestamp", "metadata"], "properties": {"transaction_id": {"type": "string", "description": "Unique identifier for the financial transaction", "pattern": "^[a-zA-Z0-9-_]+$", "minLength": 1, "maxLength": 100, "examples": ["txn-123456", "TXN_ABC_789"]}, "order_reference": {"type": "string", "description": "Reference to the original order that generated this financial record", "pattern": "^[a-zA-Z0-9-_]+$", "minLength": 1, "maxLength": 100, "examples": ["order-456", "ORD_DEF_123"]}, "revenue_amount": {"type": "number", "description": "Revenue amount in the specified currency (excluding tax)", "minimum": 0, "multipleOf": 0.01, "examples": [100.0, 250.5, 1000.0]}, "tax_amount": {"type": "number", "description": "Tax amount in the specified currency", "minimum": 0, "multipleOf": 0.01, "examples": [6.0, 15.03, 60.0]}, "currency": {"type": "string", "description": "ISO 4217 currency code", "pattern": "^[A-Z]{3}$", "examples": ["MYR", "USD", "SGD"]}, "timestamp": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when the financial record was created", "examples": ["2023-12-25T10:30:00Z", "2023-12-25T18:30:00+08:00"]}, "metadata": {"type": "object", "description": "Additional context information for the financial record", "properties": {"payment_method": {"type": "string", "description": "Payment method used for the transaction", "examples": ["credit_card", "bank_transfer", "cash", "e_wallet"]}, "transaction_type": {"type": "string", "description": "Type of financial transaction", "examples": ["sale", "refund", "adjustment", "fee"]}, "description": {"type": "string", "description": "Human-readable description of the transaction", "maxLength": 500}, "source_service": {"type": "string", "description": "Service that generated this financial record", "examples": ["finance-service", "order-service"]}, "event_version": {"type": "string", "description": "Version of the event schema", "pattern": "^\\d+\\.\\d+$", "examples": ["1.0", "1.1", "2.0"]}}, "required": ["source_service", "event_version"], "additionalProperties": true}}, "additionalProperties": false, "examples": [{"transaction_id": "txn-*********", "order_reference": "order-*********", "revenue_amount": 100.0, "tax_amount": 6.0, "currency": "MYR", "timestamp": "2023-12-25T10:30:00Z", "metadata": {"payment_method": "credit_card", "transaction_type": "sale", "description": "Product purchase - Widget A", "source_service": "finance-service", "event_version": "1.0", "customer_id": "cust-456", "invoice_number": "INV-2023-001"}}]}