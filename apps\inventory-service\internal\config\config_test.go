package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestLoad_DefaultValues(t *testing.T) {
	// Clear environment variables to test defaults
	envVars := []string{
		"PORT", "DB_HOST", "DB_PORT", "DB_USER", "DB_PASSWORD", "DB_NAME", "DB_SSLMODE",
		"REDIS_HOST", "REDIS_PORT", "REDIS_PASSWORD", "REDIS_DB",
		"KAFKA_BROKERS", "KAFKA_GROUP_ID", "KAFKA_TOPIC",
	}

	// Store original values
	originalValues := make(map[string]string)
	for _, env := range envVars {
		originalValues[env] = os.Getenv(env)
		os.Unsetenv(env)
	}

	// Restore original values after test
	defer func() {
		for _, env := range envVars {
			if val, exists := originalValues[env]; exists && val != "" {
				os.Setenv(env, val)
			}
		}
	}()

	cfg, err := Load()
	require.NoError(t, err, "Load() should not fail")

	// Test default values
	assert.Equal(t, "8081", cfg.Server.Port, "Default port should be 8081")
	assert.Equal(t, "localhost", cfg.Database.Host, "Default DB host should be localhost")
	assert.Equal(t, "5434", cfg.Database.Port, "Default DB port should be 5434")
	assert.Equal(t, "inventory_db", cfg.Database.Name, "Default DB name should be inventory_db")
	assert.Equal(t, "127.0.0.1", cfg.Redis.Host, "Default Redis host should be 127.0.0.1")
	assert.Equal(t, "6379", cfg.Redis.Port, "Default Redis port should be 6379")
	assert.Equal(t, "inventory-service-group", cfg.Kafka.GroupID, "Default Kafka group ID should be inventory-service-group")
	assert.Equal(t, "orders.created", cfg.Kafka.Topic, "Default Kafka topic should be orders.created")
}

func TestLoad_EnvironmentVariables(t *testing.T) {
	// Set test environment variables
	testEnvVars := map[string]string{
		"PORT":           "9090",
		"DB_HOST":        "test-db",
		"DB_PORT":        "5555",
		"DB_NAME":        "test_inventory",
		"REDIS_HOST":     "test-redis",
		"REDIS_PORT":     "6380",
		"KAFKA_BROKERS":  "test-kafka:9093",
		"KAFKA_GROUP_ID": "test-group",
		"KAFKA_TOPIC":    "test.orders",
	}

	// Store original values
	originalValues := make(map[string]string)
	for key, value := range testEnvVars {
		originalValues[key] = os.Getenv(key)
		os.Setenv(key, value)
	}

	// Restore original values after test
	defer func() {
		for key := range testEnvVars {
			if val, exists := originalValues[key]; exists && val != "" {
				os.Setenv(key, val)
			} else {
				os.Unsetenv(key)
			}
		}
	}()

	cfg, err := Load()
	require.NoError(t, err, "Load() should not fail")

	// Test environment variable values
	assert.Equal(t, "9090", cfg.Server.Port, "Port should be from environment")
	assert.Equal(t, "test-db", cfg.Database.Host, "DB host should be from environment")
	assert.Equal(t, "5555", cfg.Database.Port, "DB port should be from environment")
	assert.Equal(t, "test_inventory", cfg.Database.Name, "DB name should be from environment")
	assert.Equal(t, "test-redis", cfg.Redis.Host, "Redis host should be from environment")
	assert.Equal(t, "test.orders", cfg.Kafka.Topic, "Kafka topic should be from environment")
}
