package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/company/cdh/apps/inventory-service/handlers"
	"github.com/company/cdh/apps/inventory-service/internal/config"
	"github.com/company/cdh/apps/inventory-service/internal/consumer"
	"github.com/company/cdh/apps/inventory-service/internal/database"
	"github.com/company/cdh/apps/inventory-service/internal/producer"
	"github.com/company/cdh/apps/inventory-service/internal/repository"
	"github.com/company/cdh/apps/inventory-service/internal/services"
	"github.com/company/cdh/apps/inventory-service/models"
)

func setupTestDB(t *testing.T) *sql.DB {
	// Set test environment variables for inventory database
	os.Setenv("DB_HOST", "localhost")
	os.Setenv("DB_PORT", "5434")
	os.Setenv("DB_USER", "postgres")
	os.Setenv("DB_PASSWORD", "postgres")
	os.Setenv("DB_NAME", "inventory_db")
	os.Setenv("DB_SSLMODE", "disable")

	// Load configuration
	cfg, err := config.Load()
	require.NoError(t, err, "Failed to load configuration")

	// Connect to database
	db, err := database.Connect(cfg.Database)
	require.NoError(t, err, "Failed to connect to test database")

	// Run migrations
	err = database.RunMigrations(db)
	require.NoError(t, err, "Failed to run migrations")

	return db
}

func cleanupTestDB(t *testing.T, db *sql.DB) {
	if db != nil {
		// Clean up test data
		_, err := db.Exec("DELETE FROM inventory WHERE sku LIKE 'TEST-%'")
		if err != nil {
			t.Logf("Warning: Failed to clean up test data: %v", err)
		}

		db.Close()
	}
}

func setupTestInventoryData(t *testing.T, repo repository.InventoryRepository) {
	// Create test inventory items
	testItems := []models.Inventory{
		{
			SKU:              "TEST-LAPTOP-001",
			ProductName:      "Test Laptop",
			Description:      "Test laptop for integration testing",
			StockQuantity:    10,
			ReservedQuantity: 0,
			UnitPrice:        999.99,
		},
		{
			SKU:              "TEST-MOUSE-001",
			ProductName:      "Test Mouse",
			Description:      "Test mouse for integration testing",
			StockQuantity:    5,
			ReservedQuantity: 0,
			UnitPrice:        79.99,
		},
		{
			SKU:              "TEST-LOWSTOCK-001",
			ProductName:      "Test Low Stock Item",
			Description:      "Test low stock item for integration testing",
			StockQuantity:    1,
			ReservedQuantity: 0,
			UnitPrice:        49.99,
		},
	}

	for _, item := range testItems {
		err := repo.Create(context.Background(), &item)
		require.NoError(t, err, "Failed to create test inventory item: %s", item.SKU)
	}
}

func TestInventoryServiceEndToEnd(t *testing.T) {
	db := setupTestDB(t)
	defer cleanupTestDB(t, db)

	// Initialize repository
	repo := repository.NewInventoryRepository(db)

	// Setup test data
	setupTestInventoryData(t, repo)

	// Load configuration for Kafka
	cfg, err := config.Load()
	require.NoError(t, err)

	// Force IPv4 address for Kafka in tests
	cfg.Kafka.Brokers = []string{"127.0.0.1:9092"}

	t.Run("Complete Order Processing Flow - Success", func(t *testing.T) {
		// Initialize Kafka producer (for failure events)
		kafkaProducer, err := producer.NewKafkaProducer(cfg.Kafka)
		if err != nil {
			t.Skipf("Kafka not available, skipping Kafka integration test: %v", err)
		}
		defer kafkaProducer.Close()

		// Initialize inventory processor
		processor := services.NewInventoryProcessor(repo, db, kafkaProducer)

		// Create test order event
		orderEvent := &models.OrderCreatedEvent{
			OrderID:    "TEST-ORDER-001",
			CustomerID: "TEST-CUSTOMER-001",
			Items: []models.OrderItem{
				{
					SKU:      "TEST-LAPTOP-001",
					Quantity: 2,
					Price:    999.99,
				},
				{
					SKU:      "TEST-MOUSE-001",
					Quantity: 1,
					Price:    79.99,
				},
			},
			Status:    "pending",
			CreatedAt: time.Now(),
		}

		// Validate the order event
		err = processor.ValidateOrderEvent(orderEvent)
		assert.NoError(t, err, "Order event validation should pass")

		// Get initial stock levels
		initialLaptopStock, err := repo.GetBySKU(context.Background(), "TEST-LAPTOP-001")
		require.NoError(t, err)
		initialMouseStock, err := repo.GetBySKU(context.Background(), "TEST-MOUSE-001")
		require.NoError(t, err)

		// Process the order event
		ctx := context.Background()
		err = processor.ProcessOrderEvent(ctx, orderEvent)
		assert.NoError(t, err, "Order processing should succeed")

		// Verify stock deduction
		finalLaptopStock, err := repo.GetBySKU(context.Background(), "TEST-LAPTOP-001")
		require.NoError(t, err)
		finalMouseStock, err := repo.GetBySKU(context.Background(), "TEST-MOUSE-001")
		require.NoError(t, err)

		// Assert stock was deducted correctly
		assert.Equal(t, initialLaptopStock.StockQuantity-2, finalLaptopStock.StockQuantity, "Laptop stock should be reduced by 2")
		assert.Equal(t, initialMouseStock.StockQuantity-1, finalMouseStock.StockQuantity, "Mouse stock should be reduced by 1")
	})

	t.Run("Complete Order Processing Flow - Insufficient Stock", func(t *testing.T) {
		// Initialize Kafka producer (for failure events)
		kafkaProducer, err := producer.NewKafkaProducer(cfg.Kafka)
		if err != nil {
			t.Skipf("Kafka not available, skipping Kafka integration test: %v", err)
		}
		defer kafkaProducer.Close()

		// Initialize inventory processor
		processor := services.NewInventoryProcessor(repo, db, kafkaProducer)

		// Create test order event with insufficient stock
		orderEvent := &models.OrderCreatedEvent{
			OrderID:    "TEST-ORDER-002",
			CustomerID: "TEST-CUSTOMER-002",
			Items: []models.OrderItem{
				{
					SKU:      "TEST-LOWSTOCK-001",
					Quantity: 5, // More than available (only 1 in stock)
					Price:    49.99,
				},
			},
			Status:    "pending",
			CreatedAt: time.Now(),
		}

		// Get initial stock level
		initialStock, err := repo.GetBySKU(context.Background(), "TEST-LOWSTOCK-001")
		require.NoError(t, err)

		// Process the order event (should fail)
		ctx := context.Background()
		err = processor.ProcessOrderEvent(ctx, orderEvent)
		assert.Error(t, err, "Order processing should fail due to insufficient stock")

		// Verify stock was NOT deducted
		finalStock, err := repo.GetBySKU(context.Background(), "TEST-LOWSTOCK-001")
		require.NoError(t, err)
		assert.Equal(t, initialStock.StockQuantity, finalStock.StockQuantity, "Stock should remain unchanged after failed processing")
	})

	t.Run("Complete Order Processing Flow - Product Not Found", func(t *testing.T) {
		// Initialize Kafka producer (for failure events)
		kafkaProducer, err := producer.NewKafkaProducer(cfg.Kafka)
		if err != nil {
			t.Skipf("Kafka not available, skipping Kafka integration test: %v", err)
		}
		defer kafkaProducer.Close()

		// Initialize inventory processor
		processor := services.NewInventoryProcessor(repo, db, kafkaProducer)

		// Create test order event with non-existent product
		orderEvent := &models.OrderCreatedEvent{
			OrderID:    "TEST-ORDER-003",
			CustomerID: "TEST-CUSTOMER-003",
			Items: []models.OrderItem{
				{
					SKU:      "TEST-NONEXISTENT-001",
					Quantity: 1,
					Price:    99.99,
				},
			},
			Status:    "pending",
			CreatedAt: time.Now(),
		}

		// Process the order event (should fail)
		ctx := context.Background()
		err = processor.ProcessOrderEvent(ctx, orderEvent)
		assert.Error(t, err, "Order processing should fail due to product not found")
	})
}

func TestKafkaConsumerIntegration(t *testing.T) {
	db := setupTestDB(t)
	defer cleanupTestDB(t, db)

	// Initialize repository
	repo := repository.NewInventoryRepository(db)

	// Setup test data
	setupTestInventoryData(t, repo)

	// Load configuration
	cfg, err := config.Load()
	require.NoError(t, err)

	// Force IPv4 address for Kafka in tests
	cfg.Kafka.Brokers = []string{"127.0.0.1:9092"}

	t.Run("Kafka Consumer with Real Processor", func(t *testing.T) {
		// Initialize Kafka producer (for failure events)
		kafkaProducer, err := producer.NewKafkaProducer(cfg.Kafka)
		if err != nil {
			t.Skipf("Kafka not available, skipping Kafka integration test: %v", err)
		}
		defer kafkaProducer.Close()

		// Initialize inventory processor
		processor := services.NewInventoryProcessor(repo, db, kafkaProducer)

		// Initialize Kafka consumer
		kafkaConsumer, err := consumer.NewKafkaConsumer(cfg.Kafka, processor)
		if err != nil {
			t.Skipf("Kafka not available, skipping Kafka integration test: %v", err)
		}
		defer kafkaConsumer.Close()

		// Test that consumer can be created and configured properly
		assert.NotNil(t, kafkaConsumer, "Kafka consumer should be created successfully")
	})
}

func TestDatabaseTransactionIntegrity(t *testing.T) {
	db := setupTestDB(t)
	defer cleanupTestDB(t, db)

	// Initialize repository
	repo := repository.NewInventoryRepository(db)

	// Setup test data
	setupTestInventoryData(t, repo)

	t.Run("Atomic Transaction - Multiple Items Success", func(t *testing.T) {
		// Get initial stock levels
		initialLaptopStock, err := repo.GetBySKU(context.Background(), "TEST-LAPTOP-001")
		require.NoError(t, err)
		initialMouseStock, err := repo.GetBySKU(context.Background(), "TEST-MOUSE-001")
		require.NoError(t, err)

		// Start transaction
		tx, err := db.BeginTx(context.Background(), nil)
		require.NoError(t, err)

		// Deduct stock for multiple items
		err = repo.DeductStock(context.Background(), "TEST-LAPTOP-001", 1)
		assert.NoError(t, err)

		err = repo.DeductStock(context.Background(), "TEST-MOUSE-001", 1)
		assert.NoError(t, err)

		// Commit transaction
		err = tx.Commit()
		assert.NoError(t, err)

		// Verify both items were deducted
		finalLaptopStock, err := repo.GetBySKU(context.Background(), "TEST-LAPTOP-001")
		require.NoError(t, err)
		finalMouseStock, err := repo.GetBySKU(context.Background(), "TEST-MOUSE-001")
		require.NoError(t, err)

		assert.Equal(t, initialLaptopStock.StockQuantity-1, finalLaptopStock.StockQuantity)
		assert.Equal(t, initialMouseStock.StockQuantity-1, finalMouseStock.StockQuantity)
	})
}

func TestInventoryQueryAPIIntegration(t *testing.T) {
	db := setupTestDB(t)
	defer cleanupTestDB(t, db)

	// Initialize repository and services
	repo := repository.NewInventoryRepository(db)

	// Setup test data
	setupTestInventoryData(t, repo)

	// Load configuration for Redis
	cfg, err := config.Load()
	require.NoError(t, err)

	// Setup Redis client (optional for caching tests)
	redisClient := redis.NewClient(&redis.Options{
		Addr: cfg.Redis.Host + ":" + cfg.Redis.Port,
		DB:   1, // Use test database
	})

	// Test Redis connection (skip Redis tests if not available)
	ctx := context.Background()
	_, redisErr := redisClient.Ping(ctx).Result()
	useRedis := redisErr == nil

	if !useRedis {
		t.Log("Redis not available, testing without cache")
		redisClient = nil
	}

	// Setup services and handlers
	inventoryService := services.NewInventoryService(repo, redisClient)
	inventoryHandler := handlers.NewInventoryHandler(inventoryService)

	t.Run("GET /inventory - single SKU integration", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/inventory?sku=TEST-LAPTOP-001", nil)
		w := httptest.NewRecorder()

		inventoryHandler.GetInventory(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response handlers.InventoryQueryResponse
		err := json.NewDecoder(w.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, 1, response.Count)
		assert.Len(t, response.Items, 1)
		assert.Equal(t, "TEST-LAPTOP-001", response.Items[0].SKU)
		assert.Equal(t, "Test Laptop", response.Items[0].ProductName)
		assert.Equal(t, 10, response.Items[0].StockQuantity)
		assert.Equal(t, 0, response.Items[0].ReservedQuantity)
		assert.Equal(t, 10, response.Items[0].AvailableQuantity)
		assert.Equal(t, 999.99, response.Items[0].UnitPrice)
	})

	t.Run("GET /inventory - multiple SKUs integration", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/inventory?sku=TEST-LAPTOP-001,TEST-MOUSE-001", nil)
		w := httptest.NewRecorder()

		inventoryHandler.GetInventory(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response handlers.InventoryQueryResponse
		err := json.NewDecoder(w.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, 2, response.Count)
		assert.Len(t, response.Items, 2)

		// Verify all SKUs are returned
		skus := make(map[string]bool)
		for _, item := range response.Items {
			skus[item.SKU] = true
		}
		assert.True(t, skus["TEST-LAPTOP-001"])
		assert.True(t, skus["TEST-MOUSE-001"])
	})

	t.Run("GET /inventory - non-existent SKU integration", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/inventory?sku=TEST-NONEXISTENT-001", nil)
		w := httptest.NewRecorder()

		inventoryHandler.GetInventory(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response handlers.InventoryQueryResponse
		err := json.NewDecoder(w.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, 0, response.Count)
		assert.Len(t, response.Items, 0)
	})

	// Cache performance test (only if Redis is available)
	if useRedis {
		t.Run("Redis caching integration test", func(t *testing.T) {
			// Clear cache first
			redisClient.FlushDB(ctx)

			// First request (cache miss)
			start := time.Now()
			req1 := httptest.NewRequest(http.MethodGet, "/inventory?sku=TEST-LAPTOP-001", nil)
			w1 := httptest.NewRecorder()
			inventoryHandler.GetInventory(w1, req1)
			firstRequestTime := time.Since(start)

			assert.Equal(t, http.StatusOK, w1.Code)

			// Second request (cache hit)
			start = time.Now()
			req2 := httptest.NewRequest(http.MethodGet, "/inventory?sku=TEST-LAPTOP-001", nil)
			w2 := httptest.NewRecorder()
			inventoryHandler.GetInventory(w2, req2)
			secondRequestTime := time.Since(start)

			assert.Equal(t, http.StatusOK, w2.Code)

			// Verify responses are identical
			var response1, response2 handlers.InventoryQueryResponse
			json.NewDecoder(w1.Body).Decode(&response1)
			json.NewDecoder(w2.Body).Decode(&response2)

			assert.Equal(t, response1, response2)

			t.Logf("First request (cache miss): %v", firstRequestTime)
			t.Logf("Second request (cache hit): %v", secondRequestTime)

			// Verify cache contains the data
			cacheKey := fmt.Sprintf("inventory:%s", "TEST-LAPTOP-001")
			cachedData, err := redisClient.Get(ctx, cacheKey).Result()
			assert.NoError(t, err)
			assert.NotEmpty(t, cachedData)

			// Cleanup
			redisClient.FlushDB(ctx)
		})
	}

	// Cleanup Redis connection
	if useRedis {
		redisClient.Close()
	}
}
