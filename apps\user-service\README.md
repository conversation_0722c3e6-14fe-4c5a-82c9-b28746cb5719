# User & Permissions Service

The User & Permissions Service is a microservice responsible for user management, authentication, and authorization within the Central Data Hub (CDH) platform.

## Features

- User registration with email and password
- User authentication with JWT tokens
- Password hashing using bcrypt
- PostgreSQL database integration
- Health check endpoint
- Comprehensive error handling and logging

## API Endpoints

### Health Check

**GET** `/health`

Returns the health status of the service.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### User Registration

**POST** `/register`

Registers a new user in the system.

**Request Body:**
```json
{
  "username": "johndo<PERSON>",
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Success Response (201):**
```json
{
  "id": 1,
  "username": "johndoe",
  "email": "<EMAIL>",
  "message": "User registered successfully"
}
```

**Error Response (400):**
```json
{
  "error": "validation_error",
  "message": "Username must be at least 3 characters long"
}
```

### User Login

**POST** `/login`

Authenticates a user and returns a JWT token.

**Request Body:**
```json
{
  "username": "johndoe",
  "password": "securepassword123"
}
```

**Success Response (200):**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user_id": 1,
  "message": "Login successful"
}
```

**Error Response (401):**
```json
{
  "error": "authentication_failed",
  "message": "Invalid username or password"
}
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SUPABASE_DB_URL` | Supabase PostgreSQL connection string | Required for production |
| `DB_HOST` | PostgreSQL host (fallback for local dev) | `localhost` |
| `DB_PORT` | PostgreSQL port (fallback for local dev) | `5432` |
| `DB_USER` | PostgreSQL username (fallback for local dev) | `postgres` |
| `DB_PASSWORD` | PostgreSQL password (fallback for local dev) | `postgres` |
| `DB_NAME` | PostgreSQL database name (fallback for local dev) | `user_db` |
| `DB_SSLMODE` | PostgreSQL SSL mode (fallback for local dev) | `disable` |
| `JWT_SECRET_KEY` | JWT signing secret | `default-secret-key-change-in-production` |
| `JWT_EXPIRATION_HOURS` | JWT token expiration in hours | `24` |

## Database Schema

### Users Table

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(254) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## JWT Token Structure

The JWT tokens contain the following claims:

```json
{
  "user_id": 1,
  "role": "user",
  "iss": "cdh-user-service",
  "iat": **********,
  "exp": **********
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `validation_error` | Request validation failed |
| `invalid_request` | Invalid JSON format |
| `user_exists` | User already exists |
| `authentication_failed` | Invalid credentials |
| `internal_error` | Internal server error |
| `method_not_allowed` | HTTP method not allowed |

## Development

### Prerequisites

- Go 1.21+
- Supabase account (recommended) OR PostgreSQL 12+ (local development)

### Database Configuration

The service supports two database configurations:

1. **Supabase PostgreSQL (Recommended for production)**:
   - Set `SUPABASE_DB_URL` environment variable
   - Provides managed database with automatic backups and scaling
   - Used in Kubernetes deployments

2. **Local PostgreSQL (Development fallback)**:
   - Set individual `DB_*` environment variables
   - Requires local PostgreSQL installation
   - Used when `SUPABASE_DB_URL` is not provided

### Running the Service

1. Set up environment variables
2. Start PostgreSQL database
3. Run the service:

```bash
go run main.go
```

The service will start on port 8080.

### Running Tests

```bash
# Run all tests
go test ./...

# Run tests with coverage
go test ./... -cover

# Run specific package tests
go test ./auth -v
go test ./handlers -v
go test ./models -v
```

### Building

```bash
go build -o user-service .
```

## Docker

The service includes a Dockerfile for containerized deployment.

```bash
# Build image
docker build -t user-service .

# Run container
docker run -p 8080:8080 user-service
```

## Security Considerations

- Passwords are hashed using bcrypt with default cost
- JWT tokens are signed with HMAC-SHA256
- Input validation and sanitization on all endpoints
- SQL injection protection through parameterized queries
- Comprehensive error handling without information leakage

## Monitoring and Logging

- Health check endpoint for monitoring
- Structured logging for all operations
- Request/response logging for debugging
- Error tracking and reporting
