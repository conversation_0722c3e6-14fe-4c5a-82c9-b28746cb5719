package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestOrderRequest_Validate(t *testing.T) {
	tests := []struct {
		name    string
		req     OrderRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid request",
			req: OrderRequest{
				ProductInfo: "Test Product",
				Quantity:    2,
				Price:       99.99,
			},
			wantErr: false,
		},
		{
			name: "empty product info",
			req: OrderRequest{
				ProductInfo: "",
				Quantity:    1,
				Price:       10.00,
			},
			wantErr: true,
			errMsg:  "product_info is required",
		},
		{
			name: "zero quantity",
			req: OrderRequest{
				ProductInfo: "Test Product",
				Quantity:    0,
				Price:       10.00,
			},
			wantErr: true,
			errMsg:  "quantity must be greater than 0",
		},
		{
			name: "negative quantity",
			req: OrderRequest{
				ProductInfo: "Test Product",
				Quantity:    -1,
				Price:       10.00,
			},
			wantErr: true,
			errMsg:  "quantity must be greater than 0",
		},
		{
			name: "zero price",
			req: OrderRequest{
				ProductInfo: "Test Product",
				Quantity:    1,
				Price:       0,
			},
			wantErr: true,
			errMsg:  "price must be greater than 0",
		},
		{
			name: "negative price",
			req: OrderRequest{
				ProductInfo: "Test Product",
				Quantity:    1,
				Price:       -10.00,
			},
			wantErr: true,
			errMsg:  "price must be greater than 0",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.req.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.errMsg, err.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestIsValidStatus(t *testing.T) {
	tests := []struct {
		name   string
		status OrderStatus
		want   bool
	}{
		{"pending", OrderStatusPending, true},
		{"confirmed", OrderStatusConfirmed, true},
		{"shipped", OrderStatusShipped, true},
		{"delivered", OrderStatusDelivered, true},
		{"cancelled", OrderStatusCancelled, true},
		{"invalid", OrderStatus("invalid"), false},
		{"empty", OrderStatus(""), false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := IsValidStatus(tt.status)
			assert.Equal(t, tt.want, got)
		})
	}
}