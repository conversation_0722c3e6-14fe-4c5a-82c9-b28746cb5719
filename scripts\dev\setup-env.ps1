# CDH Development Environment Setup Script
# This script sets up the local development environment

param(
    [switch]$Reset,
    [switch]$Verify
)

Write-Host "CDH Development Environment Setup" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Function to check if command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to wait for Kubernetes to be ready
function Wait-ForKubernetes {
    Write-Host "Waiting for Kubernetes cluster to be ready..." -ForegroundColor Yellow
    $timeout = 300 # 5 minutes
    $elapsed = 0
    
    while ($elapsed -lt $timeout) {
        try {
            $result = kubectl cluster-info 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Kubernetes cluster is ready!" -ForegroundColor Green
                return $true
            }
        }
        catch {
            # Continue waiting
        }
        
        Start-Sleep 5
        $elapsed += 5
        Write-Host "." -NoNewline
    }
    
    Write-Host ""
    Write-Host "Timeout waiting for Kubernetes cluster" -ForegroundColor Red
    return $false
}

# Reset environment if requested
if ($Reset) {
    Write-Host "Resetting development environment..." -ForegroundColor Yellow
    
    # Delete namespace and all resources
    kubectl delete namespace cdh-dev --ignore-not-found=true
    
    Write-Host "Environment reset complete" -ForegroundColor Green
}

# Verify prerequisites
Write-Host "Checking prerequisites..." -ForegroundColor Cyan

if (-not (Test-Command "docker")) {
    Write-Host "ERROR: Docker not found. Please install Docker Desktop." -ForegroundColor Red
    exit 1
}

if (-not (Test-Command "kubectl")) {
    Write-Host "ERROR: kubectl not found. Please enable Kubernetes in Docker Desktop." -ForegroundColor Red
    exit 1
}

# Check Docker is running
Write-Host "Checking Docker Desktop..." -ForegroundColor Cyan
try {
    $dockerVersion = docker version --format "{{.Server.Version}}" 2>$null
    if ($LASTEXITCODE -eq 0 -and $dockerVersion) {
        Write-Host "✓ Docker Desktop is running (version: $dockerVersion)" -ForegroundColor Green
    } else {
        throw "Docker daemon not responding"
    }
}
catch {
    Write-Host "ERROR: Docker is not running or not accessible." -ForegroundColor Red
    Write-Host "Please ensure Docker Desktop is started and running." -ForegroundColor Yellow
    Write-Host "You can check Docker Desktop status in the system tray." -ForegroundColor Yellow
    exit 1
}

# Check Kubernetes is running
if (-not (Wait-ForKubernetes)) {
    Write-Host "ERROR: Kubernetes cluster is not ready. Please check Docker Desktop." -ForegroundColor Red
    exit 1
}

Write-Host "✓ Kubernetes cluster is ready" -ForegroundColor Green

# Create namespace and apply configurations
Write-Host "Setting up CDH development namespace..." -ForegroundColor Cyan

kubectl apply -f infra/k8s/dev/namespace-config.yaml
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to create namespace configuration" -ForegroundColor Red
    exit 1
}

# Set default namespace
kubectl config set-context --current --namespace=cdh-dev
Write-Host "✓ Default namespace set to cdh-dev" -ForegroundColor Green

# Verify setup
if ($Verify -or $true) {
    Write-Host "Verifying environment setup..." -ForegroundColor Cyan
    
    # Check namespace
    $namespace = kubectl get namespace cdh-dev -o name 2>$null
    if ($namespace) {
        Write-Host "✓ cdh-dev namespace exists" -ForegroundColor Green
    } else {
        Write-Host "✗ cdh-dev namespace not found" -ForegroundColor Red
    }
    
    # Check resource quota
    $quota = kubectl get resourcequota cdh-dev-quota -n cdh-dev -o name 2>$null
    if ($quota) {
        Write-Host "✓ Resource quota configured" -ForegroundColor Green
    } else {
        Write-Host "✗ Resource quota not found" -ForegroundColor Red
    }
    
    # Check current context
    $currentNamespace = kubectl config view --minify --output 'jsonpath={..namespace}' 2>$null
    if ($currentNamespace -eq "cdh-dev") {
        Write-Host "✓ Current namespace is cdh-dev" -ForegroundColor Green
    } else {
        Write-Host "✗ Current namespace is not cdh-dev (current: $currentNamespace)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Development environment setup complete!" -ForegroundColor Green
Write-Host "You can now deploy applications to the cdh-dev namespace." -ForegroundColor Cyan
Write-Host ""
Write-Host "Useful commands:" -ForegroundColor Yellow
Write-Host "  kubectl get all                    # View all resources"
Write-Host "  kubectl apply -f <manifest>        # Deploy application"
Write-Host "  kubectl port-forward svc/<name> <port>  # Access service locally"
Write-Host "  kubectl logs <pod-name>            # View pod logs"
