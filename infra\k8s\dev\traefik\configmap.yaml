---
apiVersion: v1
kind: ConfigMap
metadata:
  name: traefik-config
  namespace: cdh-dev
  labels:
    app: traefik
data:
  traefik.yaml: |
    # Traefik v3.x Configuration for Local Development
    
    # Global configuration
    global:
      checkNewVersion: false
      sendAnonymousUsage: false
    
    # Entry points
    entryPoints:
      web:
        address: ":80"
      websecure:
        address: ":443"
      traefik:
        address: ":8080"
    
    # Providers
    providers:
      kubernetesIngress:
        ingressClass: "traefik"
      kubernetesCRD: {}
    
    # API and Dashboard
    api:
      dashboard: true
      debug: true
      insecure: true  # Only for local development - disable in production
    
    # Metrics
    metrics:
      prometheus:
        addEntryPointsLabels: true
        addServicesLabels: true
    
    # Logging
    log:
      level: INFO
      format: json
    
    # Access logs
    accessLog:
      format: json
      fields:
        defaultMode: keep
        names:
          ClientUsername: drop
        headers:
          defaultMode: keep
          names:
            User-Agent: redact
            Authorization: drop
            Content-Type: keep
    
    # Ping endpoint for health checks
    ping:
      entryPoint: "traefik"
    
    # Certificate resolvers (disabled for local development)
    # certificatesResolvers: {}
