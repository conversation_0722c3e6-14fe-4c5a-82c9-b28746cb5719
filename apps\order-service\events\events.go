package events

import (
	"fmt"
	"time"

	"github.com/company/cdh/apps/order-service/models"
)

// OrderCreatedEvent represents an order created event
type OrderCreatedEvent struct {
	EventID     string    `json:"event_id"`
	EventType   string    `json:"event_type"`
	Timestamp   time.Time `json:"timestamp"`
	OrderID     int       `json:"order_id"`
	OrderNumber string    `json:"order_number"`
	ProductInfo string    `json:"product_info"`
	Quantity    int       `json:"quantity"`
	Price       float64   `json:"price"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
}

// NewOrderCreatedEvent creates a new OrderCreatedEvent from an Order
func NewOrderCreatedEvent(order *models.Order) *OrderCreatedEvent {
	return &OrderCreatedEvent{
		EventID:     generateEventID(),
		EventType:   "order.created",
		Timestamp:   time.Now(),
		OrderID:     order.ID,
		OrderNumber: order.OrderNumber,
		ProductInfo: order.ProductInfo,
		Quantity:    order.Quantity,
		Price:       order.Price,
		Status:      string(order.Status),
		CreatedAt:   order.CreatedAt,
	}
}

// generateEventID generates a unique event ID
func generateEventID() string {
	return fmt.Sprintf("evt_%d", time.Now().UnixNano())
}
