package database

import (
	"database/sql"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/company/cdh/apps/order-service/models"
	"github.com/company/cdh/apps/order-service/internal/errors"
)

// MockOrderRepository implements repository.OrderRepository for testing
type MockOrderRepository struct {
	orders  map[int]*models.Order
	nextID  int
	numbers map[string]*models.Order
}

func NewMockOrderRepository() *MockOrderRepository {
	return &MockOrderRepository{
		orders:  make(map[int]*models.Order),
		nextID:  1,
		numbers: make(map[string]*models.Order),
	}
}

func (m *MockOrderRepository) CreateOrder(req *models.OrderRequest) (*models.Order, error) {
	order := req.ToOrder()
	order.ID = m.nextID
	order.OrderNumber = "ORD-" + string(rune(m.nextID))
	order.CreatedAt = time.Now()
	order.UpdatedAt = time.Now()
	
	m.orders[order.ID] = order
	m.numbers[order.OrderNumber] = order
	m.nextID++
	
	return order, nil
}

func (m *MockOrderRepository) GetOrderByID(id int) (*models.Order, error) {
	order, exists := m.orders[id]
	if !exists {
		return nil, errors.ErrOrderNotFound
	}
	return order, nil
}

func (m *MockOrderRepository) GetOrderByNumber(orderNumber string) (*models.Order, error) {
	order, exists := m.numbers[orderNumber]
	if !exists {
		return nil, errors.ErrOrderNotFound
	}
	return order, nil
}

func (m *MockOrderRepository) UpdateOrderStatus(id int, status models.OrderStatus) error {
	order, exists := m.orders[id]
	if !exists {
		return errors.ErrOrderNotFound
	}
	
	if !models.IsValidStatus(status) {
		return errors.NewValidationError("status", "invalid order status")
	}
	
	order.Status = status
	order.UpdatedAt = time.Now()
	return nil
}

func TestNewOrderRepository(t *testing.T) {
	t.Run("Create repository with valid DB", func(t *testing.T) {
		// Create a mock DB (in real tests, you'd use a real connection)
		db := &sql.DB{}
		
		repo := NewOrderRepository(db)
		assert.NotNil(t, repo)
	})
}

func TestOrderRepository_CreateOrder(t *testing.T) {
	repo := NewMockOrderRepository()

	t.Run("Create valid order", func(t *testing.T) {
		req := &models.OrderRequest{
			ProductInfo: "Test Product",
			Quantity:    2,
			Price:       99.99,
		}

		order, err := repo.CreateOrder(req)
		
		assert.NoError(t, err)
		assert.NotNil(t, order)
		assert.Equal(t, 1, order.ID)
		assert.NotEmpty(t, order.OrderNumber)
		assert.Equal(t, "Test Product", order.ProductInfo)
		assert.Equal(t, 2, order.Quantity)
		assert.Equal(t, 99.99, order.Price)
		assert.Equal(t, models.OrderStatusPending, order.Status)
		assert.False(t, order.CreatedAt.IsZero())
		assert.False(t, order.UpdatedAt.IsZero())
	})
}

func TestOrderRepository_GetOrderByID(t *testing.T) {
	repo := NewMockOrderRepository()

	t.Run("Get existing order", func(t *testing.T) {
		// Create an order first
		req := &models.OrderRequest{
			ProductInfo: "Test Product",
			Quantity:    1,
			Price:       50.00,
		}
		createdOrder, err := repo.CreateOrder(req)
		require.NoError(t, err)

		// Get the order by ID
		order, err := repo.GetOrderByID(createdOrder.ID)
		
		assert.NoError(t, err)
		assert.NotNil(t, order)
		assert.Equal(t, createdOrder.ID, order.ID)
		assert.Equal(t, createdOrder.OrderNumber, order.OrderNumber)
		assert.Equal(t, "Test Product", order.ProductInfo)
	})

	t.Run("Get non-existent order", func(t *testing.T) {
		order, err := repo.GetOrderByID(999)
		
		assert.Error(t, err)
		assert.Nil(t, order)
		assert.Equal(t, errors.ErrOrderNotFound, err)
	})
}

func TestOrderRepository_GetOrderByNumber(t *testing.T) {
	repo := NewMockOrderRepository()

	t.Run("Get existing order by number", func(t *testing.T) {
		// Create an order first
		req := &models.OrderRequest{
			ProductInfo: "Test Product",
			Quantity:    1,
			Price:       50.00,
		}
		createdOrder, err := repo.CreateOrder(req)
		require.NoError(t, err)

		// Get the order by number
		order, err := repo.GetOrderByNumber(createdOrder.OrderNumber)
		
		assert.NoError(t, err)
		assert.NotNil(t, order)
		assert.Equal(t, createdOrder.ID, order.ID)
		assert.Equal(t, createdOrder.OrderNumber, order.OrderNumber)
	})

	t.Run("Get non-existent order by number", func(t *testing.T) {
		order, err := repo.GetOrderByNumber("NON-EXISTENT")
		
		assert.Error(t, err)
		assert.Nil(t, order)
		assert.Equal(t, errors.ErrOrderNotFound, err)
	})
}

func TestOrderRepository_UpdateOrderStatus(t *testing.T) {
	repo := NewMockOrderRepository()

	t.Run("Update existing order status", func(t *testing.T) {
		// Create an order first
		req := &models.OrderRequest{
			ProductInfo: "Test Product",
			Quantity:    1,
			Price:       50.00,
		}
		createdOrder, err := repo.CreateOrder(req)
		require.NoError(t, err)

		// Update the status
		err = repo.UpdateOrderStatus(createdOrder.ID, models.OrderStatusConfirmed)
		
		assert.NoError(t, err)
		
		// Verify the status was updated
		order, err := repo.GetOrderByID(createdOrder.ID)
		require.NoError(t, err)
		assert.Equal(t, models.OrderStatusConfirmed, order.Status)
	})

	t.Run("Update non-existent order", func(t *testing.T) {
		err := repo.UpdateOrderStatus(999, models.OrderStatusConfirmed)
		
		assert.Error(t, err)
		assert.Equal(t, errors.ErrOrderNotFound, err)
	})

	t.Run("Update with invalid status", func(t *testing.T) {
		// Create an order first
		req := &models.OrderRequest{
			ProductInfo: "Test Product",
			Quantity:    1,
			Price:       50.00,
		}
		createdOrder, err := repo.CreateOrder(req)
		require.NoError(t, err)

		// Try to update with invalid status
		err = repo.UpdateOrderStatus(createdOrder.ID, "invalid_status")
		
		assert.Error(t, err)
		var validationErr errors.ValidationError
		assert.ErrorAs(t, err, &validationErr)
		assert.Equal(t, "status", validationErr.Field)
	})
}
