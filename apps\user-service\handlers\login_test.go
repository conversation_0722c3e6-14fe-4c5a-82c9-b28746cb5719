package handlers

import (
	"bytes"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/company/cdh/apps/user-service/models"
	"github.com/stretchr/testify/assert"
)

func TestParseLoginRequest(t *testing.T) {
	tests := []struct {
		name        string
		requestBody string
		expectError bool
	}{
		{
			name:        "Valid request",
			requestBody: `{"username":"testuser","password":"password123"}`,
			expectError: false,
		},
		{
			name:        "Invalid JSON",
			requestBody: `{"username":"testuser","password":}`,
			expectError: true,
		},
		{
			name:        "Unknown fields",
			requestBody: `{"username":"testuser","password":"password123","extra":"field"}`,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(http.MethodPost, "/login", bytes.NewBufferString(tt.requestBody))
			req.Header.Set("Content-Type", "application/json")

			result, err := parseLoginRequest(req)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "testuser", result.Username)
				assert.Equal(t, "password123", result.Password)
			}
		})
	}
}

func TestLoginRequestValidation(t *testing.T) {
	tests := []struct {
		name        string
		request     models.UserLoginRequest
		expectError bool
	}{
		{
			name: "Valid request",
			request: models.UserLoginRequest{
				Username: "testuser",
				Password: "password123",
			},
			expectError: false,
		},
		{
			name: "Missing username",
			request: models.UserLoginRequest{
				Username: "",
				Password: "password123",
			},
			expectError: true,
		},
		{
			name: "Missing password",
			request: models.UserLoginRequest{
				Username: "testuser",
				Password: "",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.request.Validate()

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestLoginRequestSanitize(t *testing.T) {
	request := models.UserLoginRequest{
		Username: "  TestUser  ",
		Password: "password123",
	}

	request.Sanitize()

	assert.Equal(t, "testuser", request.Username)
	assert.Equal(t, "password123", request.Password) // Password should not be trimmed
}

func TestAuthenticationError(t *testing.T) {
	err := &AuthenticationError{Message: "Test error"}
	assert.Equal(t, "Test error", err.Error())
}
