# Central Data Hub (CDH) System Architecture Document

## 1. Introduction
This document outlines the overall project architecture for the Central Data Hub (CDH). It is a microservices system based on Go, following cloud-native principles. Its primary goal is to serve as the core backend for future POS, WMS, and e-commerce systems, providing unified data management and business processing capabilities, and ensuring compliance with the LHDN MyInvois system.

### Change Log
| Date       | Version | Description           | Author             |
| :--------- | :------ | :-------------------- | :----------------- |
| 2025-08-01 | 1.0     | Initial Architecture Design | Winston, Architect |
| 2025-08-01 | 1.1     | Updated database architecture to Supabase PostgreSQL | Winston, Architect |
| 2025-08-01 | 1.2     | Migrated to independent database infrastructure per microservice | Winston, Architect |

## 2. High-Level Architecture

### Technical Summary
This system uses an event-driven microservices architecture based on Kubernetes (K8s). All services are developed in Go and deployed as Docker containers. Asynchronous communication between services is achieved via Kafka for decoupling and high reliability. Traefik serves as the API Gateway, acting as the single entry point for all external requests, handling routing, security, and authentication. Each service operates with its own dedicated PostgreSQL instance using Docker containers to ensure complete data isolation, independent scaling, and true microservices architecture compliance.

### Architecture Diagram
```mermaid
graph TD
    subgraph "External Systems (Future Development)"
        POS[POS System]
        WMS[WMS System]
        ECOM[E-commerce Platform]
    end

    subgraph "CDH Platform"
        subgraph "Infrastructure"
            K8S[Kubernetes Cluster]
            KAFKA[Kafka]
            MONITORING["Monitoring & Logs<br/>(Prometheus, Grafana, EFK)"]
        end

        subgraph "Microservices"
            GW["API Gateway - Traefik"]
            USER["User & Permissions Service<br/>(Go)"]
            ORDER["Order Service<br/>(Go)"]
            INV["Inventory Service<br/>(Go)"]
            FIN["Finance Service<br/>(Go)"]
        end
        
        subgraph "Independent Database Infrastructure"
            DB_USER["User DB<br/>(PostgreSQL:5432)"]
            DB_ORDER["Order DB<br/>(PostgreSQL:5433)"]
            DB_INV["Inventory DB<br/>(PostgreSQL:5434)"]
            DB_FIN["Finance DB<br/>(PostgreSQL:5435)"]
        end

        GW --> USER
        GW --> ORDER
        GW --> INV

        USER -- CRUD --> DB_USER
        ORDER -- CRUD --> DB_ORDER
        INV -- CRUD --> DB_INV
        FIN -- CRUD --> DB_FIN
        
        ORDER -- "orders.created" Event --> KAFKA
        KAFKA -- Consumes --> INV
        KAFKA -- Consumes --> FIN
    end

    subgraph "Third-Party Integration"
      LHDN[LHDN MyInvois API]
    end

    POS --> GW
    WMS --> GW
    ECOM --> GW
    FIN --> LHDN
```

### Architecture and Design Patterns
* **Microservices Architecture**: Decomposes functionality into independent, autonomous services for easier development, deployment, and scaling.
* **Event-Driven Architecture**: Services communicate asynchronously by publishing and subscribing to events via Kafka, improving system resilience and responsiveness.
* **API First**: Each service exposes its functionality through a well-defined API, which serves as the contract for inter-service communication.
* **Database per Service**: Ensures loose coupling between services and the independence of their data models.
* **Independent Database Infrastructure**: Each microservice operates with its own dedicated PostgreSQL instance, ensuring complete isolation, independent scaling, and fault tolerance.
* **Container-Based Database Deployment**: Each service database runs in its own Docker container with dedicated ports, volumes, and configurations for true microservices architecture.

## 3. Technology Stack
| Category | Technology | Notes |
| :--- | :--- | :--- |
| **Language** | Go | High performance, concurrency model is ideal for high-traffic scenarios, simple deployment. |
| **Database** | PostgreSQL | Independent PostgreSQL instances per service using Docker containers. Each service has dedicated database infrastructure with isolated ports (5432-5435), volumes, and configurations for true microservices architecture. |
| **Cache** | Redis | Used for caching hot data, such as inventory queries, to improve response times. |
| **Message Queue** | Kafka | Serves as an event streaming platform, supporting asynchronous communication and future data analytics. |
| **API Gateway** | Traefik | Cloud-native, tightly integrated with K8s, automates service discovery and routing. |
| **Containerization** | Docker | A standardized way to package applications. |
| **Orchestration** | Kubernetes | Automates the deployment, scaling, and management of containerized applications. |
| **Monitoring** | Prometheus, Grafana | For collecting and visualizing system performance metrics. |
| **Logging** | EFK Stack | For centrally managing and querying logs from all microservices. |

## 4. Database Infrastructure

### Independent Database Architecture

Each microservice operates with its own dedicated PostgreSQL instance to ensure true microservices independence:

| Service | Database | Port | Container | Volume |
|---------|----------|------|-----------|--------|
| User & Permissions | `user_db` | 5432 | `cdh-user-db` | `user_db_data` |
| Order Management | `order_db` | 5433 | `cdh-order-db` | `order_db_data` |
| Inventory Management | `inventory_db` | 5434 | `cdh-inventory-db` | `inventory_db_data` |
| Finance & Tax | `finance_db` | 5435 | `cdh-finance-db` | `finance_db_data` |

### Benefits of Independent Database Infrastructure

* **Fault Isolation**: Database failure in one service doesn't affect others
* **Independent Scaling**: Each database can be scaled based on service-specific needs
* **Technology Freedom**: Services can use different database versions or configurations
* **Team Autonomy**: Teams can manage their database independently
* **Performance Isolation**: Heavy queries in one service don't impact others
* **Security Boundaries**: Each service has isolated data access

### Database Deployment

Databases are deployed using Docker Compose for development and StatefulSets for Kubernetes production environments. Each database instance includes:

* Dedicated PostgreSQL 17 container
* Persistent volume for data storage
* Health checks and monitoring
* Automated initialization scripts
* Backup and recovery procedures

## 5. Core Service Module Descriptions

### API Gateway (Traefik)
* **Responsibility**: The single entry point for all external requests.
* **Core Functions**:
    * **Routing**: Automatically discovers and forwards requests to the correct backend microservice.
    * **Authentication & Authorization**: Validates JWTs to ensure only authorized users have access.
    * **Security**: Provides SSL/TLS termination, rate limiting, and basic firewall capabilities.

### User & Permissions Service (Go)
* **Responsibility**: Manages all users, roles, and permissions.
* **Core Functions**:
    * Registration, login, and information management for users/system accounts.
    * Generation and validation of API keys and JWTs.
    * Assignment of roles and permissions.
* **Database**: `user_db` (Independent PostgreSQL instance on port 5432)

### Order Service (Go)
* **Responsibility**: Uniformly processes and manages orders from all channels.
* **Core Functions**:
    * Provides a `POST /orders` API for creating orders.
    * Provides a `GET /orders/{id}` API for querying orders.
    * Publishes an "Order Created" message to the `orders.created` Kafka topic after an order is created.
* **Database**: `order_db` (Independent PostgreSQL instance on port 5433)

### Inventory Service (Go)
* **Responsibility**: The "single source of truth" for all inventory data.
* **Core Functions**:
    * Provides a `GET /inventory?sku={sku1},{sku2}` API for querying stock by SKUs.
    * Implements Redis caching for high-performance inventory queries (4.6x faster cache hits).
    * Listens to the `orders.created` Kafka topic to receive messages and asynchronously deduct inventory.
    * Publishes failure events to `inventory.failures` topic when stock is insufficient.
    * Supports atomic inventory operations with transaction safety.
* **Database**: `inventory_db` (Independent PostgreSQL instance on port 5434) + Redis Cache
* **API Endpoints**:
    * `GET /inventory?sku={skus}` - Query inventory by comma-separated SKUs
    * `GET /health` - Comprehensive health check with dependency status
    * `GET /health/live` - Liveness probe for Kubernetes
    * `GET /health/ready` - Readiness probe for Kubernetes
* **Performance**: Redis cache-aside pattern with 5-minute TTL, graceful degradation when cache unavailable

### Finance Service (Go)
* **Responsibility**: Processes transactional financial data and provides standardized financial records for external accounting platform integration.
* **Core Functions**:
    * Listens to the `orders.created` Kafka topic to automatically generate standardized financial entries.
    * Publishes financial events to `financial.records.created` topic for consumption by external accounting platforms.
    * Provides REST API endpoints for accounting platforms to query financial data and submit processing status.
    * Maintains audit trails and data validation for financial record integrity.
* **Database**: `finance_db` (Independent PostgreSQL instance on port 5435)
* **Integration Pattern**: Event-driven data provider for external accounting systems (separation of concerns: CDH handles transactional data, accounting platforms handle tax compliance and e-invoicing)

## 5. Source Code Repository Structure (Monorepo)
```plaintext
/
├── apps/
│   ├── admin-ui/        # Admin backend frontend defined in Epic 4
│   ├── finance-service/ # Finance Service (Go)
│   ├── inventory-service/ # Inventory Service (Go)
│   ├── order-service/   # Order Service (Go)
│   └── user-service/    # User & Permissions Service (Go)
├── infra/
│   └── k8s/             # Kubernetes deployment configuration files (YAML)
├── packages/
│   └── shared/          # Shared Go code/type definitions
├── .github/
│   └── workflows/       # CI/CD pipeline definitions
├── go.work              # Go Workspace configuration file
└── README.md            # Project README
```

## 6. Deployment and Operations
* **Deployment Process**: Developer commits code to Git -> GitHub Actions triggers CI/CD -> Automatically runs tests -> Builds Docker image and pushes to an image registry -> Updates Kubernetes deployment configuration to pull the new image and perform a rolling update of the service.
* **Environment Separation**: Use K8s Namespaces to separate development, testing, and production environments.
* **Configuration Management**: Use K8s ConfigMaps and Secrets to manage application configuration and sensitive information, instead of hardcoding them.
* **Database Management**: Supabase provides managed PostgreSQL instances with automatic backups, monitoring, and scaling. Database connections are configured via environment variables supporting both Supabase cloud and local development environments.