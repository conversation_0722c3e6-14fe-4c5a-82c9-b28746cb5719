# Template for CDH service configuration
# Use ConfigMaps for non-sensitive configuration data

apiVersion: v1
kind: ConfigMap
metadata:
  name: <SERVICE_NAME>-config
  namespace: cdh-dev
  labels:
    app: <SERVICE_NAME>
    environment: development
    project: cdh
data:
  # Application configuration
  app.properties: |
    # Service-specific configuration
    service.name=<SERVICE_NAME>
    service.environment=development
    service.log.level=debug
    
    # Database configuration (use secrets for credentials)
    database.host=localhost
    database.port=5432
    database.name=cdh_dev
    
    # API configuration
    api.timeout=30s
    api.retry.attempts=3
    
    # Add service-specific configuration here
  
  # Logging configuration
  logging.yaml: |
    level:
      root: INFO
      com.cdh: DEBUG
    appenders:
      console:
        type: console
        pattern: "%d{ISO8601} [%thread] %-5level %logger{36} - %msg%n"
      file:
        type: file
        file: /var/log/<SERVICE_NAME>.log
        pattern: "%d{ISO8601} [%thread] %-5level %logger{36} - %msg%n"
    loggers:
      com.cdh:
        level: DEBUG
        additivity: false
        appenders:
          - console
          - file

---
# Template for secrets (sensitive configuration)
# Use this for passwords, API keys, certificates, etc.
apiVersion: v1
kind: Secret
metadata:
  name: <SERVICE_NAME>-secrets
  namespace: cdh-dev
  labels:
    app: <SERVICE_NAME>
    environment: development
    project: cdh
type: Opaque
data:
  # Base64 encoded values
  # Use: echo -n "your-secret" | base64
  database-password: <BASE64_ENCODED_PASSWORD>
  api-key: <BASE64_ENCODED_API_KEY>
  # Add other secrets here
stringData:
  # Plain text values (will be base64 encoded automatically)
  # database-username: "cdh_user"
  # jwt-secret: "your-jwt-secret-key"
