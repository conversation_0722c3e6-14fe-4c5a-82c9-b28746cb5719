# Supabase Database Setup Guide

## Overview

The CDH platform uses Supabase PostgreSQL as the primary database solution, providing managed database services with automatic backups, scaling, and monitoring.

## Environment Configuration

### Production/Kubernetes Deployment
Set the `SUPABASE_DB_URL` environment variable:

```bash
SUPABASE_DB_URL=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require
```

### Local Development (Fallback)
If `SUPABASE_DB_URL` is not set, the service will fall back to local PostgreSQL using these variables:

```bash
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=user_db
DB_SSLMODE=disable
```

## Testing Configuration

Tests automatically use the environment-based configuration. Set `SUPABASE_DB_URL` to run tests against Supabase, or use local PostgreSQL variables for local testing.

## Migration Status

All services have been migrated to use Supabase PostgreSQL as the primary database:

- ✅ User Service (Stories 1.5, 1.6)
- ✅ Kubernetes deployments updated
- ✅ Documentation updated
- ✅ Test configurations cleaned up
- ✅ Architecture documentation aligned

## Benefits

- **Managed Service**: Automatic backups, monitoring, scaling
- **Security**: SSL/TLS encryption, managed access controls
- **Performance**: Optimized PostgreSQL with connection pooling
- **Reliability**: High availability and disaster recovery
- **Development**: Consistent environment across dev/staging/production