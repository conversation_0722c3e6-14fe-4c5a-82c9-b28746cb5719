package services

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/company/cdh/apps/order-service/events"
	"github.com/company/cdh/apps/order-service/internal/repository"
	"github.com/company/cdh/apps/order-service/models"
)

// OrderServiceInterface defines the interface for order business logic
type OrderServiceInterface interface {
	CreateOrder(ctx context.Context, req *models.OrderRequest) (*models.Order, error)
	GetOrderByID(ctx context.Context, id int) (*models.Order, error)
}

// OrderService handles business logic for orders
type OrderService struct {
	repo     repository.OrderRepository
	producer events.Producer
}

// NewOrderService creates a new OrderService
func NewOrderService(repo repository.OrderRepository, producer events.Producer) *OrderService {
	return &OrderService{
		repo:     repo,
		producer: producer,
	}
}

// CreateOrder creates a new order and publishes an event
func (s *OrderService) CreateOrder(ctx context.Context, req *models.OrderRequest) (*models.Order, error) {
	// Set a timeout for the entire operation
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()
	// Create order in database
	order, err := s.repo.CreateOrder(req)
	if err != nil {
		return nil, fmt.Errorf("failed to create order in database: %w", err)
	}

	// Publish order created event
	event := events.NewOrderCreatedEvent(order)
	if err := s.producer.PublishOrderCreated(event); err != nil {
		// Log the error but don't fail the order creation
		// In a production system, you might want to implement a retry mechanism
		// or store the event for later processing
		log.Printf("ERROR: Failed to publish order created event for order %s: %v", order.OrderNumber, err)

		// TODO: Consider implementing:
		// 1. Retry mechanism with exponential backoff
		// 2. Dead letter queue for failed events
		// 3. Event store for replay capability

		// For now, we'll continue and return the order successfully
		// The event publishing failure is logged but doesn't affect the order creation
	} else {
		log.Printf("INFO: Successfully published order created event for order %s", order.OrderNumber)
	}

	return order, nil
}

// GetOrderByID retrieves an order by ID
func (s *OrderService) GetOrderByID(ctx context.Context, id int) (*models.Order, error) {
	// Set a timeout for the database operation
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	return s.repo.GetOrderByID(id)
}
