# Financial Events API Documentation

## Overview

The Financial Events API provides standardized financial event publishing for external accounting systems. Events are published to Kafka topics in real-time as financial transactions are processed.

## Event Schema

### Financial Record Event

Published to Kafka topic: `financial.records.created`

#### Schema Definition

```json
{
  "transaction_id": "string",      // Unique transaction identifier
  "order_reference": "string",     // Original order reference
  "revenue_amount": "number",      // Revenue amount (excluding tax)
  "tax_amount": "number",         // Tax amount (6% SST for Malaysia)
  "currency": "string",           // Currency code (ISO 4217)
  "timestamp": "string",          // ISO 8601 timestamp
  "metadata": {                   // Additional context information
    "source_service": "string",   // Always "finance-service"
    "event_version": "string",    // Event schema version
    "transaction_type": "string", // Transaction type (e.g., "sale")
    "payment_method": "string",   // Payment method used
    "description": "string"       // Human-readable description
  }
}
```

#### Field Descriptions

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `transaction_id` | string | Yes | Unique identifier for the financial transaction |
| `order_reference` | string | Yes | Reference to the original order |
| `revenue_amount` | number | Yes | Revenue amount excluding tax (≥ 0) |
| `tax_amount` | number | Yes | Tax amount calculated (≥ 0) |
| `currency` | string | Yes | ISO 4217 currency code (e.g., "MYR") |
| `timestamp` | string | Yes | ISO 8601 formatted timestamp |
| `metadata` | object | Yes | Additional context information |

#### Metadata Fields

| Field | Type | Description |
|-------|------|-------------|
| `source_service` | string | Always "finance-service" |
| `event_version` | string | Current version is "1.0" |
| `transaction_type` | string | Type of transaction (e.g., "sale", "refund") |
| `payment_method` | string | Payment method (e.g., "credit_card", "bank_transfer") |
| `description` | string | Human-readable transaction description |

## Example Events

### Standard Sale Transaction

```json
{
  "transaction_id": "TXN-order-123-**********",
  "order_reference": "order-123",
  "revenue_amount": 100.00,
  "tax_amount": 6.00,
  "currency": "MYR",
  "timestamp": "2025-08-02T15:30:00Z",
  "metadata": {
    "source_service": "finance-service",
    "event_version": "1.0",
    "transaction_type": "sale",
    "payment_method": "credit_card",
    "description": "Order ORD-123: Premium Product Package"
  }
}
```

### Multiple Items Transaction

```json
{
  "transaction_id": "TXN-order-456-**********",
  "order_reference": "order-456",
  "revenue_amount": 500.00,
  "tax_amount": 30.00,
  "currency": "MYR",
  "timestamp": "2025-08-02T15:35:00Z",
  "metadata": {
    "source_service": "finance-service",
    "event_version": "1.0",
    "transaction_type": "sale",
    "payment_method": "bank_transfer",
    "description": "Order ORD-456: Bulk Purchase (10 items)"
  }
}
```

## Integration Guidelines

### Kafka Consumer Setup

#### Consumer Configuration

```properties
# Kafka Consumer Configuration
bootstrap.servers=localhost:9092
group.id=external-accounting-system
auto.offset.reset=earliest
enable.auto.commit=true
auto.commit.interval.ms=1000
session.timeout.ms=30000
key.deserializer=org.apache.kafka.common.serialization.StringDeserializer
value.deserializer=org.apache.kafka.common.serialization.StringDeserializer
```

#### Topic Subscription

```java
// Java Example
Properties props = new Properties();
props.put("bootstrap.servers", "localhost:9092");
props.put("group.id", "external-accounting-system");
props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");

KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
consumer.subscribe(Arrays.asList("financial.records.created"));

while (true) {
    ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(100));
    for (ConsumerRecord<String, String> record : records) {
        // Process financial record event
        String eventJson = record.value();
        processFinancialEvent(eventJson);
    }
}
```

### Event Processing

#### Validation

Always validate incoming events before processing:

```python
# Python Example
import json
from datetime import datetime

def validate_financial_event(event_json):
    try:
        event = json.loads(event_json)
        
        # Required fields validation
        required_fields = [
            'transaction_id', 'order_reference', 'revenue_amount',
            'tax_amount', 'currency', 'timestamp', 'metadata'
        ]
        
        for field in required_fields:
            if field not in event:
                raise ValueError(f"Missing required field: {field}")
        
        # Type validation
        if not isinstance(event['revenue_amount'], (int, float)) or event['revenue_amount'] < 0:
            raise ValueError("Invalid revenue_amount")
        
        if not isinstance(event['tax_amount'], (int, float)) or event['tax_amount'] < 0:
            raise ValueError("Invalid tax_amount")
        
        # Timestamp validation
        datetime.fromisoformat(event['timestamp'].replace('Z', '+00:00'))
        
        return True
    except Exception as e:
        print(f"Event validation failed: {e}")
        return False
```

#### Idempotency

Events may be delivered multiple times. Implement idempotency using the `transaction_id`:

```sql
-- SQL Example for idempotent processing
INSERT INTO financial_records (
    transaction_id, order_reference, revenue_amount, 
    tax_amount, currency, processed_at
) VALUES (
    ?, ?, ?, ?, ?, NOW()
) ON DUPLICATE KEY UPDATE
    processed_at = NOW();
```

## Error Handling

### Common Error Scenarios

1. **Invalid JSON Format**
   - Log error and skip event
   - Alert monitoring system

2. **Missing Required Fields**
   - Log validation error
   - Send to dead letter queue for manual review

3. **Duplicate Events**
   - Check transaction_id for existing records
   - Skip processing if already exists

4. **Processing Failures**
   - Implement retry logic with exponential backoff
   - Send to dead letter queue after max retries

### Monitoring and Alerting

#### Key Metrics to Monitor

- Event processing rate
- Processing latency
- Error rates by type
- Dead letter queue size
- Consumer lag

#### Sample Monitoring Queries

```sql
-- Processing rate (events per minute)
SELECT 
    DATE_FORMAT(processed_at, '%Y-%m-%d %H:%i') as minute,
    COUNT(*) as events_processed
FROM financial_records 
WHERE processed_at >= NOW() - INTERVAL 1 HOUR
GROUP BY minute
ORDER BY minute;

-- Error rate by type
SELECT 
    error_type,
    COUNT(*) as error_count,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM processing_logs) as error_percentage
FROM processing_logs 
WHERE created_at >= NOW() - INTERVAL 1 DAY
GROUP BY error_type;
```

## Support and Troubleshooting

### Common Issues

1. **Events Not Received**
   - Check Kafka connectivity
   - Verify topic subscription
   - Check consumer group status

2. **Duplicate Processing**
   - Implement proper idempotency checks
   - Verify transaction_id uniqueness

3. **Schema Validation Failures**
   - Check event format against schema
   - Verify all required fields are present

### Contact Information

For technical support or integration questions:
- Email: <EMAIL>
- Slack: #finance-service-support
- Documentation: https://docs.company.com/finance-service

### Version History

| Version | Date | Changes |
|---------|------|---------|
| 1.0 | 2025-08-02 | Initial release with basic financial event schema |

---

*This documentation is maintained by the Finance Service team. Last updated: 2025-08-02*
