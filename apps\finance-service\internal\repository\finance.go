package repository

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/company/cdh/apps/finance-service/models"
)

// financeRepository implements the FinanceRepository interface
type financeRepository struct {
	db *sql.DB
}

// NewFinanceRepository creates a new finance repository
func NewFinanceRepository(db *sql.DB) FinanceRepository {
	return &financeRepository{db: db}
}

// CreateFinancialEntry creates a new financial entry
func (r *financeRepository) CreateFinancialEntry(entry *models.FinancialEntry) error {
	query := `
		INSERT INTO financial_entries (
			order_id, transaction_id, revenue_amount, tax_amount, 
			currency, payment_method, transaction_type, description,
			created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		RETURNING id, created_at, updated_at`

	now := time.Now()
	entry.CreatedAt = now
	entry.UpdatedAt = now

	err := r.db.QueryRow(
		query,
		entry.OrderID,
		entry.TransactionID,
		entry.RevenueAmount,
		entry.TaxAmount,
		entry.Currency,
		entry.PaymentMethod,
		entry.TransactionType,
		entry.Description,
		entry.CreatedAt,
		entry.UpdatedAt,
	).Scan(&entry.ID, &entry.CreatedAt, &entry.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create financial entry: %w", err)
	}

	return nil
}

// GetFinancialEntryByTransactionID retrieves a financial entry by transaction ID
func (r *financeRepository) GetFinancialEntryByTransactionID(transactionID string) (*models.FinancialEntry, error) {
	query := `
		SELECT id, order_id, transaction_id, revenue_amount, tax_amount,
			   currency, payment_method, transaction_type, description,
			   created_at, updated_at
		FROM financial_entries
		WHERE transaction_id = $1`

	entry := &models.FinancialEntry{}
	err := r.db.QueryRow(query, transactionID).Scan(
		&entry.ID,
		&entry.OrderID,
		&entry.TransactionID,
		&entry.RevenueAmount,
		&entry.TaxAmount,
		&entry.Currency,
		&entry.PaymentMethod,
		&entry.TransactionType,
		&entry.Description,
		&entry.CreatedAt,
		&entry.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("financial entry not found for transaction ID: %s", transactionID)
		}
		return nil, fmt.Errorf("failed to get financial entry: %w", err)
	}

	return entry, nil
}

// GetFinancialEntriesByOrderID retrieves all financial entries for an order
func (r *financeRepository) GetFinancialEntriesByOrderID(orderID string) ([]*models.FinancialEntry, error) {
	query := `
		SELECT id, order_id, transaction_id, revenue_amount, tax_amount,
			   currency, payment_method, transaction_type, description,
			   created_at, updated_at
		FROM financial_entries
		WHERE order_id = $1
		ORDER BY created_at DESC`

	rows, err := r.db.Query(query, orderID)
	if err != nil {
		return nil, fmt.Errorf("failed to query financial entries: %w", err)
	}
	defer rows.Close()

	var entries []*models.FinancialEntry
	for rows.Next() {
		entry := &models.FinancialEntry{}
		err := rows.Scan(
			&entry.ID,
			&entry.OrderID,
			&entry.TransactionID,
			&entry.RevenueAmount,
			&entry.TaxAmount,
			&entry.Currency,
			&entry.PaymentMethod,
			&entry.TransactionType,
			&entry.Description,
			&entry.CreatedAt,
			&entry.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan financial entry: %w", err)
		}
		entries = append(entries, entry)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating financial entries: %w", err)
	}

	return entries, nil
}

// CreateFinancialTransaction creates a new financial transaction
func (r *financeRepository) CreateFinancialTransaction(transaction *models.FinancialTransaction) error {
	query := `
		INSERT INTO financial_transactions (
			order_reference, event_id, processing_status, 
			financial_entry_id, processed_at, error_message, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id, created_at`

	now := time.Now()
	transaction.CreatedAt = now

	err := r.db.QueryRow(
		query,
		transaction.OrderReference,
		transaction.EventID,
		transaction.ProcessingStatus,
		transaction.FinancialEntryID,
		transaction.ProcessedAt,
		transaction.ErrorMessage,
		transaction.CreatedAt,
	).Scan(&transaction.ID, &transaction.CreatedAt)

	if err != nil {
		return fmt.Errorf("failed to create financial transaction: %w", err)
	}

	return nil
}

// GetFinancialTransactionByEventID retrieves a financial transaction by event ID
func (r *financeRepository) GetFinancialTransactionByEventID(eventID string) (*models.FinancialTransaction, error) {
	query := `
		SELECT id, order_reference, event_id, processing_status,
			   financial_entry_id, processed_at, error_message, created_at
		FROM financial_transactions
		WHERE event_id = $1`

	transaction := &models.FinancialTransaction{}
	err := r.db.QueryRow(query, eventID).Scan(
		&transaction.ID,
		&transaction.OrderReference,
		&transaction.EventID,
		&transaction.ProcessingStatus,
		&transaction.FinancialEntryID,
		&transaction.ProcessedAt,
		&transaction.ErrorMessage,
		&transaction.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("financial transaction not found for event ID: %s", eventID)
		}
		return nil, fmt.Errorf("failed to get financial transaction: %w", err)
	}

	return transaction, nil
}

// UpdateFinancialTransaction updates an existing financial transaction
func (r *financeRepository) UpdateFinancialTransaction(transaction *models.FinancialTransaction) error {
	query := `
		UPDATE financial_transactions 
		SET processing_status = $1, financial_entry_id = $2, 
			processed_at = $3, error_message = $4
		WHERE id = $5`

	_, err := r.db.Exec(
		query,
		transaction.ProcessingStatus,
		transaction.FinancialEntryID,
		transaction.ProcessedAt,
		transaction.ErrorMessage,
		transaction.ID,
	)

	if err != nil {
		return fmt.Errorf("failed to update financial transaction: %w", err)
	}

	return nil
}

// GetFinancialRecords retrieves financial records with filtering and pagination
func (r *financeRepository) GetFinancialRecords(filters *models.QueryFilters) ([]*models.FinancialEntry, int, error) {
	// Build the base query
	baseQuery := `
		SELECT id, order_id, transaction_id, revenue_amount, tax_amount,
			   currency, payment_method, transaction_type, description,
			   created_at, updated_at
		FROM financial_entries`

	countQuery := `SELECT COUNT(*) FROM financial_entries`

	var conditions []string
	var args []interface{}
	argIndex := 1

	// Add filters
	if filters.DateFrom != nil {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, *filters.DateFrom)
		argIndex++
	}

	if filters.DateTo != nil {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, *filters.DateTo)
		argIndex++
	}

	if filters.OrderReference != "" {
		conditions = append(conditions, fmt.Sprintf("order_id = $%d", argIndex))
		args = append(args, filters.OrderReference)
		argIndex++
	}

	// Note: Status filtering will be implemented when database schema is updated
	// For now, we'll ignore the status filter since the column doesn't exist yet

	// Build WHERE clause
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	// Get total count
	var total int
	err := r.db.QueryRow(countQuery+whereClause, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}

	// Add pagination
	offset := (filters.Page - 1) * filters.Limit
	paginationClause := fmt.Sprintf(" ORDER BY created_at DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, filters.Limit, offset)

	// Execute main query
	finalQuery := baseQuery + whereClause + paginationClause
	rows, err := r.db.Query(finalQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query financial records: %w", err)
	}
	defer rows.Close()

	var entries []*models.FinancialEntry
	for rows.Next() {
		entry := &models.FinancialEntry{}
		err := rows.Scan(
			&entry.ID,
			&entry.OrderID,
			&entry.TransactionID,
			&entry.RevenueAmount,
			&entry.TaxAmount,
			&entry.Currency,
			&entry.PaymentMethod,
			&entry.TransactionType,
			&entry.Description,
			&entry.CreatedAt,
			&entry.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan financial entry: %w", err)
		}
		entries = append(entries, entry)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("error iterating financial entries: %w", err)
	}

	return entries, total, nil
}

// GetFinancialRecordByID retrieves a financial record by ID
func (r *financeRepository) GetFinancialRecordByID(id int) (*models.FinancialEntry, error) {
	query := `
		SELECT id, order_id, transaction_id, revenue_amount, tax_amount,
			   currency, payment_method, transaction_type, description,
			   created_at, updated_at
		FROM financial_entries
		WHERE id = $1`

	entry := &models.FinancialEntry{}
	err := r.db.QueryRow(query, id).Scan(
		&entry.ID,
		&entry.OrderID,
		&entry.TransactionID,
		&entry.RevenueAmount,
		&entry.TaxAmount,
		&entry.Currency,
		&entry.PaymentMethod,
		&entry.TransactionType,
		&entry.Description,
		&entry.CreatedAt,
		&entry.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("financial record not found for ID: %d", id)
		}
		return nil, fmt.Errorf("failed to get financial record: %w", err)
	}

	return entry, nil
}

// UpdateFinancialRecordStatus updates the status of a financial record
func (r *financeRepository) UpdateFinancialRecordStatus(id int, status string, notes string) error {
	// First, verify the record exists
	_, err := r.GetFinancialRecordByID(id)
	if err != nil {
		return err
	}

	// Update the status, processing notes, and processed_at timestamp
	query := `
		UPDATE financial_entries
		SET status = $1, processing_notes = $2, processed_at = $3, updated_at = $4
		WHERE id = $5
	`

	now := time.Now()
	_, err = r.db.Exec(query, status, notes, now, now, id)
	if err != nil {
		return fmt.Errorf("failed to update financial record status: %w", err)
	}

	return nil
}

// CreateAuditTrailEntry creates a new audit trail entry
func (r *financeRepository) CreateAuditTrailEntry(entry *models.AuditTrailEntry) error {
	// Validate the entry
	if err := entry.Validate(); err != nil {
		return fmt.Errorf("invalid audit trail entry: %w", err)
	}

	query := `
		INSERT INTO audit_trail (record_id, old_status, new_status, notes, changed_by, changed_at, ip_address, user_agent)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id, changed_at
	`

	err := r.db.QueryRow(query,
		entry.RecordID,
		entry.OldStatus,
		entry.NewStatus,
		entry.Notes,
		entry.ChangedBy,
		entry.ChangedAt,
		entry.IPAddress,
		entry.UserAgent,
	).Scan(&entry.ID, &entry.ChangedAt)

	if err != nil {
		return fmt.Errorf("failed to create audit trail entry: %w", err)
	}

	return nil
}

// GetAuditTrailByRecordID retrieves audit trail entries for a specific record
func (r *financeRepository) GetAuditTrailByRecordID(recordID int) ([]*models.AuditTrailEntry, error) {
	if recordID <= 0 {
		return nil, fmt.Errorf("record_id must be greater than 0")
	}

	query := `
		SELECT id, record_id, old_status, new_status, notes, changed_by, changed_at, ip_address, user_agent
		FROM audit_trail
		WHERE record_id = $1
		ORDER BY changed_at DESC
	`

	rows, err := r.db.Query(query, recordID)
	if err != nil {
		return nil, fmt.Errorf("failed to query audit trail entries: %w", err)
	}
	defer rows.Close()

	var entries []*models.AuditTrailEntry
	for rows.Next() {
		entry := &models.AuditTrailEntry{}
		err := rows.Scan(
			&entry.ID,
			&entry.RecordID,
			&entry.OldStatus,
			&entry.NewStatus,
			&entry.Notes,
			&entry.ChangedBy,
			&entry.ChangedAt,
			&entry.IPAddress,
			&entry.UserAgent,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan audit trail entry: %w", err)
		}
		entries = append(entries, entry)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating audit trail entries: %w", err)
	}

	return entries, nil
}
