# Build stage
FROM golang:1.21-alpine AS builder

# Install security updates and ca-certificates
RUN apk update && apk add --no-cache ca-certificates git && \
    apk upgrade && \
    rm -rf /var/cache/apk/*

# Create non-root user for build
RUN adduser -D -s /bin/sh appuser

# Set working directory
WORKDIR /app

# Copy go mod and sum files first for better caching
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download && go mod verify

# Copy source code
COPY . .

# Build the application with optimizations
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o hello-world .

# Final stage - use distroless for better security
FROM gcr.io/distroless/static:nonroot

# Copy CA certificates for HTTPS requests
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy the binary from builder stage
COPY --from=builder /app/hello-world /hello-world

# Use non-root user
USER nonroot:nonroot

# Expose port 8080
EXPOSE 8080

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD ["/hello-world", "--health-check"] || exit 1

# Run the binary
ENTRYPOINT ["/hello-world"]
