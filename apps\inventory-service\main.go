package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/company/cdh/apps/inventory-service/handlers"
	"github.com/company/cdh/apps/inventory-service/internal/config"
	"github.com/company/cdh/apps/inventory-service/internal/consumer"
	"github.com/company/cdh/apps/inventory-service/internal/database"
	"github.com/company/cdh/apps/inventory-service/internal/producer"
	"github.com/company/cdh/apps/inventory-service/internal/repository"
	"github.com/company/cdh/apps/inventory-service/internal/services"
)

func main() {
	log.Println("Starting Inventory Service...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database connection
	db, err := database.Connect(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Run database migrations
	if err := database.RunMigrations(db); err != nil {
		log.Fatalf("Failed to run database migrations: %v", err)
	}

	// Initialize Redis cache
	redisClient, err := database.ConnectRedis(cfg.Redis)
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	defer redisClient.Close()

	// Initialize repository
	inventoryRepo := repository.NewInventoryRepository(db)

	// Initialize Kafka producer for failure events
	kafkaProducer, err := producer.NewKafkaProducer(cfg.Kafka)
	if err != nil {
		log.Fatalf("Failed to create Kafka producer: %v", err)
	}
	defer kafkaProducer.Close()

	// Initialize inventory processor
	inventoryProcessor := services.NewInventoryProcessor(inventoryRepo, db, kafkaProducer)

	// Initialize Kafka consumer
	kafkaConsumer, err := consumer.NewKafkaConsumer(cfg.Kafka, inventoryProcessor)
	if err != nil {
		log.Fatalf("Failed to create Kafka consumer: %v", err)
	}

	// Start HTTP server for health checks
	server := handlers.NewServer(cfg.Server, db, redisClient, inventoryProcessor)
	go func() {
		if err := server.Start(); err != nil {
			log.Fatalf("Failed to start HTTP server: %v", err)
		}
	}()

	// Start Kafka consumer
	ctx, cancel := context.WithCancel(context.Background())
	go func() {
		if err := kafkaConsumer.Start(ctx); err != nil {
			log.Printf("Kafka consumer error: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down Inventory Service...")

	// Cancel context to stop Kafka consumer
	cancel()

	// Shutdown HTTP server with timeout
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	if err := server.Shutdown(shutdownCtx); err != nil {
		log.Printf("Server shutdown error: %v", err)
	}

	// Close Kafka consumer
	if err := kafkaConsumer.Close(); err != nil {
		log.Printf("Kafka consumer close error: %v", err)
	}

	log.Println("Inventory Service stopped")
}
