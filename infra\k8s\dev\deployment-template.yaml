# Template for CDH microservice deployment
# Copy and modify this template for new services

apiVersion: apps/v1
kind: Deployment
metadata:
  name: <SERVICE_NAME>
  namespace: cdh-dev
  labels:
    app: <SERVICE_NAME>
    component: <COMPONENT_TYPE>  # e.g., api, worker, frontend
    environment: development
    project: cdh
spec:
  replicas: 1
  selector:
    matchLabels:
      app: <SERVICE_NAME>
  template:
    metadata:
      labels:
        app: <SERVICE_NAME>
        component: <COMPONENT_TYPE>
        environment: development
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 2000
      containers:
      - name: <SERVICE_NAME>
        image: <IMAGE_NAME>:<TAG>
        ports:
        - containerPort: <PORT>
          name: http
        env:
        - name: ENVIRONMENT
          value: "development"
        - name: LOG_LEVEL
          value: "debug"
        - name: SUPABASE_DB_URL
          value: "postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require"
        # Add service-specific environment variables here
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        # Uncomment if service needs persistent storage
        # volumeMounts:
        # - name: data
        #   mountPath: /data
      # Uncomment if service needs persistent storage
      # volumes:
      # - name: data
      #   persistentVolumeClaim:
      #     claimName: <SERVICE_NAME>-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: <SERVICE_NAME>-service
  namespace: cdh-dev
  labels:
    app: <SERVICE_NAME>
    component: <COMPONENT_TYPE>
    environment: development
spec:
  selector:
    app: <SERVICE_NAME>
  ports:
  - port: <PORT>
    targetPort: http
    protocol: TCP
    name: http
  type: ClusterIP

# Uncomment if service needs persistent storage
# ---
# apiVersion: v1
# kind: PersistentVolumeClaim
# metadata:
#   name: <SERVICE_NAME>-pvc
#   namespace: cdh-dev
#   labels:
#     app: <SERVICE_NAME>
# spec:
#   accessModes:
#     - ReadWriteOnce
#   resources:
#     requests:
#       storage: 1Gi
