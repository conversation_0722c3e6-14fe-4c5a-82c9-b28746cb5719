# 3. Technology Stack
| Category | Technology | Notes |
| :--- | :--- | :--- |
| **Language** | Go | High performance, concurrency model is ideal for high-traffic scenarios, simple deployment. |
| **Database** | PostgreSQL(Supabase) | A reliable relational database, providing an independent instance for each service. |
| **Cache** | Redis | Used for caching hot data, such as inventory queries, to improve response times. |
| **Message Queue** | Kafka | Serves as an event streaming platform, supporting asynchronous communication and future data analytics. |
| **API Gateway** | Traefik | Cloud-native, tightly integrated with K8s, automates service discovery and routing. |
| **Containerization** | Docker | A standardized way to package applications. |
| **Orchestration** | Kubernetes | Automates the deployment, scaling, and management of containerized applications. |
| **Monitoring** | Prometheus, Grafana | For collecting and visualizing system performance metrics. |
| **Logging** | EFK Stack | For centrally managing and querying logs from all microservices. |

