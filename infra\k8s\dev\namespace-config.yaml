apiVersion: v1
kind: Namespace
metadata:
  name: cdh-dev
  labels:
    name: cdh-dev
    environment: development
    project: cdh
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: cdh-dev-quota
  namespace: cdh-dev
spec:
  hard:
    requests.cpu: "2"
    requests.memory: 2Gi
    limits.cpu: "4"
    limits.memory: 4Gi
    pods: "10"
    services: "5"
    persistentvolumeclaims: "3"
    count/deployments.apps: "5"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: cdh-dev-limits
  namespace: cdh-dev
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
