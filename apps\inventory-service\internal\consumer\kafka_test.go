package consumer

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/company/cdh/apps/inventory-service/internal/config"
	"github.com/company/cdh/apps/inventory-service/models"
)

// MockInventoryProcessor is a mock implementation of InventoryProcessor
type MockInventoryProcessor struct {
	mock.Mock
}

func (m *MockInventoryProcessor) ProcessOrderEvent(ctx context.Context, event *models.OrderCreatedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *MockInventoryProcessor) ValidateOrderEvent(event *models.OrderCreatedEvent) error {
	args := m.Called(event)
	return args.Error(0)
}

func TestOrderEventHandler_processOrderEvent(t *testing.T) {
	t.Run("Valid order event", func(t *testing.T) {
		mockProcessor := &MockInventoryProcessor{}

		handler := &OrderEventHandler{
			ready:     make(chan bool),
			processor: mockProcessor,
		}
		event := models.OrderCreatedEvent{
			OrderID:    "order-123",
			CustomerID: "customer-456",
			Items: []models.OrderItem{
				{
					SKU:      "LAPTOP-001",
					Quantity: 2,
					Price:    999.99,
				},
				{
					SKU:      "MOUSE-001",
					Quantity: 1,
					Price:    79.99,
				},
			},
			Status:    "pending",
			CreatedAt: time.Now(),
		}

		data, err := json.Marshal(event)
		require.NoError(t, err)

		// Mock the processor to expect validation and processing
		mockProcessor.On("ValidateOrderEvent", mock.MatchedBy(func(e *models.OrderCreatedEvent) bool {
			return e.OrderID == "order-123" && e.CustomerID == "customer-456" && len(e.Items) == 2
		})).Return(nil)

		mockProcessor.On("ProcessOrderEvent", mock.Anything, mock.MatchedBy(func(e *models.OrderCreatedEvent) bool {
			return e.OrderID == "order-123" && e.CustomerID == "customer-456" && len(e.Items) == 2
		})).Return(nil)

		ctx := context.Background()
		err = handler.processOrderEvent(ctx, data)
		assert.NoError(t, err)
		mockProcessor.AssertExpectations(t)
	})

	t.Run("Invalid JSON", func(t *testing.T) {
		mockProcessor := &MockInventoryProcessor{}

		handler := &OrderEventHandler{
			ready:     make(chan bool),
			processor: mockProcessor,
		}

		invalidJSON := []byte(`{"invalid": json}`)

		ctx := context.Background()
		err := handler.processOrderEvent(ctx, invalidJSON)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "unmarshal")
	})

	t.Run("Invalid order event - empty order ID", func(t *testing.T) {
		mockProcessor := &MockInventoryProcessor{}

		handler := &OrderEventHandler{
			ready:     make(chan bool),
			processor: mockProcessor,
		}

		event := models.OrderCreatedEvent{
			OrderID:    "", // Invalid
			CustomerID: "customer-456",
			Items: []models.OrderItem{
				{
					SKU:      "LAPTOP-001",
					Quantity: 2,
					Price:    999.99,
				},
			},
			Status:    "pending",
			CreatedAt: time.Now(),
		}

		// Mock validation to return an error for invalid event
		mockProcessor.On("ValidateOrderEvent", mock.Anything).Return(assert.AnError)

		data, err := json.Marshal(event)
		require.NoError(t, err)

		ctx := context.Background()
		err = handler.processOrderEvent(ctx, data)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "validate")
		mockProcessor.AssertExpectations(t)
	})

	t.Run("Invalid order event - empty items", func(t *testing.T) {
		mockProcessor := &MockInventoryProcessor{}

		handler := &OrderEventHandler{
			ready:     make(chan bool),
			processor: mockProcessor,
		}

		event := models.OrderCreatedEvent{
			OrderID:    "order-123",
			CustomerID: "customer-456",
			Items:      []models.OrderItem{}, // Invalid - empty
			Status:     "pending",
			CreatedAt:  time.Now(),
		}

		// Mock validation to return an error for invalid event
		mockProcessor.On("ValidateOrderEvent", mock.Anything).Return(assert.AnError)

		data, err := json.Marshal(event)
		require.NoError(t, err)

		ctx := context.Background()
		err = handler.processOrderEvent(ctx, data)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "validate")
		mockProcessor.AssertExpectations(t)
	})

	t.Run("Invalid order event - invalid item", func(t *testing.T) {
		mockProcessor := &MockInventoryProcessor{}

		handler := &OrderEventHandler{
			ready:     make(chan bool),
			processor: mockProcessor,
		}

		event := models.OrderCreatedEvent{
			OrderID:    "order-123",
			CustomerID: "customer-456",
			Items: []models.OrderItem{
				{
					SKU:      "", // Invalid - empty SKU
					Quantity: 2,
					Price:    999.99,
				},
			},
			Status:    "pending",
			CreatedAt: time.Now(),
		}

		// Mock validation to return an error for invalid event
		mockProcessor.On("ValidateOrderEvent", mock.Anything).Return(assert.AnError)

		data, err := json.Marshal(event)
		require.NoError(t, err)

		ctx := context.Background()
		err = handler.processOrderEvent(ctx, data)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "validate")
		mockProcessor.AssertExpectations(t)
	})
}

func TestNewKafkaConsumer(t *testing.T) {
	t.Run("Valid configuration", func(t *testing.T) {
		cfg := config.KafkaConfig{
			Brokers:           []string{"localhost:9092"},
			GroupID:           "test-group",
			Topic:             "test.orders",
			SessionTimeout:    10 * time.Second,
			HeartbeatInterval: 3 * time.Second,
		}

		mockProcessor := &MockInventoryProcessor{}

		// Note: This will fail in actual test environment without Kafka
		// but tests the configuration validation
		consumer, err := NewKafkaConsumer(cfg, mockProcessor)

		// In a real test environment with Kafka, this would succeed
		// For unit tests, we expect it to fail due to no Kafka broker
		if err != nil {
			assert.Contains(t, err.Error(), "kafka")
		} else {
			assert.NotNil(t, consumer)
			consumer.Close()
		}
	})
}

func TestOrderEventHandler_Setup(t *testing.T) {
	handler := &OrderEventHandler{
		ready: make(chan bool),
	}

	err := handler.Setup(nil)
	assert.NoError(t, err)

	// Verify that ready channel is closed
	select {
	case <-handler.ready:
		// Channel is closed, which is expected
	case <-time.After(100 * time.Millisecond):
		t.Error("Ready channel should be closed after Setup")
	}
}

func TestOrderEventHandler_Cleanup(t *testing.T) {
	handler := &OrderEventHandler{
		ready: make(chan bool),
	}

	err := handler.Cleanup(nil)
	assert.NoError(t, err)
}

func TestOrderEventHandler_ConsumeClaim_Integration(t *testing.T) {
	// Note: This is a simplified test since we can't easily mock sarama interfaces
	// In a real environment, you would use testcontainers or similar for integration testing

	handler := &OrderEventHandler{
		ready: make(chan bool),
	}

	// Test that the handler can be created
	assert.NotNil(t, handler)
	assert.NotNil(t, handler.ready)
}
