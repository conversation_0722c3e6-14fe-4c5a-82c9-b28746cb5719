package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"log"
	"net/http"
	"time"

	"github.com/company/cdh/apps/finance-service/internal/config"
	"github.com/company/cdh/apps/finance-service/internal/middleware"
	"github.com/company/cdh/apps/finance-service/internal/services"
	"github.com/company/cdh/apps/finance-service/models"
)

// Server represents the HTTP server
type Server struct {
	server         *http.Server
	db             *sql.DB
	financeService services.FinanceServiceInterface
	authMiddleware *middleware.AuthMiddleware
}

// HealthResponse represents the health check response
type HealthResponse struct {
	Status       string            `json:"status"`
	Timestamp    time.Time         `json:"timestamp"`
	Service      string            `json:"service"`
	Version      string            `json:"version"`
	Dependencies map[string]string `json:"dependencies,omitempty"`
}

// NewServer creates a new HTTP server
func NewServer(cfg config.ServerConfig, db *sql.DB, financeService services.FinanceServiceInterface) *Server {
	mux := http.NewServeMux()

	// Initialize authentication middleware
	authMiddleware := middleware.NewAuthMiddleware(cfg.JWTSecret)

	// Add sample API keys for testing (in production, these would come from database)
	authMiddleware.AddAPIKey(&models.APIKeyInfo{
		ID:          "1",
		Key:         "test-api-key-12345",
		Name:        "Test External Platform",
		ClientID:    "external-platform-1",
		Permissions: []string{"financial_records:read", "financial_records:update"},
		IsActive:    true,
		CreatedAt:   time.Now(),
	})

	server := &Server{
		server: &http.Server{
			Addr:         ":" + cfg.Port,
			Handler:      mux,
			ReadTimeout:  cfg.ReadTimeout,
			WriteTimeout: cfg.WriteTimeout,
		},
		db:             db,
		financeService: financeService,
		authMiddleware: authMiddleware,
	}

	// Register public routes (no authentication required)
	mux.HandleFunc("/health", server.healthHandler)
	mux.HandleFunc("/health/live", server.livenessHandler)
	mux.HandleFunc("/health/ready", server.readinessHandler)

	// Configure CORS for API endpoints
	corsMiddleware := authMiddleware.CORS(
		[]string{"*"}, // In production, specify allowed origins
		[]string{"GET", "POST", "PATCH", "PUT", "DELETE", "OPTIONS"},
		[]string{"Content-Type", "Authorization", "X-API-Key"},
	)

	// Configure rate limiting (100 requests per minute)
	rateLimitMiddleware := authMiddleware.RateLimit(100, 60)

	// API v1 routes for external platforms (with authentication and rate limiting)
	mux.HandleFunc("/api/v1/financial-records",
		corsMiddleware(
			rateLimitMiddleware(
				authMiddleware.AuthenticateAPIKey(
					authMiddleware.RequirePermission("financial_records:read")(
						server.GetFinancialRecords,
					),
				),
			),
		),
	)

	mux.HandleFunc("/api/v1/financial-records/status",
		corsMiddleware(
			rateLimitMiddleware(
				authMiddleware.AuthenticateAPIKey(
					authMiddleware.RequirePermission("financial_records:update")(
						server.UpdateFinancialRecordStatus,
					),
				),
			),
		),
	)

	// RESTful status update endpoint (preferred) - with authentication
	mux.HandleFunc("/api/v1/financial-records/",
		corsMiddleware(
			rateLimitMiddleware(
				authMiddleware.AuthenticateAPIKey(
					authMiddleware.RequirePermission("financial_records:update")(
						server.UpdateFinancialRecordStatusByID,
					),
				),
			),
		),
	)

	return server
}

// Handler returns the HTTP handler for testing purposes
func (s *Server) Handler() http.Handler {
	return s.server.Handler
}

// Start starts the HTTP server
func (s *Server) Start() error {
	log.Printf("Starting HTTP server on %s", s.server.Addr)
	return s.server.ListenAndServe()
}

// Shutdown gracefully shuts down the HTTP server
func (s *Server) Shutdown(ctx context.Context) error {
	log.Println("Shutting down HTTP server...")
	return s.server.Shutdown(ctx)
}

// healthHandler handles comprehensive health checks
func (s *Server) healthHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	response := HealthResponse{
		Status:       "healthy",
		Timestamp:    time.Now(),
		Service:      "finance-service",
		Version:      "1.0.0",
		Dependencies: make(map[string]string),
	}

	// Check database connectivity
	if err := s.db.Ping(); err != nil {
		response.Status = "unhealthy"
		response.Dependencies["database"] = "unhealthy: " + err.Error()
	} else {
		response.Dependencies["database"] = "healthy"
	}

	// Set appropriate status code
	statusCode := http.StatusOK
	if response.Status == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// livenessHandler handles Kubernetes liveness probes
func (s *Server) livenessHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	response := HealthResponse{
		Status:    "alive",
		Timestamp: time.Now(),
		Service:   "finance-service",
		Version:   "1.0.0",
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// readinessHandler handles Kubernetes readiness probes
func (s *Server) readinessHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	response := HealthResponse{
		Status:    "ready",
		Timestamp: time.Now(),
		Service:   "finance-service",
		Version:   "1.0.0",
	}

	// Check if service is ready to handle requests
	if err := s.db.Ping(); err != nil {
		response.Status = "not ready"
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusServiceUnavailable)
		json.NewEncoder(w).Encode(response)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}
