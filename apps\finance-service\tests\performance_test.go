package tests

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/company/cdh/apps/finance-service/internal/services"
	"github.com/company/cdh/apps/finance-service/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestPerformance_GetFinancialRecords tests the performance of the GetFinancialRecords endpoint
func TestPerformance_GetFinancialRecords(t *testing.T) {
	// Setup
	mockRepo := NewMockFinanceRepository()
	mockProducer := NewMockEventProducer()
	financeService := services.NewFinanceService(mockRepo, mockProducer)

	// Create test data - simulate large dataset
	testDataSize := 1000
	for i := 0; i < testDataSize; i++ {
		entry := &models.FinancialEntry{
			ID:              i + 1,
			OrderID:         fmt.Sprintf("order-%d", i+1),
			TransactionID:   fmt.Sprintf("txn-%d", i+1),
			RevenueAmount:   10000 + float64(i*100),
			TaxAmount:       600 + float64(i*6),
			Currency:        "MYR",
			PaymentMethod:   "credit_card",
			TransactionType: "sale",
			Description:     fmt.Sprintf("Test transaction %d", i+1),
			CreatedAt:       time.Now().Add(-time.Duration(i) * time.Hour),
			UpdatedAt:       time.Now().Add(-time.Duration(i) * time.Hour),
		}
		mockRepo.entries = append(mockRepo.entries, entry)
	}

	testCases := []struct {
		name        string
		filters     *models.QueryFilters
		expectedMax time.Duration
	}{
		{
			name: "Default pagination (page 1, limit 20)",
			filters: &models.QueryFilters{
				Page:  1,
				Limit: 20,
			},
			expectedMax: 100 * time.Millisecond,
		},
		{
			name: "Large page size (limit 100)",
			filters: &models.QueryFilters{
				Page:  1,
				Limit: 100,
			},
			expectedMax: 200 * time.Millisecond,
		},
		{
			name: "Date range filter",
			filters: &models.QueryFilters{
				Page:     1,
				Limit:    20,
				DateFrom: timePtr(time.Now().Add(-24 * time.Hour)),
				DateTo:   timePtr(time.Now()),
			},
			expectedMax: 150 * time.Millisecond,
		},
		{
			name: "Order reference filter",
			filters: &models.QueryFilters{
				Page:           1,
				Limit:          20,
				OrderReference: "order-500",
			},
			expectedMax: 100 * time.Millisecond,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Validate filters
			tc.filters.Validate()

			// Measure execution time
			start := time.Now()
			records, total, err := financeService.GetFinancialRecords(tc.filters)
			duration := time.Since(start)

			// Assertions
			require.NoError(t, err)
			assert.NotNil(t, records)
			assert.Greater(t, total, 0)
			assert.LessOrEqual(t, duration, tc.expectedMax,
				"Query took %v, expected max %v", duration, tc.expectedMax)

			t.Logf("Query completed in %v (max: %v), returned %d records out of %d total",
				duration, tc.expectedMax, len(records), total)
		})
	}
}

// TestPerformance_ConcurrentRequests tests the service under concurrent load
func TestPerformance_ConcurrentRequests(t *testing.T) {
	// Setup
	mockRepo := NewMockFinanceRepository()
	mockProducer := NewMockEventProducer()
	financeService := services.NewFinanceService(mockRepo, mockProducer)

	// Create test data
	testDataSize := 500
	for i := 0; i < testDataSize; i++ {
		entry := &models.FinancialEntry{
			ID:              i + 1,
			OrderID:         fmt.Sprintf("order-%d", i+1),
			TransactionID:   fmt.Sprintf("txn-%d", i+1),
			RevenueAmount:   10000,
			TaxAmount:       600,
			Currency:        "MYR",
			PaymentMethod:   "credit_card",
			TransactionType: "sale",
			Description:     fmt.Sprintf("Test transaction %d", i+1),
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		}
		mockRepo.entries = append(mockRepo.entries, entry)
	}

	// Test concurrent requests
	concurrentRequests := 50
	maxDuration := 500 * time.Millisecond

	var wg sync.WaitGroup
	var mu sync.Mutex
	durations := make([]time.Duration, 0, concurrentRequests)
	errors := make([]error, 0)

	start := time.Now()

	for i := 0; i < concurrentRequests; i++ {
		wg.Add(1)
		go func(requestID int) {
			defer wg.Done()

			filters := &models.QueryFilters{
				Page:  1,
				Limit: 20,
			}
			filters.Validate()

			requestStart := time.Now()
			records, total, err := financeService.GetFinancialRecords(filters)
			requestDuration := time.Since(requestStart)

			mu.Lock()
			durations = append(durations, requestDuration)
			if err != nil {
				errors = append(errors, err)
			}
			mu.Unlock()

			// Verify response
			assert.NoError(t, err)
			assert.NotNil(t, records)
			assert.Greater(t, total, 0)
		}(i)
	}

	wg.Wait()
	totalDuration := time.Since(start)

	// Analyze results
	require.Empty(t, errors, "Some requests failed")
	require.Len(t, durations, concurrentRequests)

	// Calculate statistics
	var totalRequestTime time.Duration
	maxRequestTime := time.Duration(0)
	minRequestTime := time.Duration(1<<63 - 1) // Max duration

	for _, d := range durations {
		totalRequestTime += d
		if d > maxRequestTime {
			maxRequestTime = d
		}
		if d < minRequestTime {
			minRequestTime = d
		}
	}

	avgRequestTime := totalRequestTime / time.Duration(len(durations))

	// Assertions
	assert.LessOrEqual(t, maxRequestTime, maxDuration,
		"Slowest request took %v, expected max %v", maxRequestTime, maxDuration)

	t.Logf("Concurrent performance results:")
	t.Logf("  Total time: %v", totalDuration)
	t.Logf("  Requests: %d", concurrentRequests)
	t.Logf("  Avg request time: %v", avgRequestTime)
	t.Logf("  Min request time: %v", minRequestTime)
	t.Logf("  Max request time: %v", maxRequestTime)
	t.Logf("  Throughput: %.2f requests/second", float64(concurrentRequests)/totalDuration.Seconds())
}

// TestPerformance_StatusUpdates tests the performance of status update operations
func TestPerformance_StatusUpdates(t *testing.T) {
	// Setup
	mockRepo := NewMockFinanceRepository()
	mockProducer := NewMockEventProducer()
	financeService := services.NewFinanceService(mockRepo, mockProducer)

	// Create test data
	testDataSize := 100
	for i := 0; i < testDataSize; i++ {
		entry := &models.FinancialEntry{
			ID:              i + 1,
			OrderID:         fmt.Sprintf("order-%d", i+1),
			TransactionID:   fmt.Sprintf("txn-%d", i+1),
			RevenueAmount:   10000,
			TaxAmount:       600,
			Currency:        "MYR",
			PaymentMethod:   "credit_card",
			TransactionType: "sale",
			Description:     fmt.Sprintf("Test transaction %d", i+1),
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		}
		mockRepo.entries = append(mockRepo.entries, entry)
	}

	// Test batch status updates
	batchSize := 10
	maxDurationPerUpdate := 50 * time.Millisecond

	start := time.Now()
	for i := 1; i <= batchSize; i++ {
		updateStart := time.Now()
		err := financeService.UpdateFinancialRecordStatus(i, "processed", "Performance test update")
		updateDuration := time.Since(updateStart)

		require.NoError(t, err)
		assert.LessOrEqual(t, updateDuration, maxDurationPerUpdate,
			"Status update %d took %v, expected max %v", i, updateDuration, maxDurationPerUpdate)
	}
	totalDuration := time.Since(start)

	t.Logf("Batch status update performance:")
	t.Logf("  Total time: %v", totalDuration)
	t.Logf("  Updates: %d", batchSize)
	t.Logf("  Avg time per update: %v", totalDuration/time.Duration(batchSize))
}

// TestPerformance_MemoryUsage tests memory efficiency with large datasets
func TestPerformance_MemoryUsage(t *testing.T) {
	// Setup
	mockRepo := NewMockFinanceRepository()
	mockProducer := NewMockEventProducer()
	financeService := services.NewFinanceService(mockRepo, mockProducer)

	// Create large test dataset
	testDataSize := 5000
	for i := 0; i < testDataSize; i++ {
		entry := &models.FinancialEntry{
			ID:              i + 1,
			OrderID:         fmt.Sprintf("order-%d", i+1),
			TransactionID:   fmt.Sprintf("txn-%d", i+1),
			RevenueAmount:   10000,
			TaxAmount:       600,
			Currency:        "MYR",
			PaymentMethod:   "credit_card",
			TransactionType: "sale",
			Description:     fmt.Sprintf("Test transaction %d", i+1),
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		}
		mockRepo.entries = append(mockRepo.entries, entry)
	}

	// Test pagination with large dataset
	filters := &models.QueryFilters{
		Page:  1,
		Limit: 100,
	}
	filters.Validate()

	start := time.Now()
	records, total, err := financeService.GetFinancialRecords(filters)
	duration := time.Since(start)

	// Assertions
	require.NoError(t, err)
	assert.NotNil(t, records)
	assert.Equal(t, 100, len(records), "Should return exactly 100 records")
	assert.Equal(t, testDataSize, total, "Total should match dataset size")
	assert.LessOrEqual(t, duration, 300*time.Millisecond, "Large dataset query should complete within 300ms")

	t.Logf("Large dataset performance:")
	t.Logf("  Dataset size: %d records", testDataSize)
	t.Logf("  Query time: %v", duration)
	t.Logf("  Records returned: %d", len(records))
}

// Helper functions
func timePtr(t time.Time) *time.Time {
	return &t
}
