package models

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestOrderCreatedEvent_Validate(t *testing.T) {
	tests := []struct {
		name    string
		event   OrderCreatedEvent
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid order event",
			event: OrderCreatedEvent{
				EventID:     "event-123",
				EventType:   "order.created",
				OrderID:     "order-123",
				OrderNumber: "ORD-001",
				Price:       100.00,
				Quantity:    2,
			},
			wantErr: false,
		},
		{
			name: "missing event ID",
			event: OrderCreatedEvent{
				EventType:   "order.created",
				OrderID:     "order-123",
				OrderNumber: "ORD-001",
				Price:       100.00,
				Quantity:    2,
			},
			wantErr: true,
			errMsg:  "event_id is required",
		},
		{
			name: "missing event type",
			event: OrderCreatedEvent{
				EventID:     "event-123",
				OrderID:     "order-123",
				OrderNumber: "ORD-001",
				Price:       100.00,
				Quantity:    2,
			},
			wantErr: true,
			errMsg:  "event_type is required",
		},
		{
			name: "missing order ID",
			event: OrderCreatedEvent{
				EventID:     "event-123",
				EventType:   "order.created",
				OrderNumber: "ORD-001",
				Price:       100.00,
				Quantity:    2,
			},
			wantErr: true,
			errMsg:  "order_id is required",
		},
		{
			name: "missing order number",
			event: OrderCreatedEvent{
				EventID:   "event-123",
				EventType: "order.created",
				OrderID:   "order-123",
				Price:     100.00,
				Quantity:  2,
			},
			wantErr: true,
			errMsg:  "order_number is required",
		},
		{
			name: "negative price",
			event: OrderCreatedEvent{
				EventID:     "event-123",
				EventType:   "order.created",
				OrderID:     "order-123",
				OrderNumber: "ORD-001",
				Price:       -100.00,
				Quantity:    2,
			},
			wantErr: true,
			errMsg:  "price cannot be negative",
		},
		{
			name: "zero quantity",
			event: OrderCreatedEvent{
				EventID:     "event-123",
				EventType:   "order.created",
				OrderID:     "order-123",
				OrderNumber: "ORD-001",
				Price:       100.00,
				Quantity:    0,
			},
			wantErr: true,
			errMsg:  "quantity must be positive",
		},
		{
			name: "negative quantity",
			event: OrderCreatedEvent{
				EventID:     "event-123",
				EventType:   "order.created",
				OrderID:     "order-123",
				OrderNumber: "ORD-001",
				Price:       100.00,
				Quantity:    -1,
			},
			wantErr: true,
			errMsg:  "quantity must be positive",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.event.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestParseOrderEvent(t *testing.T) {
	validEvent := OrderCreatedEvent{
		EventID:     "event-123",
		EventType:   "order.created",
		Timestamp:   time.Now(),
		OrderID:     "order-123",
		OrderNumber: "ORD-001",
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       100.00,
		Status:      "confirmed",
		CreatedAt:   time.Now(),
	}

	validJSON, err := json.Marshal(validEvent)
	assert.NoError(t, err)

	t.Run("valid JSON", func(t *testing.T) {
		parsed, err := ParseOrderEvent(validJSON)
		assert.NoError(t, err)
		assert.Equal(t, validEvent.EventID, parsed.EventID)
		assert.Equal(t, validEvent.OrderID, parsed.OrderID)
		assert.Equal(t, validEvent.Price, parsed.Price)
		assert.Equal(t, validEvent.Quantity, parsed.Quantity)
	})

	t.Run("invalid JSON", func(t *testing.T) {
		invalidJSON := []byte(`{"invalid": "json"`)
		_, err := ParseOrderEvent(invalidJSON)
		assert.Error(t, err)
	})

	t.Run("valid JSON but invalid event", func(t *testing.T) {
		invalidEvent := map[string]interface{}{
			"event_id": "",
			"price":    -100,
		}
		invalidJSON, _ := json.Marshal(invalidEvent)
		_, err := ParseOrderEvent(invalidJSON)
		assert.Error(t, err)
	})
}

func TestOrderCreatedEvent_CalculateRevenue(t *testing.T) {
	event := OrderCreatedEvent{
		Price:    50.00,
		Quantity: 3,
	}

	revenue := event.CalculateRevenue()
	assert.Equal(t, 150.00, revenue)
}

func TestOrderCreatedEvent_CalculateTax(t *testing.T) {
	event := OrderCreatedEvent{
		Price:    100.00,
		Quantity: 1,
	}

	tax := event.CalculateTax()
	expected := 100.00 * 0.06 // 6% SST
	assert.Equal(t, expected, tax)
	assert.Equal(t, 6.00, tax)
}

func TestOrderCreatedEvent_CalculateTax_MultipleItems(t *testing.T) {
	event := OrderCreatedEvent{
		Price:    50.00,
		Quantity: 2,
	}

	tax := event.CalculateTax()
	expected := (50.00 * 2) * 0.06 // 6% SST on total revenue
	assert.Equal(t, expected, tax)
	assert.Equal(t, 6.00, tax)
}