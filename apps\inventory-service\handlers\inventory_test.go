package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/company/cdh/apps/inventory-service/models"
)

// MockInventoryService is a mock implementation of InventoryService
type MockInventoryService struct {
	mock.Mock
}

func (m *MockInventoryService) GetInventoryBySKU(ctx context.Context, sku string) (*models.Inventory, error) {
	args := m.Called(ctx, sku)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Inventory), args.Error(1)
}

func (m *MockInventoryService) GetInventoryBySKUs(ctx context.Context, skus []string) ([]*models.Inventory, error) {
	args := m.Called(ctx, skus)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.Inventory), args.Error(1)
}

func (m *MockInventoryService) UpdateInventory(ctx context.Context, inventory *models.Inventory) error {
	args := m.Called(ctx, inventory)
	return args.Error(0)
}

func (m *MockInventoryService) ReserveStock(ctx context.Context, sku string, quantity int) error {
	args := m.Called(ctx, sku, quantity)
	return args.Error(0)
}

func (m *MockInventoryService) ReleaseStock(ctx context.Context, sku string, quantity int) error {
	args := m.Called(ctx, sku, quantity)
	return args.Error(0)
}

func (m *MockInventoryService) DeductStock(ctx context.Context, sku string, quantity int) error {
	args := m.Called(ctx, sku, quantity)
	return args.Error(0)
}

func TestInventoryHandler_GetInventory(t *testing.T) {
	tests := []struct {
		name           string
		queryParam     string
		mockSetup      func(*MockInventoryService)
		expectedStatus int
		expectedCount  int
		expectedError  string
	}{
		{
			name:       "successful single SKU query",
			queryParam: "SKU001",
			mockSetup: func(m *MockInventoryService) {
				inventory := &models.Inventory{
					ID:                1,
					SKU:               "SKU001",
					ProductName:       "Test Product",
					StockQuantity:     100,
					ReservedQuantity:  10,
					AvailableQuantity: 90,
					UnitPrice:         29.99,
					CreatedAt:         time.Now(),
					UpdatedAt:         time.Now(),
				}
				m.On("GetInventoryBySKUs", mock.Anything, []string{"SKU001"}).Return([]*models.Inventory{inventory}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:       "successful multiple SKU query",
			queryParam: "SKU001,SKU002,SKU003",
			mockSetup: func(m *MockInventoryService) {
				inventories := []*models.Inventory{
					{
						ID:                1,
						SKU:               "SKU001",
						ProductName:       "Product 1",
						StockQuantity:     100,
						ReservedQuantity:  10,
						AvailableQuantity: 90,
						UnitPrice:         29.99,
					},
					{
						ID:                2,
						SKU:               "SKU002",
						ProductName:       "Product 2",
						StockQuantity:     50,
						ReservedQuantity:  5,
						AvailableQuantity: 45,
						UnitPrice:         19.99,
					},
				}
				m.On("GetInventoryBySKUs", mock.Anything, []string{"SKU001", "SKU002", "SKU003"}).Return(inventories, nil)
			},
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
		{
			name:           "missing SKU parameter",
			queryParam:     "",
			mockSetup:      func(m *MockInventoryService) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "SKU parameter is required",
		},
		{
			name:           "empty SKU parameter",
			queryParam:     "   ",
			mockSetup:      func(m *MockInventoryService) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "At least one valid SKU is required",
		},
		{
			name:       "whitespace handling",
			queryParam: " SKU001 , SKU002 , ",
			mockSetup: func(m *MockInventoryService) {
				inventory := &models.Inventory{
					ID:                1,
					SKU:               "SKU001",
					ProductName:       "Test Product",
					StockQuantity:     100,
					ReservedQuantity:  10,
					AvailableQuantity: 90,
					UnitPrice:         29.99,
				}
				m.On("GetInventoryBySKUs", mock.Anything, []string{"SKU001", "SKU002"}).Return([]*models.Inventory{inventory}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockService := new(MockInventoryService)
			tt.mockSetup(mockService)

			handler := NewInventoryHandler(mockService)

			// Create request
			reqURL := "/inventory"
			if tt.queryParam != "" {
				reqURL += "?sku=" + url.QueryEscape(tt.queryParam)
			}
			req := httptest.NewRequest(http.MethodGet, reqURL, nil)
			w := httptest.NewRecorder()

			// Execute
			handler.GetInventory(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				assert.Contains(t, w.Body.String(), tt.expectedError)
			} else {
				var response InventoryQueryResponse
				err := json.NewDecoder(w.Body).Decode(&response)
				require.NoError(t, err)
				assert.Equal(t, tt.expectedCount, response.Count)
				assert.Len(t, response.Items, tt.expectedCount)
			}

			mockService.AssertExpectations(t)
		})
	}
}

func TestInventoryHandler_GetInventory_MethodNotAllowed(t *testing.T) {
	mockService := new(MockInventoryService)
	handler := NewInventoryHandler(mockService)

	req := httptest.NewRequest(http.MethodPost, "/inventory", nil)
	w := httptest.NewRecorder()

	handler.GetInventory(w, req)

	assert.Equal(t, http.StatusMethodNotAllowed, w.Code)
	assert.Contains(t, w.Body.String(), "Method not allowed")
}

func TestInventoryHandler_GetInventory_TooManySKUs(t *testing.T) {
	mockService := new(MockInventoryService)
	handler := NewInventoryHandler(mockService)

	// Create a query with more than 100 SKUs
	skus := make([]string, 101)
	for i := 0; i < 101; i++ {
		skus[i] = fmt.Sprintf("SKU%03d", i)
	}
	queryParam := ""
	for i, sku := range skus {
		if i > 0 {
			queryParam += ","
		}
		queryParam += sku
	}

	req := httptest.NewRequest(http.MethodGet, "/inventory?sku="+url.QueryEscape(queryParam), nil)
	w := httptest.NewRecorder()

	handler.GetInventory(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
	assert.Contains(t, w.Body.String(), "Too many SKUs requested")
}
