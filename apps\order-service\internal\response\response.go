package response

import (
	"encoding/json"
	"net/http"
)

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message,omitempty"`
	Code    int    `json:"code"`
}

// SuccessResponse represents a success response
type SuccessResponse struct {
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
}

// WriteJSON writes a JSON response
func WriteJSON(w http.ResponseWriter, statusCode int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(data)
}

// WriteError writes an error response
func WriteError(w http.ResponseWriter, statusCode int, err error, message ...string) {
	msg := ""
	if len(message) > 0 {
		msg = message[0]
	}

	response := ErrorResponse{
		Error:   err.<PERSON><PERSON><PERSON>(),
		Message: msg,
		Code:    statusCode,
	}

	WriteJSON(w, statusCode, response)
}

// WriteSuccess writes a success response
func WriteSuccess(w http.ResponseWriter, statusCode int, data interface{}, message ...string) {
	msg := ""
	if len(message) > 0 {
		msg = message[0]
	}

	response := SuccessResponse{
		Data:    data,
		Message: msg,
	}

	WriteJSON(w, statusCode, response)
}