package database

import (
	"database/sql"
	"fmt"
	"log"
)

// RunMigrations runs all database migrations
func RunMigrations(db *sql.DB) error {
	log.Println("Running database migrations...")

	migrations := []struct {
		name string
		sql  string
	}{
		{
			name: "create_financial_entries_table",
			sql:  createFinancialEntriesTable,
		},
		{
			name: "create_financial_transactions_table",
			sql:  createFinancialTransactionsTable,
		},
		{
			name: "create_audit_trail_table",
			sql:  createAuditTrailTable,
		},
		{
			name: "add_status_fields_to_financial_entries",
			sql:  addStatusFieldsToFinancialEntries,
		},
		{
			name: "create_financial_entries_indexes",
			sql:  createFinancialEntriesIndexes,
		},
		{
			name: "create_financial_transactions_indexes",
			sql:  createFinancialTransactionsIndexes,
		},
		{
			name: "create_audit_trail_indexes",
			sql:  createAuditTrailIndexes,
		},
	}

	for _, migration := range migrations {
		if err := runMigration(db, migration.name, migration.sql); err != nil {
			return fmt.Errorf("failed to run migration %s: %w", migration.name, err)
		}
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// runMigration executes a single migration
func runMigration(db *sql.DB, name, sql string) error {
	log.Printf("Running migration: %s", name)

	_, err := db.Exec(sql)
	if err != nil {
		return fmt.Errorf("migration %s failed: %w", name, err)
	}

	log.Printf("Migration %s completed", name)
	return nil
}

// createFinancialEntriesTable creates the financial_entries table
const createFinancialEntriesTable = `
CREATE TABLE IF NOT EXISTS financial_entries (
    id SERIAL PRIMARY KEY,
    order_id VARCHAR(255) NOT NULL,
    transaction_id VARCHAR(255) NOT NULL UNIQUE,
    revenue_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'MYR',
    payment_method VARCHAR(50) NOT NULL,
    transaction_type VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);`

// createFinancialTransactionsTable creates the financial_transactions table
const createFinancialTransactionsTable = `
CREATE TABLE IF NOT EXISTS financial_transactions (
    id SERIAL PRIMARY KEY,
    order_reference VARCHAR(255) NOT NULL,
    event_id VARCHAR(255) NOT NULL UNIQUE,
    processing_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    financial_entry_id INTEGER REFERENCES financial_entries(id),
    processed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);`

// createFinancialEntriesIndexes creates indexes for the financial_entries table
const createFinancialEntriesIndexes = `
CREATE INDEX IF NOT EXISTS idx_financial_entries_order_id ON financial_entries(order_id);
CREATE INDEX IF NOT EXISTS idx_financial_entries_transaction_id ON financial_entries(transaction_id);
CREATE INDEX IF NOT EXISTS idx_financial_entries_created_at ON financial_entries(created_at);
CREATE INDEX IF NOT EXISTS idx_financial_entries_currency ON financial_entries(currency);`

// createFinancialTransactionsIndexes creates indexes for the financial_transactions table
const createFinancialTransactionsIndexes = `
CREATE INDEX IF NOT EXISTS idx_financial_transactions_order_reference ON financial_transactions(order_reference);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_event_id ON financial_transactions(event_id);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_status ON financial_transactions(processing_status);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_created_at ON financial_transactions(created_at);`

// createAuditTrailTable creates the audit_trail table
const createAuditTrailTable = `
CREATE TABLE IF NOT EXISTS audit_trail (
    id SERIAL PRIMARY KEY,
    record_id INTEGER NOT NULL,
    old_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    notes TEXT,
    changed_by VARCHAR(255) NOT NULL,
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT
);`

// addStatusFieldsToFinancialEntries adds status-related fields to financial_entries table
const addStatusFieldsToFinancialEntries = `
ALTER TABLE financial_entries
ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS processed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS processing_notes TEXT;`

// createAuditTrailIndexes creates indexes for the audit_trail table
const createAuditTrailIndexes = `
CREATE INDEX IF NOT EXISTS idx_audit_trail_record_id ON audit_trail(record_id);
CREATE INDEX IF NOT EXISTS idx_audit_trail_changed_at ON audit_trail(changed_at);
CREATE INDEX IF NOT EXISTS idx_audit_trail_changed_by ON audit_trail(changed_by);`
