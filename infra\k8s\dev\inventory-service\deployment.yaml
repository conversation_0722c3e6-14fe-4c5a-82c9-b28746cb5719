apiVersion: apps/v1
kind: Deployment
metadata:
  name: inventory-service
  namespace: cdh-dev
  labels:
    app: inventory-service
    component: api
    environment: development
    project: cdh
spec:
  replicas: 1
  selector:
    matchLabels:
      app: inventory-service
  template:
    metadata:
      labels:
        app: inventory-service
        component: api
        environment: development
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 2000
      containers:
      - name: inventory-service
        image: inventory-service:latest
        imagePullPolicy: Never  # Use local image for development
        ports:
        - containerPort: 8081
          name: http
        env:
        - name: ENVIRONMENT
          value: "development"
        - name: LOG_LEVEL
          value: "debug"
        - name: PORT
          value: "8081"
        - name: DB_HOST
          value: "localhost"
        - name: DB_PORT
          value: "5434"
        - name: DB_USER
          value: "postgres"
        - name: DB_PASSWORD
          value: "postgres"
        - name: DB_NAME
          value: "inventory_db"
        - name: DB_SSLMODE
          value: "disable"
        - name: REDIS_HOST
          value: "localhost"
        - name: REDIS_PORT
          value: "6379"
        - name: KAFKA_BROKERS
          value: "localhost:9092"
        - name: KAFKA_GROUP_ID
          value: "inventory-service-group"
        - name: KAFKA_TOPIC
          value: "orders.created"
        - name: KAFKA_FAILURE_TOPIC
          value: "inventory.failures"
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        livenessProbe:
          httpGet:
            path: /health/live
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        # Add volume mount for temporary files if needed
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: inventory-service
  namespace: cdh-dev
  labels:
    app: inventory-service
    component: api
    environment: development
spec:
  selector:
    app: inventory-service
  ports:
  - port: 8081
    targetPort: http
    protocol: TCP
    name: http
  type: ClusterIP
