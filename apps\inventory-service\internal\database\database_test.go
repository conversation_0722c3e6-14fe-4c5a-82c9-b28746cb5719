package database

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/company/cdh/apps/inventory-service/internal/config"
)

func TestConnect(t *testing.T) {
	t.Run("Invalid configuration", func(t *testing.T) {
		cfg := config.DatabaseConfig{
			Host:     "invalid-host-that-does-not-exist",
			Port:     "5432",
			User:     "invalid_user",
			Password: "invalid_pass",
			Name:     "invalid_db",
			SSLMode:  "disable",
		}

		db, err := Connect(cfg)

		// Should return an error for invalid configuration
		assert.Error(t, err)
		assert.Nil(t, db)
		assert.Contains(t, err.<PERSON>rror(), "failed to ping database")
	})

	t.Run("Empty configuration", func(t *testing.T) {
		cfg := config.DatabaseConfig{}

		db, err := Connect(cfg)

		// Should return an error for empty configuration
		assert.Error(t, err)
		assert.Nil(t, db)
		assert.Contains(t, err.<PERSON><PERSON><PERSON>(), "failed to ping database")
	})
}

func TestConnectRedis(t *testing.T) {
	t.Run("Invalid configuration", func(t *testing.T) {
		cfg := config.RedisConfig{
			Host:     "invalid-host-that-does-not-exist",
			Port:     "6379",
			Password: "",
			DB:       0,
		}

		client, err := ConnectRedis(cfg)

		// Should return an error for invalid configuration
		assert.Error(t, err)
		assert.Nil(t, client)
		assert.Contains(t, err.Error(), "failed to ping Redis")
	})

	t.Run("Empty configuration", func(t *testing.T) {
		cfg := config.RedisConfig{}

		client, err := ConnectRedis(cfg)

		// Should return an error for empty configuration
		assert.Error(t, err)
		assert.Nil(t, client)
		assert.Contains(t, err.Error(), "failed to ping Redis")
	})
}

// Note: For integration tests with a real database, you would need:
// 1. A test database instance (e.g., using testcontainers)
// 2. Test migrations
// 3. Cleanup procedures
//
// Example integration test structure:
//
// func TestConnect_Integration(t *testing.T) {
//     if testing.Short() {
//         t.Skip("Skipping integration test")
//     }
//
//     // Setup test database container
//     cfg := config.DatabaseConfig{
//         Host:     "localhost",
//         Port:     "5434",
//         User:     "test_user",
//         Password: "test_pass",
//         DBName:   "test_db",
//         SSLMode:  "disable",
//     }
//
//     db, err := Connect(cfg)
//     require.NoError(t, err)
//     defer db.Close()
//
//     // Test connection
//     err = db.Ping()
//     assert.NoError(t, err)
// }
//
// func TestRunMigrations_Integration(t *testing.T) {
//     if testing.Short() {
//         t.Skip("Skipping integration test")
//     }
//
//     // Setup test database
//     db := setupTestDB(t)
//     defer db.Close()
//
//     err := RunMigrations(db)
//     assert.NoError(t, err)
//
//     // Verify migrations were applied
//     var count int
//     err = db.QueryRow("SELECT COUNT(*) FROM inventory").Scan(&count)
//     assert.NoError(t, err)
// }
