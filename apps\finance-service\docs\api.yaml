openapi: 3.0.3
info:
  title: Finance Service API
  description: |
    External API for accounting platform integration with the CDH Finance Service.
    
    This API provides endpoints for external accounting platforms to:
    - Query financial records with filtering and pagination
    - Update financial record status for processing workflows
    - Access comprehensive audit trails for compliance
    
    ## Authentication
    
    This API supports two authentication methods:
    
    ### API Key Authentication
    Include your API key in the `X-API-Key` header:
    ```
    X-API-Key: your-api-key-here
    ```
    
    ### JWT Token Authentication
    Include your JWT token in the `Authorization` header:
    ```
    Authorization: Bearer your-jwt-token-here
    ```
    
    ## Rate Limiting
    
    API requests are rate limited to 100 requests per minute per client.
    Rate limit information is included in response headers:
    - `X-RateLimit-Limit`: Maximum requests allowed
    - `X-RateLimit-Remaining`: Remaining requests in current window
    - `X-RateLimit-Reset`: Unix timestamp when the rate limit resets
    
    ## Error Handling
    
    All errors follow a consistent format:
    ```json
    {
      "error": "Error Type",
      "message": "Human readable error message",
      "code": 400,
      "details": {
        "field": "Additional error details"
      }
    }
    ```
    
  version: 1.0.0
  contact:
    name: CDH Development Team
    email: <EMAIL>
  license:
    name: Proprietary
    
servers:
  - url: https://api.cdh.company/finance/v1
    description: Production server
  - url: https://staging-api.cdh.company/finance/v1
    description: Staging server
  - url: http://localhost:8082/api/v1
    description: Development server

security:
  - ApiKeyAuth: []
  - BearerAuth: []

paths:
  /financial-records:
    get:
      summary: Get financial records
      description: |
        Retrieve financial records with optional filtering and pagination.
        
        Supports filtering by:
        - Date range (date_from, date_to)
        - Order reference
        - Processing status
        
        Results are paginated with configurable page size.
      operationId: getFinancialRecords
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 1
        - name: limit
          in: query
          description: Number of records per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: date_from
          in: query
          description: Start date for filtering (YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
            example: "2024-01-01"
        - name: date_to
          in: query
          description: End date for filtering (YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
            example: "2024-12-31"
        - name: order_reference
          in: query
          description: Filter by order reference
          required: false
          schema:
            type: string
            minLength: 3
            maxLength: 100
            pattern: '^[a-zA-Z0-9_-]+$'
            example: "ORD-2024-001"
        - name: status
          in: query
          description: Filter by processing status
          required: false
          schema:
            type: string
            enum: [pending, processed, requires_attention, completed]
            example: "processed"
      responses:
        '200':
          description: Financial records retrieved successfully
          headers:
            X-RateLimit-Limit:
              description: Maximum requests allowed per minute
              schema:
                type: integer
            X-RateLimit-Remaining:
              description: Remaining requests in current window
              schema:
                type: integer
            X-RateLimit-Reset:
              description: Unix timestamp when rate limit resets
              schema:
                type: integer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FinancialRecordsListResponse'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '429':
          description: Rate limit exceeded
          headers:
            Retry-After:
              description: Seconds to wait before retrying
              schema:
                type: integer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /financial-records/{id}/status:
    patch:
      summary: Update financial record status
      description: |
        Update the processing status of a specific financial record.
        
        This endpoint supports idempotent operations - repeated requests
        with the same parameters will not cause side effects.
        
        All status changes are recorded in an audit trail for compliance.
      operationId: updateFinancialRecordStatus
      security:
        - ApiKeyAuth: []
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          description: Financial record ID
          required: true
          schema:
            type: integer
            minimum: 1
            example: 12345
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StatusUpdateRequest'
      responses:
        '200':
          description: Status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatusUpdateResponse'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Financial record not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '429':
          description: Rate limit exceeded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for external platform authentication
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for secure access

  schemas:
    FinancialRecordResponse:
      type: object
      description: Financial record data for external platforms
      properties:
        id:
          type: integer
          description: Unique financial record identifier
          example: 12345
        order_id:
          type: string
          description: Associated order identifier
          example: "ORD-2024-001"
        transaction_id:
          type: string
          description: Unique transaction identifier
          example: "TXN-ORD-2024-001-1234567890"
        revenue_amount:
          type: number
          format: decimal
          description: Revenue amount in cents
          example: 10000
        tax_amount:
          type: number
          format: decimal
          description: Tax amount in cents
          example: 600
        total_amount:
          type: number
          format: decimal
          description: Total amount (revenue + tax) in cents
          example: 10600
        currency:
          type: string
          description: Currency code (ISO 4217)
          example: "MYR"
        payment_method:
          type: string
          description: Payment method used
          example: "credit_card"
        transaction_type:
          type: string
          description: Type of transaction
          example: "sale"
        description:
          type: string
          description: Transaction description
          example: "Product purchase - Widget A"
        created_at:
          type: string
          format: date-time
          description: Record creation timestamp
          example: "2024-01-15T10:30:00Z"
        updated_at:
          type: string
          format: date-time
          description: Record last update timestamp
          example: "2024-01-15T10:30:00Z"
      required:
        - id
        - order_id
        - transaction_id
        - revenue_amount
        - tax_amount
        - total_amount
        - currency
        - payment_method
        - transaction_type
        - created_at
        - updated_at

    PaginationMetadata:
      type: object
      description: Pagination information for list responses
      properties:
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Records per page
          example: 20
        total_records:
          type: integer
          description: Total number of records
          example: 150
        total_pages:
          type: integer
          description: Total number of pages
          example: 8
        has_next:
          type: boolean
          description: Whether there are more pages
          example: true
        has_previous:
          type: boolean
          description: Whether there are previous pages
          example: false
      required:
        - page
        - limit
        - total_records
        - total_pages
        - has_next
        - has_previous

    FinancialRecordsListResponse:
      type: object
      description: Paginated list of financial records
      properties:
        data:
          type: array
          description: Array of financial records
          items:
            $ref: '#/components/schemas/FinancialRecordResponse'
        pagination:
          $ref: '#/components/schemas/PaginationMetadata'
      required:
        - data
        - pagination

    StatusUpdateRequest:
      type: object
      description: Request to update financial record status
      properties:
        status:
          type: string
          description: New processing status
          enum: [processed, requires_attention, completed]
          example: "processed"
        notes:
          type: string
          description: Optional notes about the status change
          maxLength: 500
          example: "Processed successfully by external accounting system"
      required:
        - status

    StatusUpdateResponse:
      type: object
      description: Response after status update
      properties:
        id:
          type: integer
          description: Financial record ID
          example: 12345
        status:
          type: string
          description: Updated status
          example: "processed"
        updated_at:
          type: string
          format: date-time
          description: Update timestamp
          example: "2024-01-15T10:35:00Z"
        message:
          type: string
          description: Success message
          example: "Status updated successfully"
      required:
        - id
        - status
        - updated_at
        - message

    ErrorResponse:
      type: object
      description: Standard error response format
      properties:
        error:
          type: string
          description: Error type or category
          example: "Validation Error"
        message:
          type: string
          description: Human-readable error message
          example: "Invalid status value provided"
        code:
          type: integer
          description: HTTP status code
          example: 400
        details:
          type: object
          description: Additional error details
          additionalProperties:
            type: string
          example:
            field: "status"
            allowed_values: "processed, requires_attention, completed"
      required:
        - error
        - message
        - code

  examples:
    FinancialRecordsExample:
      summary: Example financial records response
      value:
        data:
          - id: 12345
            order_id: "ORD-2024-001"
            transaction_id: "TXN-ORD-2024-001-1234567890"
            revenue_amount: 10000
            tax_amount: 600
            total_amount: 10600
            currency: "MYR"
            payment_method: "credit_card"
            transaction_type: "sale"
            description: "Product purchase - Widget A"
            created_at: "2024-01-15T10:30:00Z"
            updated_at: "2024-01-15T10:30:00Z"
          - id: 12346
            order_id: "ORD-2024-002"
            transaction_id: "TXN-ORD-2024-002-**********"
            revenue_amount: 5000
            tax_amount: 300
            total_amount: 5300
            currency: "MYR"
            payment_method: "bank_transfer"
            transaction_type: "sale"
            description: "Service payment - Consultation"
            created_at: "2024-01-15T11:00:00Z"
            updated_at: "2024-01-15T11:00:00Z"
        pagination:
          page: 1
          limit: 20
          total_records: 150
          total_pages: 8
          has_next: true
          has_previous: false

    StatusUpdateExample:
      summary: Example status update request
      value:
        status: "processed"
        notes: "Successfully processed by QuickBooks integration"

    ErrorExample:
      summary: Example error response
      value:
        error: "Validation Error"
        message: "Invalid status value provided"
        code: 400
        details:
          field: "status"
          allowed_values: "processed, requires_attention, completed"
