package config

import (
	"os"
	"time"
)

// Config holds all configuration for the finance service
type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	Kafka    KafkaConfig
}

// ServerConfig holds HTTP server configuration
type ServerConfig struct {
	Port         string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	JWTSecret    string
	APIKeySecret string
}

// DatabaseConfig holds database connection configuration
type DatabaseConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	Name     string
	SSLMode  string
}

// KafkaConfig holds Kafka configuration
type KafkaConfig struct {
	Brokers       []string
	GroupID       string
	Topic         string
	ProducerTopic string
}

// Load loads configuration from environment variables
func Load() *Config {
	return &Config{
		Server: ServerConfig{
			Port:         getEnv("PORT", "8082"),
			ReadTimeout:  parseDuration(getEnv("READ_TIMEOUT", "15s")),
			WriteTimeout: parseDuration(getEnv("WRITE_TIMEOUT", "15s")),
			JWTSecret:    getEnv("JWT_SECRET", "default-jwt-secret-for-development"),
			APIKeySecret: getEnv("API_KEY_SECRET", "default-api-key-secret-for-development"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5435"),
			User:     getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", "postgres"),
			Name:     getEnv("DB_NAME", "finance_db"),
			SSLMode:  getEnv("DB_SSLMODE", "disable"),
		},
		Kafka: KafkaConfig{
			Brokers:       []string{getEnv("KAFKA_BROKERS", "localhost:9092")},
			GroupID:       getEnv("KAFKA_GROUP_ID", "finance-service-group"),
			Topic:         getEnv("KAFKA_TOPIC", "orders.created"),
			ProducerTopic: getEnv("KAFKA_PRODUCER_TOPIC", "financial.records.created"),
		},
	}
}

// getEnv gets an environment variable or returns a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// parseDuration parses a duration string or returns a default
func parseDuration(s string) time.Duration {
	d, err := time.ParseDuration(s)
	if err != nil {
		return 15 * time.Second
	}
	return d
}

// DSN returns the database connection string
func (d DatabaseConfig) DSN() string {
	return "host=" + d.Host + " port=" + d.Port + " user=" + d.User +
		" password=" + d.Password + " dbname=" + d.Name + " sslmode=" + d.SSLMode
}
