package consumer

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/company/cdh/apps/finance-service/internal/config"
	"github.com/company/cdh/apps/finance-service/models"
)

// MockFinanceService is a mock implementation of FinanceServiceInterface
type MockFinanceService struct {
	mock.Mock
}

func (m *MockFinanceService) ProcessOrderEvent(messageValue []byte) error {
	args := m.Called(messageValue)
	return args.Error(0)
}

func (m *MockFinanceService) PublishFinancialEvent(financialEntry *models.FinancialEntry) error {
	args := m.Called(financialEntry)
	return args.Error(0)
}

func (m *MockFinanceService) GetFinancialRecords(filters *models.QueryFilters) ([]*models.FinancialEntry, int, error) {
	args := m.Called(filters)
	return args.Get(0).([]*models.FinancialEntry), args.Int(1), args.Error(2)
}

func (m *MockFinanceService) UpdateFinancialRecordStatus(id int, status string, notes string) error {
	args := m.Called(id, status, notes)
	return args.Error(0)
}

func TestNewKafkaConsumer(t *testing.T) {
	cfg := config.KafkaConfig{
		Brokers: []string{"localhost:9092"},
		GroupID: "test-group",
		Topic:   "test-topic",
	}

	mockService := &MockFinanceService{}

	consumer, err := NewKafkaConsumer(cfg, mockService)

	assert.NoError(t, err)
	assert.NotNil(t, consumer)
	assert.Equal(t, []string{"test-topic"}, consumer.topics)
	assert.Equal(t, mockService, consumer.financeService)
}

func TestOrderEventHandler_Setup(t *testing.T) {
	mockService := &MockFinanceService{}
	handler := &OrderEventHandler{
		financeService: mockService,
	}

	err := handler.Setup(nil)
	assert.NoError(t, err)
}

func TestOrderEventHandler_Cleanup(t *testing.T) {
	mockService := &MockFinanceService{}
	handler := &OrderEventHandler{
		financeService: mockService,
	}

	err := handler.Cleanup(nil)
	assert.NoError(t, err)
}

func TestOrderEventHandler_processOrderEvent(t *testing.T) {
	mockService := &MockFinanceService{}
	handler := &OrderEventHandler{
		financeService: mockService,
	}

	testMessage := []byte(`{"event_id": "test-123", "order_id": "order-123"}`)

	t.Run("successful processing", func(t *testing.T) {
		mockService.On("ProcessOrderEvent", testMessage).Return(nil).Once()

		err := handler.processOrderEvent(testMessage)
		assert.NoError(t, err)

		mockService.AssertExpectations(t)
	})

	t.Run("processing error", func(t *testing.T) {
		mockService.On("ProcessOrderEvent", testMessage).Return(assert.AnError).Once()

		err := handler.processOrderEvent(testMessage)
		assert.Error(t, err)

		mockService.AssertExpectations(t)
	})
}
