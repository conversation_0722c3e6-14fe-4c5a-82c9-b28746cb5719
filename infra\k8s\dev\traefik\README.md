# Traefik API Gateway - Development Environment

This directory contains Kubernetes manifests for deploying Traefik v3.x as an API Gateway in the `cdh-dev` namespace.

## Overview

Traefik serves as the API Gateway for the CDH Platform, providing:
- **Automatic Service Discovery**: Discovers services via Kubernetes API
- **Dynamic Routing**: Routes traffic based on IngressRoute configurations
- **Load Balancing**: Distributes traffic across service instances
- **Dashboard & Monitoring**: Web UI for monitoring routes and services
- **Health Checks**: Built-in health monitoring for backend services

## Architecture

```
External Traffic → Traefik (Port 80/443) → IngressRoute → Kubernetes Service → Pod
```

## Files

- `rbac.yaml` - Service account, ClusterRole, and ClusterRoleBinding for Traefik
- `configmap.yaml` - Traefik v3.x configuration with Kubernetes providers
- `deployment.yaml` - Traefik deployment with security contexts and resource limits
- `service.yaml` - LoadBalancer service for external access and ClusterIP for dashboard
- `ingressroute.yaml` - Example routing rules for Hello World service

## Deployment

### Prerequisites

1. Kubernetes cluster with `cdh-dev` namespace
2. Traefik CRDs installed (automatically handled by deployment)
3. Hello World service deployed (from Story 1.2)

### Deploy Traefik

```bash
# Deploy all components
kubectl apply -f infra/k8s/dev/traefik/

# Verify deployment
kubectl get pods -n cdh-dev -l app=traefik
kubectl get svc -n cdh-dev -l app=traefik
kubectl get ingressroute -n cdh-dev
```

### Verify Installation

```bash
# Check Traefik logs
kubectl logs -n cdh-dev -l app=traefik

# Test routing (requires port-forward)
kubectl port-forward -n cdh-dev pod/$(kubectl get pod -n cdh-dev -l app=traefik -o jsonpath='{.items[0].metadata.name}') 8080:80
curl http://localhost:8080/health
curl http://localhost:8080/hello-world
```

## Local Access

### Web Traffic (Port 80)

```bash
# Forward web traffic port
kubectl port-forward -n cdh-dev svc/traefik 8080:80

# Test endpoints
curl http://localhost:8080/health
curl http://localhost:8080/hello-world
curl http://localhost:8080/hello-world/health
```

### Dashboard Access (Port 8080)

```bash
# Forward dashboard port
kubectl port-forward -n cdh-dev svc/traefik-dashboard 8081:8080

# Access dashboard
open http://localhost:8081
```

### API Access

```bash
# Get all routers
curl http://localhost:8081/api/http/routers | jq

# Get all services
curl http://localhost:8081/api/http/services | jq

# Get metrics
curl http://localhost:8081/metrics
```

## Routing Configuration

### IngressRoute Example

```yaml
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: hello-world-route
  namespace: cdh-dev
spec:
  entryPoints:
    - web
  routes:
    - match: PathPrefix(`/hello-world`)
      kind: Rule
      services:
        - name: hello-world-service
          port: 8080
```

### Supported Route Matchers

- `Path(/exact/path)` - Exact path match
- `PathPrefix(/prefix)` - Path prefix match
- `Host(example.com)` - Host header match
- `Method(GET,POST)` - HTTP method match

## Troubleshooting

### Common Issues

#### 1. 404 Not Found
```bash
# Check if IngressRoute exists
kubectl get ingressroute -n cdh-dev

# Verify service discovery
kubectl port-forward -n cdh-dev svc/traefik-dashboard 8081:8080
curl http://localhost:8081/api/http/services | jq '.[] | select(.name | contains("hello-world"))'
```

#### 2. Service Not Discovered
```bash
# Check RBAC permissions
kubectl auth can-i get ingressroutes --as=system:serviceaccount:cdh-dev:traefik

# Check Traefik logs for discovery errors
kubectl logs -n cdh-dev -l app=traefik | grep -i error
```

#### 3. Port Forward Issues
```bash
# Use different local port
kubectl port-forward -n cdh-dev pod/traefik-xxx 8082:80

# Check pod status
kubectl get pods -n cdh-dev -l app=traefik
kubectl describe pod -n cdh-dev -l app=traefik
```

#### 4. Configuration Issues
```bash
# Validate ConfigMap
kubectl get configmap traefik-config -n cdh-dev -o yaml

# Check for syntax errors in logs
kubectl logs -n cdh-dev -l app=traefik | grep -i "configuration"
```

### Debug Commands

```bash
# Get all Traefik resources
kubectl get all -n cdh-dev -l app=traefik

# Check service endpoints
kubectl get endpoints -n cdh-dev hello-world-service

# Test internal connectivity
kubectl exec -n cdh-dev deployment/traefik -- wget -qO- http://hello-world-service:8080/health

# Monitor real-time logs
kubectl logs -n cdh-dev -l app=traefik -f
```

### Health Checks

```bash
# Traefik ping endpoint
kubectl exec -n cdh-dev deployment/traefik -- wget -qO- http://localhost:8080/ping

# Backend service health
kubectl exec -n cdh-dev deployment/traefik -- wget -qO- http://localhost:80/health
```

## Configuration Reference

### Entry Points
- `web` (Port 80) - HTTP traffic
- `websecure` (Port 443) - HTTPS traffic (future)
- `traefik` (Port 8080) - Dashboard and API

### Providers
- `kubernetesIngress` - Standard Kubernetes Ingress resources
- `kubernetesCRD` - Traefik IngressRoute CRDs

### Security
- Non-root user (UID 65532)
- Read-only root filesystem
- Resource limits: 100m CPU, 128Mi memory
- Security contexts enabled

## Next Steps

1. **TLS/HTTPS Setup** - Configure SSL certificates for websecure entry point
2. **Authentication** - Add middleware for authentication/authorization
3. **Rate Limiting** - Implement rate limiting middleware
4. **Monitoring** - Integrate with Prometheus/Grafana
5. **Production Config** - Adjust for production environment requirements
