package services

import "github.com/company/cdh/apps/finance-service/models"

// FinanceServiceInterface defines the interface for finance service operations
type FinanceServiceInterface interface {
	// ProcessOrderEvent processes an order event from Kafka
	ProcessOrderEvent(messageValue []byte) error

	// PublishFinancialEvent publishes a financial record event to external systems
	PublishFinancialEvent(financialEntry *models.FinancialEntry) error

	// API operations for external platforms
	GetFinancialRecords(filters *models.QueryFilters) ([]*models.FinancialEntry, int, error)
	UpdateFinancialRecordStatus(id int, status string, notes string) error
}
