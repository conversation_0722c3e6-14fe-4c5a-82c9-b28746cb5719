package events

import (
	"encoding/json"
	"fmt"
	"log"

	"github.com/IBM/sarama"
	"github.com/company/cdh/apps/order-service/internal/config"
)

// Producer interface for event publishing
type Producer interface {
	PublishOrderCreated(event *OrderCreatedEvent) error
	Close() error
}

// KafkaProducer implements Producer interface using Kafka
type KafkaProducer struct {
	producer sarama.SyncProducer
	topic    string
}

// NewKafkaProducer creates a new Kafka producer
func NewKafkaProducer(cfg *config.KafkaConfig) (*KafkaProducer, error) {
	config := sarama.NewConfig()
	config.Producer.RequiredAcks = sarama.WaitForAll // Wait for all in-sync replicas to ack
	config.Producer.Retry.Max = 5                    // Retry up to 5 times
	config.Producer.Return.Successes = true

	producer, err := sarama.NewSyncProducer(cfg.Brokers, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create Kafka producer: %w", err)
	}

	return &KafkaProducer{
		producer: producer,
		topic:    cfg.Topic,
	}, nil
}

// PublishOrderCreated publishes an order created event to Kafka
func (p *KafkaProducer) PublishOrderCreated(event *OrderCreatedEvent) error {
	// Serialize event to JSON
	eventData, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal order created event: %w", err)
	}

	// Create Kafka message
	// Using order number as partition key ensures:
	// 1. All events for the same order go to the same partition (ordering guarantee)
	// 2. Even distribution across partitions for different orders
	// 3. Consumer can process events for the same order sequentially
	msg := &sarama.ProducerMessage{
		Topic: p.topic,
		Key:   sarama.StringEncoder(event.OrderNumber), // Use order number as key for partitioning
		Value: sarama.ByteEncoder(eventData),
	}

	// Send message
	partition, offset, err := p.producer.SendMessage(msg)
	if err != nil {
		return fmt.Errorf("failed to send order created event: %w", err)
	}

	log.Printf("Order created event published successfully: partition=%d, offset=%d, order=%s",
		partition, offset, event.OrderNumber)

	return nil
}

// Close closes the Kafka producer
func (p *KafkaProducer) Close() error {
	if p.producer != nil {
		return p.producer.Close()
	}
	return nil
}
