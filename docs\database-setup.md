# Database Setup Guide

## Overview

The CDH platform uses independent PostgreSQL instances for each microservice to ensure true microservices architecture compliance with complete database isolation, independent scaling, and fault tolerance.

## Database Architecture

| Service | Database | Port | Container | Volume |
|---------|----------|------|-----------|--------|
| User & Permissions | `user_db` | 5432 | `cdh-user-db` | `user_db_data` |
| Order Management | `order_db` | 5433 | `cdh-order-db` | `order_db_data` |
| Inventory Management | `inventory_db` | 5434 | `cdh-inventory-db` | `inventory_db_data` |
| Finance & Tax | `finance_db` | 5435 | `cdh-finance-db` | `finance_db_data` |

## Development Setup

### Prerequisites

- Docker and Docker Compose installed
- PostgreSQL client tools (optional, for manual database operations)

### Starting Database Infrastructure

1. **Start all databases:**
   ```bash
   docker-compose -f docker-compose.databases.yml up -d
   ```

2. **Check database health:**
   ```bash
   docker-compose -f docker-compose.databases.yml ps
   ```

3. **View database logs:**
   ```bash
   docker-compose -f docker-compose.databases.yml logs user-db
   ```

### Connecting to Databases

#### User Service Database
```bash
# Using Docker
docker exec -it cdh-user-db psql -U postgres -d user_db

# Using local psql client
psql -h localhost -p 5432 -U postgres -d user_db
```

#### Other Service Databases
```bash
# Order Service (when implemented)
docker exec -it cdh-order-db psql -U postgres -d order_db

# Inventory Service (when implemented)  
docker exec -it cdh-inventory-db psql -U postgres -d inventory_db

# Finance Service (when implemented)
docker exec -it cdh-finance-db psql -U postgres -d finance_db
```

## Service Configuration

### User Service

The User Service is configured to use the independent PostgreSQL database through environment variables:

```env
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=user_db
DB_SSLMODE=disable
```

### Migration from Supabase

If migrating from a previous Supabase setup:

1. **Export existing data:**
   ```bash
   # Run against Supabase database
   psql "postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require" -f scripts/export-supabase-data.sql
   ```

2. **Start independent databases:**
   ```bash
   docker-compose -f docker-compose.databases.yml up -d
   ```

3. **Import data:**
   ```bash
   # Customize scripts/import-user-data.sql with actual data
   psql -h localhost -p 5432 -U postgres -d user_db -f scripts/import-user-data.sql
   ```

## Production Deployment

For production environments, databases should be deployed using Kubernetes StatefulSets with:

- Persistent volumes for data storage
- Resource limits and requests
- Backup and recovery procedures
- Monitoring and alerting
- Security configurations (SSL, authentication)

## Troubleshooting

### Common Issues

1. **Connection refused:**
   - Ensure Docker containers are running
   - Check port availability
   - Verify firewall settings

2. **Authentication failed:**
   - Verify username/password in environment variables
   - Check database initialization logs

3. **Database not found:**
   - Ensure initialization scripts ran successfully
   - Check container logs for errors

### Useful Commands

```bash
# Stop all databases
docker-compose -f docker-compose.databases.yml down

# Remove all data (destructive)
docker-compose -f docker-compose.databases.yml down -v

# Restart specific database
docker-compose -f docker-compose.databases.yml restart user-db

# View real-time logs
docker-compose -f docker-compose.databases.yml logs -f user-db
```

## Security Considerations

- Change default passwords in production
- Use SSL/TLS for database connections
- Implement proper network security
- Regular security updates
- Database access auditing
- Backup encryption