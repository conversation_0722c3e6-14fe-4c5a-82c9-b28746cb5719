# 4. Technical Assumptions

### Repository Structure
* **Monorepo**.

### Service Architecture
* **Microservices Architecture**.

### Testing Requirements
* **Unit + Integration Testing**.

### Additional Technical Assumptions
* **Language**: Go
* **Containerization**: Docker
* **Orchestration**: Kubernetes (K8s)
* **Database**: PostgreSQL (independent per service)
* **Cache**: Redis
* **Message Queue**: Kafka
* **API Gateway**: Traefik
* **Monitoring & Logging**: Prometheus, Grafana, EFK Stack

