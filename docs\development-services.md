# CDH Development Services

This document describes how to set up and manage the development services for the CDH platform.

## Services Overview

### Core Services
- **PostgreSQL Databases** (4 instances)
  - User DB: `localhost:5432`
  - Order DB: `localhost:5433`
  - Inventory DB: `localhost:5434`
  - Finance DB: `localhost:5435`

- **Kafka** (Message Broker)
  - Kafka: `localhost:9092`
  - Zookeeper: `localhost:2181`

- **Redis** (Cache)
  - Redis: `localhost:6379`

### Management Tools (Optional)
- **Kafka UI**: `http://localhost:8080`
- **Redis Commander**: `http://localhost:8081` (admin/admin)

## Quick Start

### 1. Start All Services
```powershell
# Start databases, Kafka, and Redis
.\scripts\dev\start-services.ps1

# Or start specific service groups
.\scripts\dev\start-services.ps1 -Services databases
.\scripts\dev\start-services.ps1 -Services messaging
.\scripts\dev\start-services.ps1 -Services cache
```

### 2. Start with Management Tools
```powershell
# Start all services including web UIs
.\scripts\dev\start-services.ps1 -Services all
.\scripts\dev\start-services.ps1 -Services tools
```

### 3. Check Service Status
```powershell
.\scripts\dev\start-services.ps1 -Status
```

### 4. View Logs
```powershell
# All services
.\scripts\dev\start-services.ps1 -Logs

# Specific service
docker-compose -f docker-compose.services.yml logs -f kafka
```

### 5. Stop Services
```powershell
# Stop all
.\scripts\dev\start-services.ps1 -Stop

# Stop specific group
.\scripts\dev\start-services.ps1 -Services messaging -Stop
```

## Manual Docker Compose Commands

### Start Databases Only
```bash
docker-compose -f docker-compose.databases.yml up -d
```

### Start Kafka and Redis
```bash
docker-compose -f docker-compose.services.yml up -d zookeeper kafka redis
```

### Start Management Tools
```bash
docker-compose -f docker-compose.services.yml --profile tools up -d
```

## Testing with Full Services

### Run Tests with Kafka and Redis
```bash
# Start all services first
.\scripts\dev\start-services.ps1

# Run tests (they will now use Kafka and Redis)
cd apps/inventory-service
go test ./... -v
```

### Expected Test Behavior
- **With services running**: All tests pass, including Kafka and Redis integration tests
- **Without services**: Tests gracefully skip Kafka/Redis tests, core functionality still tested

## Service Configuration

### Kafka Topics
The following topics are auto-created:
- `order-events` - Order creation events
- `inventory-failures` - Inventory processing failures

### Redis Configuration
- **Memory limit**: 256MB
- **Eviction policy**: allkeys-lru (remove least recently used keys)
- **Persistence**: AOF (Append Only File) enabled

### Database Credentials
All PostgreSQL instances use:
- **Username**: `postgres`
- **Password**: `postgres`
- **SSL**: Disabled (development only)

## Troubleshooting

### Common Issues

#### 1. Port Conflicts
If ports are already in use:
```powershell
# Check what's using the port
netstat -ano | findstr :9092

# Stop conflicting services or change ports in docker-compose files
```

#### 2. Docker Network Issues
```powershell
# Recreate the network
docker network rm cdh-network
docker network create cdh-network
```

#### 3. Service Health Checks Failing
```powershell
# Check service logs
docker-compose -f docker-compose.services.yml logs kafka

# Restart specific service
docker-compose -f docker-compose.services.yml restart kafka
```

#### 4. Data Persistence Issues
```powershell
# Clean all data and restart
.\scripts\dev\start-services.ps1 -Clean
.\scripts\dev\start-services.ps1
```

### Health Check Commands

#### Kafka
```bash
# Test Kafka connectivity
docker exec cdh-kafka kafka-topics --bootstrap-server localhost:9092 --list

# Create test topic
docker exec cdh-kafka kafka-topics --bootstrap-server localhost:9092 --create --topic test-topic
```

#### Redis
```bash
# Test Redis connectivity
docker exec cdh-redis redis-cli ping

# Check Redis info
docker exec cdh-redis redis-cli info
```

#### PostgreSQL
```bash
# Test database connectivity
docker exec cdh-inventory-db psql -U postgres -d inventory_db -c "SELECT version();"
```

## Development Workflow

### Recommended Startup Order
1. Start databases: `.\scripts\dev\start-services.ps1 -Services databases`
2. Wait for databases to be ready (health checks pass)
3. Start messaging and cache: `.\scripts\dev\start-services.ps1 -Services messaging,cache`
4. Optionally start tools: `.\scripts\dev\start-services.ps1 -Services tools`

### Testing Workflow
1. Start all services
2. Run application tests
3. Use management UIs to inspect data:
   - Kafka UI: Monitor topics and messages
   - Redis Commander: Inspect cache contents
   - Database clients: Query data directly

## Performance Considerations

### Resource Usage
- **Kafka + Zookeeper**: ~1GB RAM
- **Redis**: ~256MB RAM (configured limit)
- **PostgreSQL (4 instances)**: ~200MB RAM each
- **Total**: ~2.5GB RAM for all services

### Optimization Tips
- Use `docker-compose down` instead of `stop` to free resources
- Start only needed services during development
- Use the `tools` profile only when debugging
- Clean up regularly with the `-Clean` option
