package database

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/company/cdh/apps/order-service/models"
	"github.com/company/cdh/apps/order-service/internal/errors"
	"github.com/company/cdh/apps/order-service/internal/repository"
)

// orderRepository handles database operations for orders
type orderRepository struct {
	db *sql.DB
}

// NewOrderRepository creates a new order repository
func NewOrderRepository(db *sql.DB) repository.OrderRepository {
	return &orderRepository{db: db}
}

// CreateOrder creates a new order in the database
func (r *orderRepository) CreateOrder(req *models.OrderRequest) (*models.Order, error) {
	// Generate order number with better uniqueness
	orderNumber := fmt.Sprintf("ORD-%d", time.Now().UnixNano())
	
	query := `
		INSERT INTO orders (order_number, product_info, quantity, price, status, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id, created_at, updated_at
	`
	
	order := req.ToOrder()
	order.OrderNumber = orderNumber

	err := r.db.QueryRow(query, order.OrderNumber, order.ProductInfo, order.Quantity, 
		order.Price, order.Status, order.CreatedAt, order.UpdatedAt).Scan(
		&order.ID, &order.CreatedAt, &order.UpdatedAt)
	
	if err != nil {
		return nil, errors.NewDatabaseError("create order", err)
	}

	return order, nil
}

// GetOrderByID retrieves an order by its ID
func (r *orderRepository) GetOrderByID(id int) (*models.Order, error) {
	query := `
		SELECT id, order_number, product_info, quantity, price, status, created_at, updated_at
		FROM orders
		WHERE id = $1
	`
	
	order := &models.Order{}
	err := r.db.QueryRow(query, id).Scan(
		&order.ID, &order.OrderNumber, &order.ProductInfo, &order.Quantity,
		&order.Price, &order.Status, &order.CreatedAt, &order.UpdatedAt)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.ErrOrderNotFound
		}
		return nil, errors.NewDatabaseError("get order by ID", err)
	}

	return order, nil
}

// GetOrderByNumber retrieves an order by its order number
func (r *orderRepository) GetOrderByNumber(orderNumber string) (*models.Order, error) {
	query := `
		SELECT id, order_number, product_info, quantity, price, status, created_at, updated_at
		FROM orders
		WHERE order_number = $1
	`
	
	order := &models.Order{}
	err := r.db.QueryRow(query, orderNumber).Scan(
		&order.ID, &order.OrderNumber, &order.ProductInfo, &order.Quantity,
		&order.Price, &order.Status, &order.CreatedAt, &order.UpdatedAt)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.ErrOrderNotFound
		}
		return nil, errors.NewDatabaseError("get order by number", err)
	}

	return order, nil
}

// UpdateOrderStatus updates the status of an order
func (r *orderRepository) UpdateOrderStatus(id int, status models.OrderStatus) error {
	// Validate status first
	if !models.IsValidStatus(status) {
		return errors.NewValidationError("status", "invalid order status")
	}

	query := `
		UPDATE orders 
		SET status = $1, updated_at = $2
		WHERE id = $3
	`
	
	result, err := r.db.Exec(query, status, time.Now(), id)
	if err != nil {
		return errors.NewDatabaseError("update order status", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.NewDatabaseError("get rows affected", err)
	}

	if rowsAffected == 0 {
		return errors.ErrOrderNotFound
	}

	return nil
}