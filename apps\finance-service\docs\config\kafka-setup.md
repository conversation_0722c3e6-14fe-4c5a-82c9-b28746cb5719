# Kafka Configuration Guide

## Overview

This guide provides configuration examples and setup instructions for the Finance Service Kafka integration.

## Environment Configuration

### Development Environment

#### Docker Compose Setup

```yaml
# docker-compose.kafka.yml
version: '3.8'
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9997:9997"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9997
      KAFKA_JMX_HOSTNAME: localhost

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    depends_on:
      - kafka
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
```

#### Starting Kafka

```bash
# Start Kafka cluster
docker-compose -f docker-compose.kafka.yml up -d

# Create financial records topic
docker exec kafka kafka-topics --create \
  --topic financial.records.created \
  --bootstrap-server localhost:9092 \
  --partitions 3 \
  --replication-factor 1

# Verify topic creation
docker exec kafka kafka-topics --list --bootstrap-server localhost:9092
```

### Production Environment

#### Kafka Cluster Configuration

```properties
# server.properties (Kafka Broker Configuration)

# Broker ID (unique for each broker)
broker.id=1

# Network and Security
listeners=PLAINTEXT://0.0.0.0:9092,SSL://0.0.0.0:9093
advertised.listeners=PLAINTEXT://kafka-broker-1:9092,SSL://kafka-broker-1:9093
security.inter.broker.protocol=SSL

# Zookeeper
zookeeper.connect=zk1:2181,zk2:2181,zk3:2181

# Log Configuration
log.dirs=/var/kafka-logs
num.network.threads=8
num.io.threads=16
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600

# Topic Configuration
num.partitions=3
default.replication.factor=3
min.insync.replicas=2

# Log Retention
log.retention.hours=168
log.retention.bytes=1073741824
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000

# Performance Tuning
replica.fetch.max.bytes=1048576
message.max.bytes=1000000
```

#### Topic Configuration

```bash
# Create production topic with proper configuration
kafka-topics --create \
  --topic financial.records.created \
  --bootstrap-server kafka-cluster:9092 \
  --partitions 6 \
  --replication-factor 3 \
  --config min.insync.replicas=2 \
  --config retention.ms=604800000 \
  --config compression.type=lz4 \
  --config cleanup.policy=delete
```

## Finance Service Configuration

### Application Configuration

#### Development (config/development.yaml)

```yaml
# Finance Service Configuration - Development
database:
  host: localhost
  port: 5435
  name: finance_db
  user: finance_user
  password: finance_password
  ssl_mode: disable
  max_connections: 10
  connection_timeout: 30s

kafka:
  producer:
    brokers:
      - localhost:9092
    topic: financial.records.created
    client_id: finance-service-dev
    acks: 1
    retries: 3
    batch_size: 16384
    linger_ms: 5
    buffer_memory: 33554432
    compression_type: lz4
    max_request_size: 1048576
    request_timeout_ms: 30000
    retry_backoff_ms: 100
    enable_idempotence: true

logging:
  level: debug
  format: json
  output: stdout

metrics:
  enabled: true
  port: 8081
  path: /metrics
```

#### Production (config/production.yaml)

```yaml
# Finance Service Configuration - Production
database:
  host: ${DB_HOST}
  port: ${DB_PORT}
  name: ${DB_NAME}
  user: ${DB_USER}
  password: ${DB_PASSWORD}
  ssl_mode: require
  max_connections: 50
  connection_timeout: 30s
  max_idle_connections: 10
  max_lifetime: 1h

kafka:
  producer:
    brokers:
      - ${KAFKA_BROKER_1}
      - ${KAFKA_BROKER_2}
      - ${KAFKA_BROKER_3}
    topic: financial.records.created
    client_id: finance-service-${INSTANCE_ID}
    acks: all
    retries: 10
    batch_size: 65536
    linger_ms: 10
    buffer_memory: 134217728
    compression_type: lz4
    max_request_size: 1048576
    request_timeout_ms: 30000
    retry_backoff_ms: 1000
    enable_idempotence: true
    security_protocol: SSL
    ssl_truststore_location: /etc/kafka/ssl/kafka.client.truststore.jks
    ssl_truststore_password: ${SSL_TRUSTSTORE_PASSWORD}
    ssl_keystore_location: /etc/kafka/ssl/kafka.client.keystore.jks
    ssl_keystore_password: ${SSL_KEYSTORE_PASSWORD}

logging:
  level: info
  format: json
  output: /var/log/finance-service/app.log

metrics:
  enabled: true
  port: 8081
  path: /metrics

health_check:
  port: 8082
  path: /health
```

### Environment Variables

#### Development

```bash
# .env.development
DB_HOST=localhost
DB_PORT=5435
DB_NAME=finance_db
DB_USER=finance_user
DB_PASSWORD=finance_password

KAFKA_BROKERS=localhost:9092
KAFKA_TOPIC=financial.records.created

LOG_LEVEL=debug
METRICS_ENABLED=true
```

#### Production

```bash
# .env.production
DB_HOST=finance-db-cluster.internal
DB_PORT=5432
DB_NAME=finance_production
DB_USER=finance_service
DB_PASSWORD=${VAULT_DB_PASSWORD}

KAFKA_BROKER_1=kafka-1.internal:9092
KAFKA_BROKER_2=kafka-2.internal:9092
KAFKA_BROKER_3=kafka-3.internal:9092
KAFKA_TOPIC=financial.records.created

SSL_TRUSTSTORE_PASSWORD=${VAULT_SSL_TRUSTSTORE_PASSWORD}
SSL_KEYSTORE_PASSWORD=${VAULT_SSL_KEYSTORE_PASSWORD}

INSTANCE_ID=${HOSTNAME}
LOG_LEVEL=info
METRICS_ENABLED=true
```

## Monitoring Configuration

### Prometheus Metrics

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'finance-service'
    static_configs:
      - targets: ['finance-service:8081']
    metrics_path: /metrics
    scrape_interval: 10s

  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka-1:9997', 'kafka-2:9997', 'kafka-3:9997']
    metrics_path: /metrics
    scrape_interval: 30s
```

### Grafana Dashboard

```json
{
  "dashboard": {
    "title": "Finance Service - Kafka Events",
    "panels": [
      {
        "title": "Event Publishing Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(finance_service_events_published_total[5m])",
            "legendFormat": "Events/sec"
          }
        ]
      },
      {
        "title": "Publishing Errors",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(finance_service_events_failed_total[5m])",
            "legendFormat": "Errors/sec"
          }
        ]
      },
      {
        "title": "Kafka Producer Latency",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(finance_service_kafka_publish_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

## Security Configuration

### SSL/TLS Setup

#### Certificate Generation

```bash
# Generate CA certificate
openssl req -new -x509 -keyout ca-key -out ca-cert -days 365 -subj "/CN=kafka-ca"

# Generate server certificate
openssl req -new -keyout kafka-server-key -out kafka-server-req -subj "/CN=kafka-server"
openssl x509 -req -CA ca-cert -CAkey ca-key -in kafka-server-req -out kafka-server-cert -days 365 -CAcreateserial

# Create truststore
keytool -keystore kafka.client.truststore.jks -alias CARoot -import -file ca-cert -storepass truststore-password -noprompt

# Create keystore
keytool -keystore kafka.client.keystore.jks -alias kafka-client -validity 365 -genkey -keyalg RSA -storepass keystore-password -keypass keystore-password -dname "CN=kafka-client"
```

### SASL Authentication

```properties
# SASL SCRAM configuration
security.protocol=SASL_SSL
sasl.mechanism=SCRAM-SHA-256
sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required \
  username="finance-service" \
  password="secure-password";
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   ```bash
   # Check Kafka status
   docker exec kafka kafka-broker-api-versions --bootstrap-server localhost:9092
   
   # Check network connectivity
   telnet kafka-host 9092
   ```

2. **Topic Not Found**
   ```bash
   # List all topics
   kafka-topics --list --bootstrap-server localhost:9092
   
   # Describe specific topic
   kafka-topics --describe --topic financial.records.created --bootstrap-server localhost:9092
   ```

3. **Producer Timeout**
   ```bash
   # Check producer configuration
   # Increase request.timeout.ms and retry.backoff.ms
   
   # Monitor broker logs
   docker logs kafka
   ```

### Performance Tuning

#### Producer Optimization

```yaml
kafka:
  producer:
    # Increase batch size for higher throughput
    batch_size: 65536
    linger_ms: 10
    
    # Increase buffer memory for high-volume scenarios
    buffer_memory: 134217728
    
    # Use compression to reduce network usage
    compression_type: lz4
    
    # Enable idempotence for exactly-once semantics
    enable_idempotence: true
    acks: all
```

#### Topic Optimization

```bash
# Increase partitions for parallel processing
kafka-topics --alter --topic financial.records.created --partitions 12 --bootstrap-server localhost:9092

# Configure retention based on requirements
kafka-configs --alter --entity-type topics --entity-name financial.records.created \
  --add-config retention.ms=604800000 --bootstrap-server localhost:9092
```

---

*This configuration guide is maintained by the Finance Service team. Last updated: 2025-08-02*
