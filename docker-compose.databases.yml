version: '3.8'

services:
  # User Service Database
  user-db:
    image: postgres:17-alpine
    container_name: cdh-user-db
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: user_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - user_db_data:/var/lib/postgresql/data
      - ./scripts/init-user-db.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d user_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Order Service Database  
  order-db:
    image: postgres:17-alpine
    container_name: cdh-order-db
    ports:
      - "5433:5432"
    environment:
      POSTGRES_DB: order_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - order_db_data:/var/lib/postgresql/data
      - ./scripts/init-order-db.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d order_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Inventory Service Database
  inventory-db:
    image: postgres:17-alpine
    container_name: cdh-inventory-db
    ports:
      - "5434:5432"
    environment:
      POSTGRES_DB: inventory_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - inventory_db_data:/var/lib/postgresql/data
      - ./scripts/init-inventory-db.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d inventory_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Finance Service Database
  finance-db:
    image: postgres:17-alpine
    container_name: cdh-finance-db
    ports:
      - "5435:5432"
    environment:
      POSTGRES_DB: finance_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - finance_db_data:/var/lib/postgresql/data
      - ./scripts/init-finance-db.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d finance_db"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  user_db_data:
  order_db_data:
  inventory_db_data:
  finance_db_data:

networks:
  default:
    name: cdh-network