package handlers

import (
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/company/cdh/apps/finance-service/models"
)

// GetFinancialRecords handles GET /api/v1/financial-records
func (s *Server) GetFinancialRecords(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		s.writeErrorResponse(w, "Method not allowed", http.StatusMethodNotAllowed, nil)
		return
	}

	// Parse query parameters
	filters, err := s.parseQueryFilters(r)
	if err != nil {
		s.writeErrorResponse(w, "Invalid query parameters", http.StatusBadRequest, map[string]string{
			"validation": err.Error(),
		})
		return
	}

	// Validate filters
	if err := filters.Validate(); err != nil {
		s.writeErrorResponse(w, "Invalid filter parameters", http.StatusBadRequest, map[string]string{
			"validation": err.<PERSON>rror(),
		})
		return
	}

	// Get financial records from repository
	entries, total, err := s.financeService.GetFinancialRecords(filters)
	if err != nil {
		s.writeErrorResponse(w, "Failed to retrieve financial records", http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
		return
	}

	// Convert to API response models
	var responseData []models.FinancialRecordResponse
	for _, entry := range entries {
		responseData = append(responseData, entry.ToFinancialRecordResponse())
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(total) / float64(filters.Limit)))
	pagination := models.PaginationMetadata{
		Page:        filters.Page,
		Limit:       filters.Limit,
		Total:       total,
		TotalPages:  totalPages,
		HasNext:     filters.Page < totalPages,
		HasPrevious: filters.Page > 1,
	}

	// Create response
	response := models.FinancialRecordsListResponse{
		Data:       responseData,
		Pagination: pagination,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// UpdateFinancialRecordStatus handles PATCH /api/v1/financial-records/{id}/status
func (s *Server) UpdateFinancialRecordStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPatch {
		s.writeErrorResponse(w, "Method not allowed", http.StatusMethodNotAllowed, nil)
		return
	}

	// Extract ID from URL path
	// For now, we'll expect the ID to be passed as a query parameter
	// In a full implementation, we'd use a proper router like gorilla/mux
	idStr := r.URL.Query().Get("id")
	if idStr == "" {
		s.writeErrorResponse(w, "Missing record ID", http.StatusBadRequest, map[string]string{
			"parameter": "id is required in query parameters",
		})
		return
	}

	// Validate ID format and range
	if len(idStr) > 10 { // Reasonable limit for ID string length
		s.writeErrorResponse(w, "Invalid record ID", http.StatusBadRequest, map[string]string{
			"parameter": "id is too long",
		})
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		s.writeErrorResponse(w, "Invalid record ID", http.StatusBadRequest, map[string]string{
			"parameter": "id must be a valid positive integer",
		})
		return
	}

	if id <= 0 {
		s.writeErrorResponse(w, "Invalid record ID", http.StatusBadRequest, map[string]string{
			"parameter": "id must be greater than 0",
		})
		return
	}

	if id > 2147483647 { // Max int32 value for database compatibility
		s.writeErrorResponse(w, "Invalid record ID", http.StatusBadRequest, map[string]string{
			"parameter": "id value is too large",
		})
		return
	}

	// Validate Content-Type header
	contentType := r.Header.Get("Content-Type")
	if contentType != "application/json" && contentType != "" {
		s.writeErrorResponse(w, "Invalid Content-Type", http.StatusBadRequest, map[string]string{
			"header": "Content-Type must be application/json",
		})
		return
	}

	// Validate request body size (prevent large payloads)
	if r.ContentLength > 1024 { // 1KB limit for status update requests
		s.writeErrorResponse(w, "Request body too large", http.StatusBadRequest, map[string]string{
			"size": "request body must be less than 1KB",
		})
		return
	}

	// Parse request body
	var updateRequest models.StatusUpdateRequest
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Reject unknown fields
	if err := decoder.Decode(&updateRequest); err != nil {
		var details map[string]string
		if err.Error() == "json: unknown field" {
			details = map[string]string{
				"json": "request contains unknown fields",
			}
		} else {
			details = map[string]string{
				"json": "invalid JSON format: " + err.Error(),
			}
		}
		s.writeErrorResponse(w, "Invalid request body", http.StatusBadRequest, details)
		return
	}

	// Validate required fields
	if updateRequest.Status == "" {
		s.writeErrorResponse(w, "Missing required field", http.StatusBadRequest, map[string]string{
			"status": "status field is required",
		})
		return
	}

	// Validate status value
	validStatuses := map[string]bool{
		"processed":          true,
		"requires_attention": true,
		"completed":          true,
	}
	if !validStatuses[updateRequest.Status] {
		s.writeErrorResponse(w, "Invalid status value", http.StatusBadRequest, map[string]string{
			"status": "must be one of: processed, requires_attention, completed",
		})
		return
	}

	// Validate notes field length
	if len(updateRequest.Notes) > 1000 {
		s.writeErrorResponse(w, "Invalid notes field", http.StatusBadRequest, map[string]string{
			"notes": "notes field must be less than 1000 characters",
		})
		return
	}

	// Update the record status
	err = s.financeService.UpdateFinancialRecordStatus(id, updateRequest.Status, updateRequest.Notes)
	if err != nil {
		if err.Error() == fmt.Sprintf("financial record not found for ID: %d", id) {
			s.writeErrorResponse(w, "Financial record not found", http.StatusNotFound, nil)
			return
		}
		s.writeErrorResponse(w, "Failed to update financial record status", http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
		return
	}

	// Create response
	response := models.StatusUpdateResponse{
		ID:              id,
		Status:          updateRequest.Status,
		ProcessedAt:     time.Now(),
		ProcessingNotes: updateRequest.Notes,
		UpdatedAt:       time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// parseQueryFilters parses query parameters into QueryFilters struct
func (s *Server) parseQueryFilters(r *http.Request) (*models.QueryFilters, error) {
	filters := &models.QueryFilters{
		Page:  1,
		Limit: 20,
	}

	// Parse page
	if pageStr := r.URL.Query().Get("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			return nil, fmt.Errorf("invalid page parameter: must be a valid integer")
		}
		if page < 1 {
			return nil, fmt.Errorf("invalid page parameter: must be greater than 0")
		}
		if page > 10000 { // Reasonable upper limit
			return nil, fmt.Errorf("invalid page parameter: must be less than or equal to 10000")
		}
		filters.Page = page
	}

	// Parse limit
	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		limit, err := strconv.Atoi(limitStr)
		if err != nil {
			return nil, fmt.Errorf("invalid limit parameter: must be a valid integer")
		}
		if limit < 1 {
			return nil, fmt.Errorf("invalid limit parameter: must be greater than 0")
		}
		if limit > 100 {
			return nil, fmt.Errorf("invalid limit parameter: must be less than or equal to 100")
		}
		filters.Limit = limit
	}

	// Parse date_from
	if dateFromStr := r.URL.Query().Get("date_from"); dateFromStr != "" {
		// Validate date format
		if len(dateFromStr) != 10 {
			return nil, fmt.Errorf("invalid date_from format: must be YYYY-MM-DD")
		}
		dateFrom, err := time.Parse("2006-01-02", dateFromStr)
		if err != nil {
			return nil, fmt.Errorf("invalid date_from format: must be a valid date in YYYY-MM-DD format")
		}
		// Validate date range (not too far in the past or future)
		now := time.Now()
		if dateFrom.Before(now.AddDate(-10, 0, 0)) {
			return nil, fmt.Errorf("invalid date_from: date cannot be more than 10 years in the past")
		}
		if dateFrom.After(now.AddDate(1, 0, 0)) {
			return nil, fmt.Errorf("invalid date_from: date cannot be more than 1 year in the future")
		}
		filters.DateFrom = &dateFrom
	}

	// Parse date_to
	if dateToStr := r.URL.Query().Get("date_to"); dateToStr != "" {
		// Validate date format
		if len(dateToStr) != 10 {
			return nil, fmt.Errorf("invalid date_to format: must be YYYY-MM-DD")
		}
		dateTo, err := time.Parse("2006-01-02", dateToStr)
		if err != nil {
			return nil, fmt.Errorf("invalid date_to format: must be a valid date in YYYY-MM-DD format")
		}
		// Validate date range
		now := time.Now()
		if dateTo.Before(now.AddDate(-10, 0, 0)) {
			return nil, fmt.Errorf("invalid date_to: date cannot be more than 10 years in the past")
		}
		if dateTo.After(now.AddDate(1, 0, 0)) {
			return nil, fmt.Errorf("invalid date_to: date cannot be more than 1 year in the future")
		}
		// Set to end of day
		dateTo = dateTo.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
		filters.DateTo = &dateTo
	}

	// Parse order_reference
	if orderRef := r.URL.Query().Get("order_reference"); orderRef != "" {
		// Validate order reference format and length
		if len(orderRef) < 3 {
			return nil, fmt.Errorf("invalid order_reference: must be at least 3 characters long")
		}
		if len(orderRef) > 100 {
			return nil, fmt.Errorf("invalid order_reference: must be less than 100 characters")
		}
		// Basic alphanumeric validation
		for _, char := range orderRef {
			if !((char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') ||
				(char >= '0' && char <= '9') || char == '-' || char == '_') {
				return nil, fmt.Errorf("invalid order_reference: can only contain letters, numbers, hyphens, and underscores")
			}
		}
		filters.OrderReference = orderRef
	}

	// Parse status
	if status := r.URL.Query().Get("status"); status != "" {
		// Validate status value
		validStatuses := map[string]bool{
			"pending":            true,
			"processed":          true,
			"requires_attention": true,
			"completed":          true,
		}
		if !validStatuses[status] {
			return nil, fmt.Errorf("invalid status: must be one of pending, processed, requires_attention, completed")
		}
		filters.Status = status
	}

	// Cross-field validation: ensure date_from is not after date_to
	if filters.DateFrom != nil && filters.DateTo != nil {
		if filters.DateFrom.After(*filters.DateTo) {
			return nil, fmt.Errorf("invalid date range: date_from cannot be after date_to")
		}
		// Check for reasonable date range (not more than 2 years)
		if filters.DateTo.Sub(*filters.DateFrom) > 2*365*24*time.Hour {
			return nil, fmt.Errorf("invalid date range: date range cannot exceed 2 years")
		}
	}

	return filters, nil
}

// writeErrorResponse writes a standardized error response
func (s *Server) writeErrorResponse(w http.ResponseWriter, message string, code int, details map[string]string) {
	errorResponse := models.ErrorResponse{
		Error:   http.StatusText(code),
		Message: message,
		Code:    code,
		Details: details,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	json.NewEncoder(w).Encode(errorResponse)
}

// UpdateFinancialRecordStatusByID handles PATCH /api/v1/financial-records/{id}/status
// This is the RESTful version that extracts ID from URL path
func (s *Server) UpdateFinancialRecordStatusByID(w http.ResponseWriter, r *http.Request) {
	// Set response headers
	w.Header().Set("Content-Type", "application/json")

	// Only allow PATCH method
	if r.Method != http.MethodPatch {
		w.Header().Set("Allow", "PATCH")
		s.writeErrorResponse(w, "Method not allowed", http.StatusMethodNotAllowed, map[string]string{
			"allowed_methods": "PATCH",
		})
		return
	}

	// Extract ID from URL path
	// Expected path: /api/v1/financial-records/{id}/status
	pathParts := strings.Split(strings.Trim(r.URL.Path, "/"), "/")
	if len(pathParts) != 5 || pathParts[0] != "api" || pathParts[1] != "v1" ||
		pathParts[2] != "financial-records" || pathParts[4] != "status" {
		s.writeErrorResponse(w, "Invalid URL path", http.StatusBadRequest, map[string]string{
			"expected_format": "/api/v1/financial-records/{id}/status",
		})
		return
	}

	idStr := pathParts[3]
	if idStr == "" {
		s.writeErrorResponse(w, "Missing record ID", http.StatusBadRequest, map[string]string{
			"parameter": "id is required in URL path",
		})
		return
	}

	// Validate ID format and range
	if len(idStr) > 10 { // Reasonable limit for ID string length
		s.writeErrorResponse(w, "Invalid record ID", http.StatusBadRequest, map[string]string{
			"parameter": "id is too long",
		})
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		s.writeErrorResponse(w, "Invalid record ID", http.StatusBadRequest, map[string]string{
			"parameter": "id must be a valid positive integer",
		})
		return
	}

	if id <= 0 {
		s.writeErrorResponse(w, "Invalid record ID", http.StatusBadRequest, map[string]string{
			"parameter": "id must be greater than 0",
		})
		return
	}

	if id > 2147483647 { // Max int32 value for database compatibility
		s.writeErrorResponse(w, "Invalid record ID", http.StatusBadRequest, map[string]string{
			"parameter": "id value is too large",
		})
		return
	}

	// Validate Content-Type header
	contentType := r.Header.Get("Content-Type")
	if contentType != "application/json" && contentType != "" {
		s.writeErrorResponse(w, "Invalid Content-Type", http.StatusBadRequest, map[string]string{
			"header": "Content-Type must be application/json",
		})
		return
	}

	// Validate request body size (prevent large payloads)
	if r.ContentLength > 1024 { // 1KB limit for status update requests
		s.writeErrorResponse(w, "Request body too large", http.StatusBadRequest, map[string]string{
			"size": "request body must be less than 1KB",
		})
		return
	}

	// Parse request body
	var updateRequest models.StatusUpdateRequest
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Reject unknown fields
	if err := decoder.Decode(&updateRequest); err != nil {
		var details map[string]string
		if strings.Contains(err.Error(), "unknown field") {
			details = map[string]string{
				"json": "request contains unknown fields",
			}
		} else {
			details = map[string]string{
				"json": "invalid JSON format: " + err.Error(),
			}
		}
		s.writeErrorResponse(w, "Invalid request body", http.StatusBadRequest, details)
		return
	}

	// Validate required fields
	if updateRequest.Status == "" {
		s.writeErrorResponse(w, "Missing required field", http.StatusBadRequest, map[string]string{
			"status": "status field is required",
		})
		return
	}

	// Validate status value
	validStatuses := map[string]bool{
		"processed":          true,
		"requires_attention": true,
		"completed":          true,
	}
	if !validStatuses[updateRequest.Status] {
		s.writeErrorResponse(w, "Invalid status value", http.StatusBadRequest, map[string]string{
			"status": "must be one of: processed, requires_attention, completed",
		})
		return
	}

	// Validate notes field length
	if len(updateRequest.Notes) > 1000 {
		s.writeErrorResponse(w, "Invalid notes field", http.StatusBadRequest, map[string]string{
			"notes": "notes field must be less than 1000 characters",
		})
		return
	}

	// For idempotency: check if the status is already the requested status
	// Note: Since we don't have status column yet, we'll skip this check for now
	// In the future, this would:
	// 1. Get the current record by ID
	// 2. Check if currentRecord.Status == updateRequest.Status
	// 3. If yes, return success response without making changes
	// This ensures that repeated identical requests are safe and don't cause side effects

	// Update the record status
	err = s.financeService.UpdateFinancialRecordStatus(id, updateRequest.Status, updateRequest.Notes)
	if err != nil {
		if err.Error() == fmt.Sprintf("financial record not found for ID: %d", id) {
			s.writeErrorResponse(w, "Financial record not found", http.StatusNotFound, nil)
			return
		}
		s.writeErrorResponse(w, "Failed to update financial record status", http.StatusInternalServerError, nil)
		return
	}

	// Create success response
	response := models.StatusUpdateResponse{
		ID:              id,
		Status:          updateRequest.Status,
		ProcessedAt:     time.Now(),
		ProcessingNotes: updateRequest.Notes,
		UpdatedAt:       time.Now(),
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}
