package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/company/cdh/apps/finance-service/handlers"
	"github.com/company/cdh/apps/finance-service/internal/config"
	"github.com/company/cdh/apps/finance-service/internal/consumer"
	"github.com/company/cdh/apps/finance-service/internal/database"
	"github.com/company/cdh/apps/finance-service/internal/producer"
	"github.com/company/cdh/apps/finance-service/internal/repository"
	"github.com/company/cdh/apps/finance-service/internal/services"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Connect to database
	db, err := database.Connect(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Run database migrations
	if err := database.RunMigrations(db); err != nil {
		log.Fatalf("Failed to run migrations: %v", err)
	}

	// Initialize repositories
	financeRepo := repository.NewFinanceRepository(db)

	// Initialize Kafka producer
	producerConfig := producer.NewProducerConfigFromConfig(cfg)
	eventProducer, err := producer.NewKafkaProducer(producerConfig)
	if err != nil {
		log.Fatalf("Failed to create Kafka producer: %v", err)
	}
	defer eventProducer.Close()

	// Initialize services
	financeService := services.NewFinanceService(financeRepo, eventProducer)

	// Initialize Kafka consumer
	kafkaConsumer, err := consumer.NewKafkaConsumer(cfg.Kafka, financeService)
	if err != nil {
		log.Fatalf("Failed to create Kafka consumer: %v", err)
	}

	// Start Kafka consumer in background
	go func() {
		if err := kafkaConsumer.Start(); err != nil {
			log.Printf("Kafka consumer error: %v", err)
			// Consider implementing retry logic or graceful degradation
		}
	}()

	// Initialize HTTP server
	server := handlers.NewServer(cfg.Server, db, financeService)

	// Start HTTP server in background
	go func() {
		log.Printf("Starting Finance Service on port %s", cfg.Server.Port)
		if err := server.Start(); err != nil && err != http.ErrServerClosed {
			log.Printf("Server error: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down Finance Service...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown HTTP server
	if err := server.Shutdown(ctx); err != nil {
		log.Printf("Server shutdown error: %v", err)
	}

	// Close Kafka consumer
	if err := kafkaConsumer.Close(); err != nil {
		log.Printf("Kafka consumer close error: %v", err)
	}

	log.Println("Finance Service stopped")
}
