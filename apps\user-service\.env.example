# User Service Environment Configuration

# Database Configuration (Independent PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=user_db
DB_SSLMODE=disable

# Legacy Supabase Configuration (for migration reference)
# SUPABASE_DB_URL=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require

# JWT Configuration
JWT_SECRET=your-jwt-secret-key-here

# Server Configuration
PORT=8080
HOST=0.0.0.0

# Environment
ENV=development