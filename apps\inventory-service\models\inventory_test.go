package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestInventory_CalculateAvailableQuantity(t *testing.T) {
	tests := []struct {
		name             string
		stockQuantity    int
		reservedQuantity int
		expected         int
	}{
		{
			name:             "Normal case",
			stockQuantity:    100,
			reservedQuantity: 20,
			expected:         80,
		},
		{
			name:             "Zero reserved",
			stockQuantity:    50,
			reservedQuantity: 0,
			expected:         50,
		},
		{
			name:             "Fully reserved",
			stockQuantity:    30,
			reservedQuantity: 30,
			expected:         0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			inventory := &Inventory{
				StockQuantity:    tt.stockQuantity,
				ReservedQuantity: tt.reservedQuantity,
			}

			inventory.CalculateAvailableQuantity()

			assert.Equal(t, tt.expected, inventory.AvailableQuantity,
				"CalculateAvailableQuantity() should return correct value")
		})
	}
}

func TestOrderCreatedEvent_Structure(t *testing.T) {
	event := OrderCreatedEvent{
		OrderID:    "order-123",
		CustomerID: "customer-456",
		Items: []OrderItem{
			{
				SKU:      "LAPTOP-001",
				Quantity: 2,
				Price:    999.99,
			},
		},
		Status:    "pending",
		CreatedAt: time.Now(),
	}

	assert.Equal(t, "order-123", event.OrderID, "OrderID should match")
	assert.Len(t, event.Items, 1, "Should have exactly 1 item")
	assert.Equal(t, "LAPTOP-001", event.Items[0].SKU, "SKU should match")
}

func TestInventory_Validate(t *testing.T) {
	tests := []struct {
		name      string
		inventory Inventory
		wantError bool
		errorMsg  string
	}{
		{
			name: "Valid inventory",
			inventory: Inventory{
				SKU:              "LAPTOP-001",
				ProductName:      "Gaming Laptop",
				StockQuantity:    100,
				ReservedQuantity: 20,
				UnitPrice:        999.99,
			},
			wantError: false,
		},
		{
			name: "Empty SKU",
			inventory: Inventory{
				SKU:         "",
				ProductName: "Gaming Laptop",
			},
			wantError: true,
			errorMsg:  "SKU cannot be empty",
		},
		{
			name: "Empty product name",
			inventory: Inventory{
				SKU:         "LAPTOP-001",
				ProductName: "",
			},
			wantError: true,
			errorMsg:  "Product name cannot be empty",
		},
		{
			name: "Negative stock quantity",
			inventory: Inventory{
				SKU:           "LAPTOP-001",
				ProductName:   "Gaming Laptop",
				StockQuantity: -10,
			},
			wantError: true,
			errorMsg:  "Stock quantity cannot be negative",
		},
		{
			name: "Reserved exceeds stock",
			inventory: Inventory{
				SKU:              "LAPTOP-001",
				ProductName:      "Gaming Laptop",
				StockQuantity:    50,
				ReservedQuantity: 60,
			},
			wantError: true,
			errorMsg:  "Reserved quantity cannot exceed stock quantity",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.inventory.Validate()
			if tt.wantError {
				require.Error(t, err, "Validation should fail")
				assert.Contains(t, err.Error(), tt.errorMsg, "Error message should contain expected text")
			} else {
				assert.NoError(t, err, "Validation should pass")
			}
		})
	}
}

func TestOrderCreatedEvent_Validate(t *testing.T) {
	validEvent := OrderCreatedEvent{
		OrderID:    "order-123",
		CustomerID: "customer-456",
		Items: []OrderItem{
			{
				SKU:      "LAPTOP-001",
				Quantity: 2,
				Price:    999.99,
			},
		},
		Status:    "pending",
		CreatedAt: time.Now(),
	}

	t.Run("Valid event", func(t *testing.T) {
		err := validEvent.Validate()
		assert.NoError(t, err, "Valid event should pass validation")
	})

	t.Run("Empty order ID", func(t *testing.T) {
		event := validEvent
		event.OrderID = ""
		err := event.Validate()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "Order ID cannot be empty")
	})

	t.Run("Empty items", func(t *testing.T) {
		event := validEvent
		event.Items = []OrderItem{}
		err := event.Validate()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "Order must have at least one item")
	})

	t.Run("Invalid item", func(t *testing.T) {
		event := validEvent
		event.Items[0].SKU = ""
		err := event.Validate()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "Invalid item")
	})
}
