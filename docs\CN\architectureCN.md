# 中央数据中心 (CDH) 系统架构文档

## 1. 简介
本文件概述了中央数据中心 (CDH) 的整体项目架构。它是一个以 Go 语言为核心、遵循云原生理念的微服务系统。其主要目标是作为未来 POS、WMS 和电商等系统的核心后端，提供统一的数据管理和业务处理能力，并确保对 LHDN MyInvois 系统的合规性。

### 变更日志
| 日期 | 版本 | 描述 | 作者 |
| :--- | :--- | :--- | :--- |
| 2025-08-01 | 1.0 | 初始架构设计 | <PERSON>, Architect |

## 2. 高层架构

### 技术概要
本系统采用基于 Kubernetes (K8s) 的事件驱动微服务架构。所有服务使用 Go 语言开发，通过 Docker 容器化部署。服务间通过 Kafka 进行异步通信以实现解耦和高可靠性。Traefik 作为 API 网关，是所有外部请求的统一入口，负责路由、安全和认证。每个服务拥有独立的 PostgreSQL 数据库，确保数据隔离。

### 架构图
```mermaid
graph TD
    subgraph "外部系统 (未来开发)"
        POS[POS 系统]
        WMS[WMS 系统]
        ECOM[电商平台]
    end

    subgraph "CDH 平台"
        subgraph "基础设施"
            K8S[Kubernetes Cluster]
            KAFKA[Kafka]
            MONITORING["监控 & 日志<br/>(Prometheus, Grafana, EFK)"]
        end

        subgraph "微服务"
            GW["API 网关-Traefik"]
            USER["用户与权限服务<br/>(Go)"]
            ORDER["订单服务<br/>(Go)"]
            INV["库存服务<br/>(Go)"]
            FIN["财务与税务服务<br/>(Go)"]
        end
        
        subgraph "数据库 (PostgreSQL)"
            DB_USER[User DB]
            DB_ORDER[Order DB]
            DB_INV[Inventory DB]
            DB_FIN[Finance DB]
        end

        GW --> USER
        GW --> ORDER
        GW --> INV

        USER -- CRUD --> DB_USER
        ORDER -- CRUD --> DB_ORDER
        INV -- CRUD --> DB_INV
        FIN -- CRUD --> DB_FIN
        
        ORDER -- "orders.created" Event --> KAFKA
        KAFKA -- Consumes --> INV
        KAFKA -- Consumes --> FIN
    end

    subgraph "第三方集成"
        LHDN[LHDN MyInvois API]
    end

    POS --> GW
    WMS --> GW
    ECOM --> GW
    FIN --> LHDN
```

### 架构与设计模式
* **微服务架构**: 将功能拆分为独立、自治的服务，便于独立开发、部署和扩展。
* **事件驱动架构**: 服务间通过 Kafka 发布和订阅事件，实现异步通信，提高系统弹性和响应能力。
* **API 优先**: 每个服务通过定义良好的 API 暴露功能，API 是服务间交互的契约。
* **每个服务一个数据库**: 确保服务间的松耦合和数据模型的独立性。

## 3. 技术栈
| 类别 | 技术选型 | 备注 |
| :--- | :--- | :--- |
| **开发语言** | Go | 高性能，并发模型适合高并发场景，部署简单。 |
| **数据库** | PostgreSQL | 可靠的关系型数据库，为每个服务提供独立实例。 |
| **缓存** | Redis | 用于缓存热点数据，如库存查询，提升响应速度。 |
| **消息队列** | Kafka | 作为事件流平台，支撑异步通信和未来数据分析。 |
| **API 网关** | Traefik | 云原生，与 K8s 紧密集成，自动化服务发现和路由。 |
| **容器化** | Docker | 标准化的应用打包方式。 |
| **容器编排** | Kubernetes | 自动化部署、扩展和管理容器化应用。 |
| **监控** | Prometheus, Grafana | 收集和可视化系统性能指标。 |
| **日志** | EFK Stack | 集中管理和查询所有微服务的日志。 |

## 4. 核心服务模块说明

### API 网关 (Traefik)
* **职责**: 所有外部请求的唯一入口。
* **核心功能**:
    * **路由**: 自动发现并转发请求到正确的后端微服务。
    * **认证与授权**: 验证 JWT，确保只有授权用户能访问。
    * **安全**: 提供 SSL/TLS 终止、限流和基础防火墙功能。

### 用户与权限服务 (Go)
* **职责**: 管理所有用户、角色和权限。
* **核心功能**:
    * 用户/系统账户的注册、登录、信息管理。
    * API 密钥和 JWT 的生成与验证。
    * 角色与权限的分配。
* **数据库**: `user_db` (PostgreSQL)

### 订单服务 (Go)
* **职责**: 统一处理和管理来自所有渠道的订单。
* **核心功能**:
    * 提供 `POST /orders` API 用于创建订单。
    * 提供 `GET /orders/{id}` API 用于查询订单。
    * 当订单创建后，将“新订单”消息发送到 Kafka 的 `orders.created` 主题。
* **数据库**: `order_db` (PostgreSQL)

### 库存服务 (Go)
* **职责**: 所有库存数据的“单一真实来源”。
* **核心功能**:
    * 提供 `GET /inventory` API 用于查询库存。
    * 监听 Kafka 的 `orders.created` 主题，接收消息并异步扣减库存。
* **数据库**: `inventory_db` (PostgreSQL) + Redis 缓存

### 财务服务 (Go)
* **职责**: 处理交易财务数据，为外部会计平台集成提供标准化财务记录。
* **核心功能**:
    * 监听 Kafka 的 `orders.created` 主题，自动生成标准化财务分录。
    * 发布财务事件到 `financial.records.created` 主题，供外部会计平台消费。
    * 提供 REST API 端点，供会计平台查询财务数据和提交处理状态。
    * 维护审计追踪和财务记录完整性数据验证。
* **数据库**: `finance_db` (PostgreSQL)
* **集成模式**: 为外部会计系统提供事件驱动的数据服务（职责分离：CDH 处理交易数据，会计平台处理税务合规和电子发票）

## 5. 源代码仓库结构 (Monorepo)
```plaintext
/
├── apps/
│   ├── admin-ui/        # 史诗4中定义的管理后台前端
│   ├── finance-service/ # 财务与税务服务 (Go)
│   ├── inventory-service/ # 库存服务 (Go)
│   ├── order-service/   # 订单服务 (Go)
│   └── user-service/    # 用户与权限服务 (Go)
├── infra/
│   └── k8s/             # Kubernetes 部署配置文件 (YAML)
├── packages/
│   └── shared/          # Go 语言的共享代码/类型定义
├── .github/
│   └── workflows/       # CI/CD 管道定义
├── go.work              # Go Workspace 配置文件
└── README.md
```

## 6. 部署与运维
* **部署流程**: 开发者提交代码到 Git -> GitHub Actions 触发 CI/CD -> 自动运行测试 -> 构建 Docker 镜像并推送到镜像仓库 -> 更新 Kubernetes 的部署配置以拉取新镜像并滚动更新服务。
* **环境分离**: 使用 K8s 的命名空间 (Namespace) 来分离开发、测试和生产环境。
* **配置管理**: 使用 K8s 的 ConfigMaps 和 Secrets 来管理应用配置和敏感信息，而非硬编码在代码中