package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/go-redis/redis/v8"

	"github.com/company/cdh/apps/inventory-service/internal/config"
	"github.com/company/cdh/apps/inventory-service/internal/repository"
	"github.com/company/cdh/apps/inventory-service/internal/services"
	"github.com/company/cdh/apps/inventory-service/models"
)

// Server represents the HTTP server
type Server struct {
	server           *http.Server
	db               *sql.DB
	redisClient      *redis.Client
	processor        services.OrderEventProcessor
	inventoryHandler *InventoryHandler
}

// HealthResponse represents the health check response
type HealthResponse struct {
	Status    string            `json:"status"`
	Timestamp time.Time         `json:"timestamp"`
	Services  map[string]string `json:"services"`
}

// NewServer creates a new HTTP server
func NewServer(cfg config.ServerConfig, db *sql.DB, redisClient *redis.Client, processor services.OrderEventProcessor) *Server {
	mux := http.NewServeMux()

	// Create repository, service and handlers
	repo := repository.NewInventoryRepository(db)
	inventoryService := services.NewInventoryService(repo, redisClient)
	inventoryHandler := NewInventoryHandler(inventoryService)

	server := &Server{
		server: &http.Server{
			Addr:    ":" + cfg.Port,
			Handler: mux,
		},
		db:               db,
		redisClient:      redisClient,
		processor:        processor,
		inventoryHandler: inventoryHandler,
	}

	// Register routes
	mux.HandleFunc("/health", server.healthHandler)
	mux.HandleFunc("/health/ready", server.readinessHandler)
	mux.HandleFunc("/health/live", server.livenessHandler)
	mux.HandleFunc("/inventory", server.inventoryHandler.GetInventory)

	return server
}

// Start starts the HTTP server
func (s *Server) Start() error {
	return s.server.ListenAndServe()
}

// Shutdown gracefully shuts down the HTTP server
func (s *Server) Shutdown(ctx context.Context) error {
	return s.server.Shutdown(ctx)
}

// healthHandler handles general health checks
func (s *Server) healthHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	services := make(map[string]string)

	// Check database connection
	if err := s.db.Ping(); err != nil {
		services["database"] = "unhealthy"
	} else {
		services["database"] = "healthy"
	}

	// Check Redis connection
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	if err := s.redisClient.Ping(ctx).Err(); err != nil {
		services["redis"] = "unhealthy"
	} else {
		services["redis"] = "healthy"
	}

	// Check inventory processor status
	if s.processor != nil {
		// Test processor with a simple validation
		testEvent := &models.OrderCreatedEvent{
			OrderID:    "health-check",
			CustomerID: "health-check",
			Items:      []models.OrderItem{},
			Status:     "pending",
			CreatedAt:  time.Now(),
		}
		if err := s.processor.ValidateOrderEvent(testEvent); err != nil {
			services["inventory_processor"] = "unhealthy"
		} else {
			services["inventory_processor"] = "healthy"
		}
	} else {
		services["inventory_processor"] = "not_configured"
	}

	// Determine overall status
	status := "healthy"
	for _, serviceStatus := range services {
		if serviceStatus == "unhealthy" {
			status = "unhealthy"
			break
		}
	}

	response := HealthResponse{
		Status:    status,
		Timestamp: time.Now(),
		Services:  services,
	}

	w.Header().Set("Content-Type", "application/json")
	if status == "unhealthy" {
		w.WriteHeader(http.StatusServiceUnavailable)
	}

	json.NewEncoder(w).Encode(response)
}

// readinessHandler handles Kubernetes readiness probes
func (s *Server) readinessHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Check if service is ready to accept traffic
	if err := s.db.Ping(); err != nil {
		http.Error(w, "Database not ready", http.StatusServiceUnavailable)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	if err := s.redisClient.Ping(ctx).Err(); err != nil {
		http.Error(w, "Redis not ready", http.StatusServiceUnavailable)
		return
	}

	w.WriteHeader(http.StatusOK)
	fmt.Fprint(w, "Ready")
}

// livenessHandler handles Kubernetes liveness probes
func (s *Server) livenessHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Simple liveness check - service is alive if it can respond
	w.WriteHeader(http.StatusOK)
	fmt.Fprint(w, "Alive")
}
