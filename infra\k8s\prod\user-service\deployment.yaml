apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: cdh-prod
  labels:
    app: user-service
    component: api
    environment: production
    project: cdh
spec:
  replicas: 3  # Production scaling
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
        component: api
        environment: production
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 65532  # nonroot user from distroless image
        runAsGroup: 65532
        fsGroup: 65532
      containers:
      - name: user-service
        image: user-service:latest
        imagePullPolicy: Always  # Pull latest for production
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "info"
        - name: DB_HOST
          value: "user-db.cdh-prod.svc.cluster.local"
        - name: DB_PORT
          value: "5432"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: user-db-secret
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: user-db-secret
              key: password
        - name: DB_NAME
          value: "user_db"
        - name: DB_SSLMODE
          value: "require"  # Enable SSL in production
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: user-service-secret
              key: jwt-secret
        - name: JWT_EXPIRATION_HOURS
          value: "24"
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true  # Distroless image supports read-only filesystem
          runAsNonRoot: true
          runAsUser: 65532
          capabilities:
            drop:
            - ALL
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3

---
apiVersion: v1
kind: Secret
metadata:
  name: user-service-secret
  namespace: cdh-prod
type: Opaque
data:
  jwt-secret: Y2hhbmdlLW1lLWluLXByb2R1Y3Rpb24tand0LXNlY3JldA==  # change-me-in-production-jwt-secret (base64)