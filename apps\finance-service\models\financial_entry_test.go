package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestFinancialEntry_Validate(t *testing.T) {
	tests := []struct {
		name    string
		entry   FinancialEntry
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid financial entry",
			entry: FinancialEntry{
				OrderID:         "order-123",
				TransactionID:   "txn-123",
				RevenueAmount:   100.00,
				TaxAmount:       6.00,
				Currency:        "MYR",
				PaymentMethod:   "credit_card",
				TransactionType: "sale",
			},
			wantErr: false,
		},
		{
			name: "missing order_id",
			entry: FinancialEntry{
				TransactionID:   "txn-123",
				RevenueAmount:   100.00,
				TaxAmount:       6.00,
				Currency:        "MYR",
				PaymentMethod:   "credit_card",
				TransactionType: "sale",
			},
			wantErr: true,
			errMsg:  "order_id is required",
		},
		{
			name: "missing transaction_id",
			entry: FinancialEntry{
				OrderID:         "order-123",
				RevenueAmount:   100.00,
				TaxAmount:       6.00,
				Currency:        "MYR",
				PaymentMethod:   "credit_card",
				TransactionType: "sale",
			},
			wantErr: true,
			errMsg:  "transaction_id is required",
		},
		{
			name: "negative revenue amount",
			entry: FinancialEntry{
				OrderID:         "order-123",
				TransactionID:   "txn-123",
				RevenueAmount:   -100.00,
				TaxAmount:       6.00,
				Currency:        "MYR",
				PaymentMethod:   "credit_card",
				TransactionType: "sale",
			},
			wantErr: true,
			errMsg:  "revenue_amount cannot be negative",
		},
		{
			name: "negative tax amount",
			entry: FinancialEntry{
				OrderID:         "order-123",
				TransactionID:   "txn-123",
				RevenueAmount:   100.00,
				TaxAmount:       -6.00,
				Currency:        "MYR",
				PaymentMethod:   "credit_card",
				TransactionType: "sale",
			},
			wantErr: true,
			errMsg:  "tax_amount cannot be negative",
		},
		{
			name: "missing currency",
			entry: FinancialEntry{
				OrderID:         "order-123",
				TransactionID:   "txn-123",
				RevenueAmount:   100.00,
				TaxAmount:       6.00,
				PaymentMethod:   "credit_card",
				TransactionType: "sale",
			},
			wantErr: true,
			errMsg:  "currency is required",
		},
		{
			name: "missing payment method",
			entry: FinancialEntry{
				OrderID:         "order-123",
				TransactionID:   "txn-123",
				RevenueAmount:   100.00,
				TaxAmount:       6.00,
				Currency:        "MYR",
				TransactionType: "sale",
			},
			wantErr: true,
			errMsg:  "payment_method is required",
		},
		{
			name: "missing transaction type",
			entry: FinancialEntry{
				OrderID:       "order-123",
				TransactionID: "txn-123",
				RevenueAmount: 100.00,
				TaxAmount:     6.00,
				Currency:      "MYR",
				PaymentMethod: "credit_card",
			},
			wantErr: true,
			errMsg:  "transaction_type is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.entry.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestFinancialEntry_CalculateTotalAmount(t *testing.T) {
	entry := FinancialEntry{
		RevenueAmount: 100.00,
		TaxAmount:     6.00,
	}

	total := entry.CalculateTotalAmount()
	assert.Equal(t, 106.00, total)
}

func TestFinancialEntry_Structure(t *testing.T) {
	now := time.Now()
	entry := FinancialEntry{
		ID:              1,
		OrderID:         "order-123",
		TransactionID:   "txn-123",
		RevenueAmount:   100.00,
		TaxAmount:       6.00,
		Currency:        "MYR",
		PaymentMethod:   "credit_card",
		TransactionType: "sale",
		Description:     "Test transaction",
		CreatedAt:       now,
		UpdatedAt:       now,
	}

	assert.Equal(t, 1, entry.ID)
	assert.Equal(t, "order-123", entry.OrderID)
	assert.Equal(t, "txn-123", entry.TransactionID)
	assert.Equal(t, 100.00, entry.RevenueAmount)
	assert.Equal(t, 6.00, entry.TaxAmount)
	assert.Equal(t, "MYR", entry.Currency)
	assert.Equal(t, "credit_card", entry.PaymentMethod)
	assert.Equal(t, "sale", entry.TransactionType)
	assert.Equal(t, "Test transaction", entry.Description)
	assert.Equal(t, now, entry.CreatedAt)
	assert.Equal(t, now, entry.UpdatedAt)
}