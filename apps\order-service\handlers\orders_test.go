package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/company/cdh/apps/order-service/models"
	"github.com/company/cdh/apps/order-service/services"
)

// MockOrderService is a mock implementation of services.OrderServiceInterface
type MockOrderService struct {
	mock.Mock
}

// Ensure MockOrderService implements the interface
var _ services.OrderServiceInterface = (*MockOrderService)(nil)

func (m *MockOrderService) CreateOrder(ctx context.Context, req *models.OrderRequest) (*models.Order, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Order), args.Error(1)
}

func (m *MockOrderService) GetOrderByID(ctx context.Context, id int) (*models.Order, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Order), args.Error(1)
}

func TestOrdersHandler_MethodNotAllowed(t *testing.T) {
	tests := []struct {
		name   string
		method string
	}{
		{"GET not allowed", http.MethodGet},
		{"PUT not allowed", http.MethodPut},
		{"DELETE not allowed", http.MethodDelete},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock service and handler
			mockService := new(MockOrderService)
			orderHandler := NewOrderHandler(mockService)

			req, err := http.NewRequest(tt.method, "/orders", nil)
			require.NoError(t, err)

			rr := httptest.NewRecorder()
			handler := http.HandlerFunc(orderHandler.OrdersHandler)

			handler.ServeHTTP(rr, req)

			assert.Equal(t, http.StatusMethodNotAllowed, rr.Code)
		})
	}
}

func TestOrderByIDHandler_MethodNotAllowed(t *testing.T) {
	// Create mock service and handler
	mockService := new(MockOrderService)
	orderHandler := NewOrderHandler(mockService)

	req, err := http.NewRequest(http.MethodPost, "/orders/1", nil)
	require.NoError(t, err)

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(orderHandler.OrderByIDHandler)

	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusMethodNotAllowed, rr.Code)
}

func TestOrderByIDHandler_InvalidID(t *testing.T) {
	tests := []struct {
		name string
		path string
	}{
		{"empty ID", "/orders/"},
		{"non-numeric ID", "/orders/abc"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock service and handler
			mockService := new(MockOrderService)
			orderHandler := NewOrderHandler(mockService)

			req, err := http.NewRequest(http.MethodGet, tt.path, nil)
			require.NoError(t, err)

			rr := httptest.NewRecorder()
			handler := http.HandlerFunc(orderHandler.OrderByIDHandler)

			handler.ServeHTTP(rr, req)

			assert.Equal(t, http.StatusBadRequest, rr.Code)
		})
	}
}

func TestCreateOrder_InvalidJSON(t *testing.T) {
	// Create mock service and handler
	mockService := new(MockOrderService)
	orderHandler := NewOrderHandler(mockService)

	invalidJSON := `{"invalid": json}`

	req, err := http.NewRequest(http.MethodPost, "/orders", bytes.NewBufferString(invalidJSON))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(orderHandler.OrdersHandler)

	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusBadRequest, rr.Code)
}

func TestCreateOrder_ValidationError(t *testing.T) {
	// Create mock service and handler
	mockService := new(MockOrderService)
	orderHandler := NewOrderHandler(mockService)

	invalidOrder := models.OrderRequest{
		ProductInfo: "", // Invalid: empty product info
		Quantity:    1,
		Price:       10.00,
	}

	jsonData, err := json.Marshal(invalidOrder)
	require.NoError(t, err)

	req, err := http.NewRequest(http.MethodPost, "/orders", bytes.NewBuffer(jsonData))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(orderHandler.OrdersHandler)

	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusBadRequest, rr.Code)
	assert.Contains(t, rr.Body.String(), "product_info is required")
}

func TestCreateOrder_Success(t *testing.T) {
	// Create mock service and handler
	mockService := new(MockOrderService)
	orderHandler := NewOrderHandler(mockService)

	// Test data
	orderReq := models.OrderRequest{
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       99.99,
	}

	expectedOrder := &models.Order{
		ID:          123,
		OrderNumber: "ORD-123456",
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       99.99,
		Status:      models.OrderStatusPending,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Set up mock expectations
	mockService.On("CreateOrder", mock.Anything, &orderReq).Return(expectedOrder, nil)

	// Prepare request
	jsonData, err := json.Marshal(orderReq)
	require.NoError(t, err)

	req, err := http.NewRequest(http.MethodPost, "/orders", bytes.NewBuffer(jsonData))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(orderHandler.OrdersHandler)

	// Execute
	handler.ServeHTTP(rr, req)

	// Assertions
	assert.Equal(t, http.StatusCreated, rr.Code)
	assert.Contains(t, rr.Body.String(), "Order created successfully")
	assert.Contains(t, rr.Body.String(), "ORD-123456")
	mockService.AssertExpectations(t)
}

func TestCreateOrder_ServiceError(t *testing.T) {
	// Create mock service and handler
	mockService := new(MockOrderService)
	orderHandler := NewOrderHandler(mockService)

	// Test data
	orderReq := models.OrderRequest{
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       99.99,
	}

	// Set up mock expectations - service returns error
	mockService.On("CreateOrder", mock.Anything, &orderReq).Return((*models.Order)(nil), errors.New("service error"))

	// Prepare request
	jsonData, err := json.Marshal(orderReq)
	require.NoError(t, err)

	req, err := http.NewRequest(http.MethodPost, "/orders", bytes.NewBuffer(jsonData))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(orderHandler.OrdersHandler)

	// Execute
	handler.ServeHTTP(rr, req)

	// Assertions
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
	assert.Contains(t, rr.Body.String(), "Failed to create order")
	mockService.AssertExpectations(t)
}
