# 6. Deployment and Operations
* **Deployment Process**: Developer commits code to Git -> GitHub Actions triggers CI/CD -> Automatically runs tests -> Builds Docker image and pushes to an image registry -> Updates Kubernetes deployment configuration to pull the new image and perform a rolling update of the service.
* **Environment Separation**: Use K8s Namespaces to separate development, testing, and production environments.
* **Configuration Management**: Use K8s ConfigMaps and Secrets to manage application configuration and sensitive information, instead of hardcoding them.