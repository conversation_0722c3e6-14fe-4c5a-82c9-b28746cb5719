# CDH Development Services Management Script
# This script manages Kafka, Redis, and PostgreSQL services for development

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("all", "databases", "messaging", "cache", "tools")]
    [string]$Services = "all",
    
    [Parameter(Mandatory=$false)]
    [switch]$Stop,
    
    [Parameter(Mandatory=$false)]
    [switch]$Restart,
    
    [Parameter(Mandatory=$false)]
    [switch]$Status,
    
    [Parameter(Mandatory=$false)]
    [switch]$Logs,
    
    [Parameter(Mandatory=$false)]
    [switch]$Clean
)

# Color functions
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

# Check if Docker is running
function Test-DockerRunning {
    try {
        docker version | Out-Null
        return $true
    }
    catch {
        Write-Error "Docker is not running. Please start Docker Desktop."
        return $false
    }
}

# Create network if it doesn't exist
function Initialize-Network {
    $networkExists = docker network ls --filter name=cdh-network --format "{{.Name}}" | Select-String "cdh-network"
    if (-not $networkExists) {
        Write-Info "Creating CDH network..."
        docker network create cdh-network
        Write-Success "CDH network created"
    }
}

# Service definitions
$ServiceGroups = @{
    "databases" = @{
        "compose_file" = "docker-compose.databases.yml"
        "services" = @("user-db", "order-db", "inventory-db", "finance-db")
        "description" = "PostgreSQL databases"
    }
    "messaging" = @{
        "compose_file" = "docker-compose.services.yml"
        "services" = @("zookeeper", "kafka")
        "description" = "Kafka messaging"
    }
    "cache" = @{
        "compose_file" = "docker-compose.services.yml"
        "services" = @("redis")
        "description" = "Redis cache"
    }
    "tools" = @{
        "compose_file" = "docker-compose.services.yml"
        "services" = @("redis-commander", "kafka-ui")
        "description" = "Management tools"
        "profile" = "tools"
    }
}

function Start-ServiceGroup {
    param($GroupName, $GroupConfig)
    
    Write-Info "Starting $($GroupConfig.description)..."
    
    $composeArgs = @("-f", $GroupConfig.compose_file, "up", "-d")
    
    if ($GroupConfig.profile) {
        $composeArgs += @("--profile", $GroupConfig.profile)
    }
    
    $composeArgs += $GroupConfig.services
    
    & docker-compose @composeArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "$($GroupConfig.description) started successfully"
    } else {
        Write-Error "Failed to start $($GroupConfig.description)"
    }
}

function Stop-ServiceGroup {
    param($GroupName, $GroupConfig)
    
    Write-Info "Stopping $($GroupConfig.description)..."
    
    $composeArgs = @("-f", $GroupConfig.compose_file, "stop")
    $composeArgs += $GroupConfig.services
    
    & docker-compose @composeArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "$($GroupConfig.description) stopped successfully"
    } else {
        Write-Error "Failed to stop $($GroupConfig.description)"
    }
}

function Show-ServiceStatus {
    Write-Info "=== CDH Development Services Status ==="
    Write-Host ""
    
    foreach ($group in $ServiceGroups.Keys) {
        $config = $ServiceGroups[$group]
        Write-Host "$($config.description):" -ForegroundColor Yellow
        
        foreach ($service in $config.services) {
            $status = docker-compose -f $config.compose_file ps --services --filter "status=running" | Select-String "^$service$"
            if ($status) {
                Write-Host "  ✓ $service" -ForegroundColor Green
            } else {
                Write-Host "  ✗ $service" -ForegroundColor Red
            }
        }
        Write-Host ""
    }
    
    # Show service URLs
    Write-Info "=== Service URLs ==="
    Write-Host "PostgreSQL Databases:"
    Write-Host "  User DB:      localhost:5432"
    Write-Host "  Order DB:     localhost:5433"
    Write-Host "  Inventory DB: localhost:5434"
    Write-Host "  Finance DB:   localhost:5435"
    Write-Host ""
    Write-Host "Messaging and Cache:"
    Write-Host "  Kafka:        localhost:9092"
    Write-Host "  Redis:        localhost:6379"
    Write-Host ""
    Write-Host "Management Tools:"
    Write-Host "  Kafka UI:     http://localhost:8080"
    Write-Host "  Redis UI:     http://localhost:8081 (admin/admin)"
}

function Show-ServiceLogs {
    param($ServiceName)
    
    if ($ServiceName) {
        Write-Info "Showing logs for $ServiceName..."
        docker-compose -f docker-compose.databases.yml -f docker-compose.services.yml logs -f $ServiceName
    } else {
        Write-Info "Showing logs for all services..."
        docker-compose -f docker-compose.databases.yml -f docker-compose.services.yml logs -f
    }
}

function Clean-Services {
    Write-Warning "This will remove all containers, volumes, and data. Are you sure? (y/N)"
    $confirmation = Read-Host
    
    if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
        Write-Info "Stopping and removing all services..."
        docker-compose -f docker-compose.databases.yml -f docker-compose.services.yml down -v --remove-orphans
        
        Write-Info "Removing CDH network..."
        docker network rm cdh-network -f
        
        Write-Success "All services cleaned up"
    } else {
        Write-Info "Clean operation cancelled"
    }
}

# Main execution
if (-not (Test-DockerRunning)) {
    exit 1
}

Initialize-Network

if ($Clean) {
    Clean-Services
    exit 0
}

if ($Status) {
    Show-ServiceStatus
    exit 0
}

if ($Logs) {
    Show-ServiceLogs
    exit 0
}

# Determine which services to manage
$servicesToManage = @()
if ($Services -eq "all") {
    $servicesToManage = @("databases", "messaging", "cache")
} else {
    $servicesToManage = @($Services)
}

# Execute the requested action
foreach ($serviceGroup in $servicesToManage) {
    if ($ServiceGroups.ContainsKey($serviceGroup)) {
        $config = $ServiceGroups[$serviceGroup]
        
        if ($Stop) {
            Stop-ServiceGroup $serviceGroup $config
        } elseif ($Restart) {
            Stop-ServiceGroup $serviceGroup $config
            Start-Sleep -Seconds 2
            Start-ServiceGroup $serviceGroup $config
        } else {
            Start-ServiceGroup $serviceGroup $config
        }
    } else {
        Write-Error "Unknown service group: $serviceGroup"
    }
}

if (-not $Stop) {
    Write-Host ""
    Show-ServiceStatus
}
