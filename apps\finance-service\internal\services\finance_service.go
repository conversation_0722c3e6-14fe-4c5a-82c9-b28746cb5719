package services

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/company/cdh/apps/finance-service/internal/producer"
	"github.com/company/cdh/apps/finance-service/internal/repository"
	"github.com/company/cdh/apps/finance-service/models"
)

// Currency constants
const (
	// DefaultCurrency represents the default currency for financial records
	DefaultCurrency = "MYR"
	// DefaultPaymentMethod represents the default payment method when unknown
	DefaultPaymentMethod = "unknown"
	// DefaultTransactionType represents the default transaction type
	DefaultTransactionType = "sale"
)

// financeService implements the FinanceServiceInterface
type financeService struct {
	financeRepo   repository.FinanceRepository
	eventProducer producer.FinancialEventProducerInterface
}

// NewFinanceService creates a new finance service
func NewFinanceService(financeRepo repository.FinanceRepository, eventProducer producer.FinancialEventProducerInterface) FinanceServiceInterface {
	return &financeService{
		financeRepo:   financeRepo,
		eventProducer: eventProducer,
	}
}

// ProcessOrderEvent processes an order event and generates financial records
func (s *financeService) ProcessOrderEvent(messageValue []byte) error {
	// Parse the order event
	orderEvent, err := models.ParseOrderEvent(messageValue)
	if err != nil {
		return fmt.Errorf("failed to parse order event: %w", err)
	}

	log.Printf("Processing order event: OrderID=%s, EventID=%s", orderEvent.OrderID, orderEvent.EventID)

	// Check if this event has already been processed
	existingTransaction, err := s.financeRepo.GetFinancialTransactionByEventID(orderEvent.EventID)
	if err == nil && existingTransaction != nil {
		log.Printf("Event %s already processed, skipping", orderEvent.EventID)
		return nil
	}

	// Create financial transaction record for audit trail
	transaction := &models.FinancialTransaction{
		OrderReference:   orderEvent.OrderNumber,
		EventID:          orderEvent.EventID,
		ProcessingStatus: models.ProcessingStatusPending,
	}

	if err := s.financeRepo.CreateFinancialTransaction(transaction); err != nil {
		return fmt.Errorf("failed to create financial transaction: %w", err)
	}

	// Generate financial entry
	financialEntry, err := s.generateFinancialEntry(orderEvent)
	if err != nil {
		// Mark transaction as failed
		transaction.MarkFailed(err.Error())
		if updateErr := s.financeRepo.UpdateFinancialTransaction(transaction); updateErr != nil {
			log.Printf("Failed to update transaction status: %v", updateErr)
		}
		return fmt.Errorf("failed to generate financial entry: %w", err)
	}

	// Save financial entry
	if err := s.financeRepo.CreateFinancialEntry(financialEntry); err != nil {
		// Mark transaction as failed
		transaction.MarkFailed(err.Error())
		if updateErr := s.financeRepo.UpdateFinancialTransaction(transaction); updateErr != nil {
			log.Printf("Failed to update transaction status: %v", updateErr)
		}
		return fmt.Errorf("failed to save financial entry: %w", err)
	}

	// Publish financial record event to external systems
	if err := s.PublishFinancialEvent(financialEntry); err != nil {
		log.Printf("Failed to publish financial record event: %v", err)
		// Don't fail the entire process if event publishing fails
		// The financial entry was created successfully
	}

	// Mark transaction as completed
	transaction.MarkCompleted(financialEntry.ID)
	if err := s.financeRepo.UpdateFinancialTransaction(transaction); err != nil {
		log.Printf("Failed to update transaction status to completed: %v", err)
		// Don't return error as the financial entry was created successfully
	}

	log.Printf("Successfully processed order event: OrderID=%s, FinancialEntryID=%d",
		orderEvent.OrderID, financialEntry.ID)

	return nil
}

// generateFinancialEntry creates a financial entry from an order event
func (s *financeService) generateFinancialEntry(orderEvent *models.OrderCreatedEvent) (*models.FinancialEntry, error) {
	// Generate unique transaction ID
	transactionID := fmt.Sprintf("TXN-%s-%d", orderEvent.OrderID, time.Now().Unix())

	// Calculate financial amounts
	revenueAmount := orderEvent.CalculateRevenue()
	taxAmount := orderEvent.CalculateTax()

	// Create financial entry
	entry := &models.FinancialEntry{
		OrderID:         orderEvent.OrderID,
		TransactionID:   transactionID,
		RevenueAmount:   revenueAmount,
		TaxAmount:       taxAmount,
		Currency:        DefaultCurrency,
		PaymentMethod:   DefaultPaymentMethod, // Will be updated when payment info is available
		TransactionType: DefaultTransactionType,
		Description:     fmt.Sprintf("Financial record for order %s - %s", orderEvent.OrderNumber, orderEvent.ProductInfo),
	}

	// Validate the entry
	if err := entry.Validate(); err != nil {
		return nil, fmt.Errorf("invalid financial entry: %w", err)
	}

	return entry, nil
}

// PublishFinancialEvent publishes a financial record event to external systems
func (s *financeService) PublishFinancialEvent(financialEntry *models.FinancialEntry) error {
	// Create financial record event from the entry
	event := models.NewFinancialRecordEventFromEntry(financialEntry)

	// Publish the event
	ctx := context.Background()
	if err := s.eventProducer.PublishFinancialRecord(ctx, event); err != nil {
		return fmt.Errorf("failed to publish financial record event: %w", err)
	}

	log.Printf("Successfully published financial record event for transaction %s", event.TransactionID)
	return nil
}

// GetFinancialRecords retrieves financial records with filtering and pagination
func (s *financeService) GetFinancialRecords(filters *models.QueryFilters) ([]*models.FinancialEntry, int, error) {
	return s.financeRepo.GetFinancialRecords(filters)
}

// UpdateFinancialRecordStatus updates the status of a financial record with audit trail
func (s *financeService) UpdateFinancialRecordStatus(id int, status string, notes string) error {
	// First, get the current record to capture the old status
	_, err := s.financeRepo.GetFinancialRecordByID(id)
	if err != nil {
		return err
	}

	// For now, we'll assume the old status is "pending" since the database schema
	// doesn't include status column yet. In the future, this would be currentRecord.Status
	oldStatus := "pending"

	// Update the record status
	err = s.financeRepo.UpdateFinancialRecordStatus(id, status, notes)
	if err != nil {
		return err
	}

	// Create audit trail entry
	var oldStatusPtr *string
	if oldStatus != "" {
		oldStatusPtr = &oldStatus
	}

	var notesPtr *string
	if notes != "" {
		notesPtr = &notes
	}

	auditEntry := &models.AuditTrailEntry{
		RecordID:  id,
		OldStatus: oldStatusPtr,
		NewStatus: status,
		Notes:     notesPtr,
		ChangedBy: "external_api", // In the future, this would come from authentication context
		ChangedAt: time.Now(),
		IPAddress: nil, // Would be populated from request context
		UserAgent: nil, // Would be populated from request context
	}

	// Log the audit trail (for now just logging, in future would be stored in database)
	err = s.financeRepo.CreateAuditTrailEntry(auditEntry)
	if err != nil {
		// Log the error but don't fail the main operation
		// In production, you might want to use a proper logger
		fmt.Printf("Warning: Failed to create audit trail entry: %v\n", err)
	}

	return nil
}
