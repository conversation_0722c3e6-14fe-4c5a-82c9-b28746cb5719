# Inventory Service

The Inventory Service is a microservice responsible for inventory management and stock tracking within the Central Data Hub (CDH) platform. It serves as the single source of truth for all inventory data across omni-channel operations.

## Features

- Real-time inventory tracking and management
- Atomic inventory operations (query, deduct, reserve, release)
- Redis caching for high-performance queries
- Kafka integration for event-driven inventory updates
- PostgreSQL database with transaction support
- Comprehensive health check endpoints
- Graceful degradation when external services are unavailable

## API Endpoints

### Inventory Query

**GET** `/inventory?sku={sku1},{sku2}`

Query inventory information for one or more SKUs.

**Parameters:**
- `sku` (required): Comma-separated list of SKU codes to query

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "sku": "LAPTOP-001",
      "product_name": "Gaming Laptop",
      "description": "High-performance gaming laptop",
      "stock_quantity": 50,
      "reserved_quantity": 5,
      "available_quantity": 45,
      "unit_price": 2999.99,
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    }
  ],
  "cache_hit": true
}
```

**Error Responses:**
- `400 Bad Request`: Missing or invalid SKU parameter
- `404 Not Found`: One or more SKUs not found
- `500 Internal Server Error`: Database or cache error

### Health Check Endpoints

**GET** `/health/live`

Returns the liveness status of the service.

**Response:**
```json
{
  "status": "alive",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**GET** `/health/ready`

Returns the readiness status of the service and its dependencies.

**Response:**
```json
{
  "status": "ready",
  "timestamp": "2024-01-01T12:00:00Z",
  "dependencies": {
    "database": "healthy",
    "redis": "healthy"
  }
}
```

**GET** `/health`

Returns comprehensive health information including all dependencies.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "dependencies": {
    "database": "healthy",
    "redis": "healthy",
    "kafka": "healthy"
  }
}
```

## Event Processing

The service listens to Kafka events for inventory updates:

### Orders Created Event

**Topic:** `orders.created`

The service automatically processes order events and deducts inventory:

1. Validates all items in the order
2. Checks stock availability
3. Deducts inventory atomically
4. Publishes failure events if needed

### Inventory Failure Events

**Topic:** `inventory.failures`

Published when inventory operations fail:

```json
{
  "order_id": "ORDER-123",
  "sku": "LAPTOP-001",
  "requested_quantity": 5,
  "available_quantity": 2,
  "failure_type": "out_of_stock",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | `8081` | HTTP server port |
| `DB_HOST` | `127.0.0.1` | PostgreSQL host |
| `DB_PORT` | `5434` | PostgreSQL port |
| `DB_USER` | `postgres` | PostgreSQL username |
| `DB_PASSWORD` | `postgres` | PostgreSQL password |
| `DB_NAME` | `inventory_db` | PostgreSQL database name |
| `DB_SSLMODE` | `disable` | PostgreSQL SSL mode |
| `REDIS_HOST` | `127.0.0.1` | Redis host |
| `REDIS_PORT` | `6379` | Redis port |
| `REDIS_PASSWORD` | `` | Redis password |
| `REDIS_DB` | `0` | Redis database number |
| `KAFKA_BROKERS` | `127.0.0.1:9092` | Kafka broker addresses |
| `KAFKA_GROUP_ID` | `inventory-service-group` | Kafka consumer group |
| `KAFKA_TOPIC` | `orders.created` | Kafka topic to consume |
| `KAFKA_FAILURE_TOPIC` | `inventory.failures` | Kafka topic for failures |

## Database Schema

### Inventory Table

```sql
CREATE TABLE inventory (
    id SERIAL PRIMARY KEY,
    sku VARCHAR(255) UNIQUE NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    description TEXT,
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    reserved_quantity INTEGER NOT NULL DEFAULT 0,
    unit_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Computed Fields

- `available_quantity`: Calculated as `stock_quantity - reserved_quantity`

## Performance Optimization

### Redis Caching

The service implements a cache-aside pattern with Redis:

- **Cache Key Format**: `inventory:sku:{SKU}`
- **TTL**: 5 minutes
- **Performance**: ~4.6x faster for cache hits
- **Graceful Degradation**: Falls back to database when Redis is unavailable

### Database Optimization

- Indexed SKU column for fast lookups
- Batch queries for multiple SKUs
- Connection pooling for concurrent requests
- Atomic transactions for inventory operations

## Development

### Prerequisites

- Go 1.21+
- PostgreSQL 15+
- Redis 7+
- Kafka 2.8+ (optional, graceful degradation)

### Local Development

1. **Start Dependencies:**
   ```bash
   # Start all services
   .\scripts\dev\start-services.ps1
   
   # Or start individually
   docker-compose -f docker-compose.databases.yml up -d
   docker-compose -f docker-compose.services.yml up -d
   ```

2. **Run the Service:**
   ```bash
   cd apps/inventory-service
   go run main.go
   ```

3. **Run Tests:**
   ```bash
   # Unit tests
   go test ./...
   
   # Integration tests (requires running services)
   go test ./integration_test.go -v
   
   # With coverage
   go test ./... -cover
   ```

### Building

```bash
# Build binary
go build -o inventory-service .

# Build Docker image
docker build -t inventory-service .
```

## Deployment

### Kubernetes

The service is deployed using Kubernetes manifests in `infra/k8s/dev/inventory-service/`:

```bash
kubectl apply -f infra/k8s/dev/inventory-service/
```

### Docker Compose

For local development:

```bash
docker-compose up inventory-service
```

## Monitoring

### Metrics

The service exposes health check endpoints for monitoring:

- **Liveness**: `/health/live` - Service is running
- **Readiness**: `/health/ready` - Service can handle requests
- **Health**: `/health` - Comprehensive health including dependencies

### Logging

Structured logging with configurable levels:

- `DEBUG`: Detailed operation logs
- `INFO`: General operation logs
- `WARN`: Warning conditions
- `ERROR`: Error conditions

## Security

- Read-only root filesystem in containers
- Non-root user execution
- Resource limits and requests
- Security context with dropped capabilities
- Input validation and sanitization
- SQL injection prevention with parameterized queries

## Architecture

The service follows clean architecture principles:

```
├── cmd/server/          # Application entry point
├── handlers/            # HTTP handlers and routing
├── internal/
│   ├── config/         # Configuration management
│   ├── consumer/       # Kafka consumer
│   ├── database/       # Database connections
│   ├── errors/         # Custom error types
│   ├── producer/       # Kafka producer
│   ├── repository/     # Data access layer
│   └── services/       # Business logic layer
├── models/             # Data models and validation
└── integration_test.go # Integration tests
```

## Contributing

1. Follow Go coding standards
2. Write comprehensive tests
3. Update documentation
4. Ensure all tests pass
5. Follow semantic versioning

## License

This project is part of the Central Data Hub (CDH) platform.
