package consumer

import (
	"context"
	"log"
	"sync"

	"github.com/IBM/sarama"
	"github.com/company/cdh/apps/finance-service/internal/config"
	"github.com/company/cdh/apps/finance-service/internal/services"
)

// KafkaConsumer handles Kafka message consumption
type KafkaConsumer struct {
	consumer       sarama.ConsumerGroup
	topics         []string
	financeService services.FinanceServiceInterface
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
}

// OrderEventHandler handles order events from Kafka
type OrderEventHandler struct {
	financeService services.FinanceServiceInterface
}

// NewKafkaConsumer creates a new Kafka consumer
func NewKafkaConsumer(cfg config.KafkaConfig, financeService services.FinanceServiceInterface) (*KafkaConsumer, error) {
	config := sarama.NewConfig()
	config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategyRoundRobin
	config.Consumer.Offsets.Initial = sarama.OffsetNewest
	config.Consumer.Return.Errors = true

	consumer, err := sarama.NewConsumerGroup(cfg.Brokers, cfg.GroupID, config)
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &KafkaConsumer{
		consumer:       consumer,
		topics:         []string{cfg.Topic},
		financeService: financeService,
		ctx:            ctx,
		cancel:         cancel,
	}, nil
}

// Start starts the Kafka consumer
func (kc *KafkaConsumer) Start() error {
	handler := &OrderEventHandler{
		financeService: kc.financeService,
	}

	kc.wg.Add(1)
	go func() {
		defer kc.wg.Done()
		for {
			select {
			case <-kc.ctx.Done():
				return
			default:
				if err := kc.consumer.Consume(kc.ctx, kc.topics, handler); err != nil {
					log.Printf("Error from consumer: %v", err)
				}
			}
		}
	}()

	// Handle consumer errors
	kc.wg.Add(1)
	go func() {
		defer kc.wg.Done()
		for {
			select {
			case err := <-kc.consumer.Errors():
				if err != nil {
					log.Printf("Consumer error: %v", err)
				}
			case <-kc.ctx.Done():
				return
			}
		}
	}()

	log.Printf("Kafka consumer started, consuming from topics: %v", kc.topics)
	return nil
}

// Close closes the Kafka consumer
func (kc *KafkaConsumer) Close() error {
	log.Println("Closing Kafka consumer...")
	kc.cancel()
	kc.wg.Wait()
	return kc.consumer.Close()
}

// Setup is run at the beginning of a new session, before ConsumeClaim
func (h *OrderEventHandler) Setup(sarama.ConsumerGroupSession) error {
	return nil
}

// Cleanup is run at the end of a session, once all ConsumeClaim goroutines have exited
func (h *OrderEventHandler) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim must start a consumer loop of ConsumerGroupClaim's Messages()
func (h *OrderEventHandler) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		select {
		case message := <-claim.Messages():
			if message == nil {
				return nil
			}
			
			log.Printf("Received message from topic %s, partition %d, offset %d", 
				message.Topic, message.Partition, message.Offset)
			
			if err := h.processOrderEvent(message.Value); err != nil {
				log.Printf("Error processing order event: %v", err)
				// Continue processing other messages even if one fails
			}
			
			session.MarkMessage(message, "")

		case <-session.Context().Done():
			return nil
		}
	}
}

// processOrderEvent processes an individual order event
func (h *OrderEventHandler) processOrderEvent(messageValue []byte) error {
	return h.financeService.ProcessOrderEvent(messageValue)
}