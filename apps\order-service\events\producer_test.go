package events

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/company/cdh/apps/order-service/internal/config"
	"github.com/company/cdh/apps/order-service/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockProducer is a mock implementation of the Producer interface
type MockProducer struct {
	mock.Mock
}

func (m *MockProducer) PublishOrderCreated(event *OrderCreatedEvent) error {
	args := m.Called(event)
	return args.Error(0)
}

func (m *MockProducer) Close() error {
	args := m.Called()
	return args.Error(0)
}

func TestMockProducer_PublishOrderCreated(t *testing.T) {
	// Create mock producer
	mockProducer := new(MockProducer)
	
	// Create test event
	order := &models.Order{
		ID:          123,
		OrderNumber: "ORD-123456",
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       99.99,
		Status:      models.OrderStatusPending,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	event := NewOrderCreatedEvent(order)

	// Set up mock expectations
	mockProducer.On("PublishOrderCreated", event).Return(nil)

	// Execute
	err := mockProducer.PublishOrderCreated(event)

	// Assertions
	assert.NoError(t, err)
	mockProducer.AssertExpectations(t)
}

func TestMockProducer_Close(t *testing.T) {
	// Create mock producer
	mockProducer := new(MockProducer)

	// Set up mock expectations
	mockProducer.On("Close").Return(nil)

	// Execute
	err := mockProducer.Close()

	// Assertions
	assert.NoError(t, err)
	mockProducer.AssertExpectations(t)
}

func TestOrderCreatedEventJSONSerialization(t *testing.T) {
	// Create test order
	order := &models.Order{
		ID:          789,
		OrderNumber: "ORD-789012",
		ProductInfo: "JSON Test Product",
		Quantity:    3,
		Price:       149.99,
		Status:      models.OrderStatusConfirmed,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Create event
	event := NewOrderCreatedEvent(order)

	// Serialize to JSON
	jsonData, err := json.Marshal(event)
	assert.NoError(t, err)
	assert.NotEmpty(t, jsonData)

	// Deserialize from JSON
	var deserializedEvent OrderCreatedEvent
	err = json.Unmarshal(jsonData, &deserializedEvent)
	assert.NoError(t, err)

	// Verify all fields are preserved
	assert.Equal(t, event.EventID, deserializedEvent.EventID)
	assert.Equal(t, event.EventType, deserializedEvent.EventType)
	assert.Equal(t, event.OrderID, deserializedEvent.OrderID)
	assert.Equal(t, event.OrderNumber, deserializedEvent.OrderNumber)
	assert.Equal(t, event.ProductInfo, deserializedEvent.ProductInfo)
	assert.Equal(t, event.Quantity, deserializedEvent.Quantity)
	assert.Equal(t, event.Price, deserializedEvent.Price)
	assert.Equal(t, event.Status, deserializedEvent.Status)
}

func TestKafkaConfigValidation(t *testing.T) {
	tests := []struct {
		name    string
		config  *config.KafkaConfig
		wantErr bool
	}{
		{
			name: "valid config",
			config: &config.KafkaConfig{
				Brokers: []string{"localhost:9092"},
				Topic:   "orders.created",
			},
			wantErr: false,
		},
		{
			name: "empty brokers",
			config: &config.KafkaConfig{
				Brokers: []string{},
				Topic:   "orders.created",
			},
			wantErr: true,
		},
		{
			name: "empty topic",
			config: &config.KafkaConfig{
				Brokers: []string{"localhost:9092"},
				Topic:   "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a full config with the Kafka config
			fullConfig := &config.Config{
				Server: config.ServerConfig{Port: "8080"},
				Database: config.DatabaseConfig{
					Host:     "localhost",
					Port:     "5433",
					User:     "postgres",
					Password: "postgres",
					DBName:   "order_db",
					SSLMode:  "disable",
				},
				Kafka: *tt.config,
			}

			err := fullConfig.Validate()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
