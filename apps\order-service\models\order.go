package models

import (
	"errors"
	"time"
)

// OrderStatus represents the status of an order
type OrderStatus string

const (
	OrderStatusPending   OrderStatus = "pending"
	OrderStatusConfirmed OrderStatus = "confirmed"
	OrderStatusShipped   OrderStatus = "shipped"
	OrderStatusDelivered OrderStatus = "delivered"
	OrderStatusCancelled OrderStatus = "cancelled"
)

// Order represents an order in the system
type Order struct {
	ID          int         `json:"id" db:"id"`
	OrderNumber string      `json:"order_number" db:"order_number"`
	ProductInfo string      `json:"product_info" db:"product_info"`
	Quantity    int         `json:"quantity" db:"quantity"`
	Price       float64     `json:"price" db:"price"`
	Status      OrderStatus `json:"status" db:"status"`
	CreatedAt   time.Time   `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time   `json:"updated_at" db:"updated_at"`
}

// OrderRequest represents the request payload for creating an order
type OrderRequest struct {
	ProductInfo string  `json:"product_info"`
	Quantity    int     `json:"quantity"`
	Price       float64 `json:"price"`
}

// OrderResponse represents the response for order operations
type OrderResponse struct {
	Order   *Order `json:"order,omitempty"`
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
}

// Validate validates the order request
func (req *OrderRequest) Validate() error {
	if req.ProductInfo == "" {
		return errors.New("product_info is required")
	}
	if req.Quantity <= 0 {
		return errors.New("quantity must be greater than 0")
	}
	if req.Price <= 0 {
		return errors.New("price must be greater than 0")
	}
	return nil
}

// ToOrder converts an OrderRequest to an Order with default values
func (req *OrderRequest) ToOrder() *Order {
	now := time.Now()
	return &Order{
		ProductInfo: req.ProductInfo,
		Quantity:    req.Quantity,
		Price:       req.Price,
		Status:      OrderStatusPending,
		CreatedAt:   now,
		UpdatedAt:   now,
	}
}

// IsValidStatus checks if the given status is valid
func IsValidStatus(status OrderStatus) bool {
	switch status {
	case OrderStatusPending, OrderStatusConfirmed, OrderStatusShipped, OrderStatusDelivered, OrderStatusCancelled:
		return true
	default:
		return false
	}
}