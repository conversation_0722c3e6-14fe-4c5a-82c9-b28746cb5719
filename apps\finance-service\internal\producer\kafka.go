package producer

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/IBM/sarama"
	"github.com/company/cdh/apps/finance-service/internal/config"
	"github.com/company/cdh/apps/finance-service/models"
)

// KafkaProducer handles publishing financial record events to Kafka
type KafkaProducer struct {
	producer sarama.SyncProducer
	topic    string
}

// ProducerConfig holds Kafka producer configuration
type ProducerConfig struct {
	Brokers []string
	Topic   string
}

// NewKafkaProducer creates a new Kafka producer for financial events
func NewKafkaProducer(cfg ProducerConfig) (*KafkaProducer, error) {
	config := sarama.NewConfig()
	config.Producer.RequiredAcks = sarama.WaitForAll // Wait for all replicas
	config.Producer.Retry.Max = 5                    // Retry up to 5 times
	config.Producer.Return.Successes = true
	config.Producer.Return.Errors = true
	config.Producer.Partitioner = sarama.NewHashPartitioner // Partition by key for ordering
	config.Producer.Idempotent = true                       // Enable idempotent producer
	config.Net.MaxOpenRequests = 1                          // Required for idempotent producer

	producer, err := sarama.NewSyncProducer(cfg.Brokers, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create Kafka producer: %w", err)
	}

	return &KafkaProducer{
		producer: producer,
		topic:    cfg.Topic,
	}, nil
}

// PublishFinancialRecord publishes a financial record event to Kafka
func (kp *KafkaProducer) PublishFinancialRecord(ctx context.Context, event *models.FinancialRecordEvent) error {
	// Validate the event before publishing
	if err := event.Validate(); err != nil {
		return fmt.Errorf("invalid financial record event: %w", err)
	}

	// Serialize the event to JSON
	eventJSON, err := event.ToJSON()
	if err != nil {
		return fmt.Errorf("failed to serialize financial record event: %w", err)
	}

	// Create Kafka message
	message := &sarama.ProducerMessage{
		Topic: kp.topic,
		Key:   sarama.StringEncoder(event.OrderReference), // Use order reference as key for partitioning
		Value: sarama.ByteEncoder(eventJSON),
		Headers: []sarama.RecordHeader{
			{
				Key:   []byte("event_type"),
				Value: []byte("financial.record.created"),
			},
			{
				Key:   []byte("event_version"),
				Value: []byte("1.0"),
			},
			{
				Key:   []byte("source_service"),
				Value: []byte("finance-service"),
			},
			{
				Key:   []byte("timestamp"),
				Value: []byte(event.Timestamp.Format(time.RFC3339)),
			},
		},
		Timestamp: event.Timestamp,
	}

	// Publish with retry logic
	var lastErr error
	for attempt := 1; attempt <= 3; attempt++ {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		partition, offset, err := kp.producer.SendMessage(message)
		if err != nil {
			lastErr = err
			log.Printf("Failed to publish financial record event (attempt %d/3): %v", attempt, err)

			// Wait before retry (exponential backoff)
			if attempt < 3 {
				backoff := time.Duration(attempt) * time.Second
				time.Sleep(backoff)
			}
			continue
		}

		log.Printf("Successfully published financial record event to topic %s, partition %d, offset %d",
			kp.topic, partition, offset)
		return nil
	}

	return fmt.Errorf("failed to publish financial record event after 3 attempts: %w", lastErr)
}

// PublishFinancialRecordBatch publishes multiple financial record events in a batch
func (kp *KafkaProducer) PublishFinancialRecordBatch(ctx context.Context, events []*models.FinancialRecordEvent) error {
	if len(events) == 0 {
		return nil
	}

	messages := make([]*sarama.ProducerMessage, 0, len(events))

	for _, event := range events {
		// Validate each event
		if err := event.Validate(); err != nil {
			return fmt.Errorf("invalid financial record event in batch: %w", err)
		}

		// Serialize the event to JSON
		eventJSON, err := event.ToJSON()
		if err != nil {
			return fmt.Errorf("failed to serialize financial record event in batch: %w", err)
		}

		message := &sarama.ProducerMessage{
			Topic: kp.topic,
			Key:   sarama.StringEncoder(event.OrderReference),
			Value: sarama.ByteEncoder(eventJSON),
			Headers: []sarama.RecordHeader{
				{
					Key:   []byte("event_type"),
					Value: []byte("financial.record.created"),
				},
				{
					Key:   []byte("event_version"),
					Value: []byte("1.0"),
				},
				{
					Key:   []byte("source_service"),
					Value: []byte("finance-service"),
				},
				{
					Key:   []byte("timestamp"),
					Value: []byte(event.Timestamp.Format(time.RFC3339)),
				},
			},
			Timestamp: event.Timestamp,
		}

		messages = append(messages, message)
	}

	// Send all messages in batch
	for _, message := range messages {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		_, _, err := kp.producer.SendMessage(message)
		if err != nil {
			return fmt.Errorf("failed to publish financial record event in batch: %w", err)
		}
	}

	log.Printf("Successfully published %d financial record events to topic %s", len(events), kp.topic)
	return nil
}

// Close closes the Kafka producer
func (kp *KafkaProducer) Close() error {
	log.Println("Closing Kafka producer...")
	return kp.producer.Close()
}

// NewProducerConfigFromConfig creates a ProducerConfig from the main config
func NewProducerConfigFromConfig(cfg *config.Config) ProducerConfig {
	return ProducerConfig{
		Brokers: cfg.Kafka.Brokers,
		Topic:   cfg.Kafka.ProducerTopic,
	}
}
