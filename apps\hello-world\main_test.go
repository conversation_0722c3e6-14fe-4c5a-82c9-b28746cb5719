package main

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestHealthHandler(t *testing.T) {
	// Create a request to pass to our handler
	req, err := http.NewRequest("GET", "/health", nil)
	require.NoError(t, err)

	// Create a ResponseRecorder to record the response
	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(healthHandler)

	// Call the handler with our request and recorder
	handler.ServeHTTP(rr, req)

	// Check the status code
	assert.Equal(t, http.StatusOK, rr.Code)

	// Check the content type
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"))

	// Parse the response body
	var response HealthResponse
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	require.NoError(t, err)

	// Check the response fields
	assert.Equal(t, "healthy", response.Status)
	assert.Equal(t, "hello-world", response.Service)
	assert.WithinDuration(t, time.Now(), response.Timestamp, time.Second)
}

func TestHelloHandler(t *testing.T) {
	// Create a request to pass to our handler
	req, err := http.NewRequest("GET", "/", nil)
	require.NoError(t, err)

	// Create a ResponseRecorder to record the response
	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(helloHandler)

	// Call the handler with our request and recorder
	handler.ServeHTTP(rr, req)

	// Check the status code
	assert.Equal(t, http.StatusOK, rr.Code)

	// Check the content type
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"))

	// Parse the response body
	var response HelloResponse
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	require.NoError(t, err)

	// Check the response fields
	assert.Equal(t, "Hello, World! Welcome to CDH Platform", response.Message)
	assert.Equal(t, "hello-world", response.Service)
	assert.WithinDuration(t, time.Now(), response.Timestamp, time.Second)
}

func TestHealthHandlerInvalidMethod(t *testing.T) {
	// Test that health endpoint works with different HTTP methods
	methods := []string{"POST", "PUT", "DELETE", "PATCH"}

	for _, method := range methods {
		req, err := http.NewRequest(method, "/health", nil)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		handler := http.HandlerFunc(healthHandler)

		handler.ServeHTTP(rr, req)

		// Health endpoint should respond to all methods (common for health checks)
		assert.Equal(t, http.StatusOK, rr.Code)
	}
}

func TestHelloHandlerInvalidMethod(t *testing.T) {
	// Test that hello endpoint works with different HTTP methods
	methods := []string{"POST", "PUT", "DELETE", "PATCH"}

	for _, method := range methods {
		req, err := http.NewRequest(method, "/", nil)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		handler := http.HandlerFunc(helloHandler)

		handler.ServeHTTP(rr, req)

		// Hello endpoint should respond to all methods
		assert.Equal(t, http.StatusOK, rr.Code)
	}
}

// TestServerIntegration tests the full server setup
func TestServerIntegration(t *testing.T) {
	// Create HTTP server mux
	mux := http.NewServeMux()
	mux.HandleFunc("/", helloHandler)
	mux.HandleFunc("/health", healthHandler)

	// Create test server
	server := httptest.NewServer(mux)
	defer server.Close()

	// Test health endpoint
	resp, err := http.Get(server.URL + "/health")
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)
	assert.Equal(t, "application/json", resp.Header.Get("Content-Type"))

	// Test hello endpoint
	resp, err = http.Get(server.URL + "/")
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)
	assert.Equal(t, "application/json", resp.Header.Get("Content-Type"))
}

// TestResponseStructures validates JSON response structures
func TestResponseStructures(t *testing.T) {
	t.Run("HealthResponse structure", func(t *testing.T) {
		response := HealthResponse{
			Status:    "healthy",
			Timestamp: time.Now(),
			Service:   "hello-world",
		}

		// Ensure JSON marshaling works correctly
		data, err := json.Marshal(response)
		require.NoError(t, err)

		// Ensure JSON unmarshaling works correctly
		var unmarshaled HealthResponse
		err = json.Unmarshal(data, &unmarshaled)
		require.NoError(t, err)

		assert.Equal(t, response.Status, unmarshaled.Status)
		assert.Equal(t, response.Service, unmarshaled.Service)
		assert.WithinDuration(t, response.Timestamp, unmarshaled.Timestamp, time.Millisecond)
	})

	t.Run("HelloResponse structure", func(t *testing.T) {
		response := HelloResponse{
			Message:   "Test message",
			Timestamp: time.Now(),
			Service:   "hello-world",
		}

		// Ensure JSON marshaling works correctly
		data, err := json.Marshal(response)
		require.NoError(t, err)

		// Ensure JSON unmarshaling works correctly
		var unmarshaled HelloResponse
		err = json.Unmarshal(data, &unmarshaled)
		require.NoError(t, err)

		assert.Equal(t, response.Message, unmarshaled.Message)
		assert.Equal(t, response.Service, unmarshaled.Service)
		assert.WithinDuration(t, response.Timestamp, unmarshaled.Timestamp, time.Millisecond)
	})
}
