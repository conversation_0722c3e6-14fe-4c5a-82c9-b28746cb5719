package handlers

import (
	"encoding/json"
	"net/http"
)

// HealthResponse represents the health check response
type HealthResponse struct {
	Status  string `json:"status"`
	Service string `json:"service"`
}

// HealthHandler handles health check requests
func HealthHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	
	response := HealthResponse{
		Status:  "ok",
		Service: "order-service",
	}

	json.NewEncoder(w).Encode(response)
}