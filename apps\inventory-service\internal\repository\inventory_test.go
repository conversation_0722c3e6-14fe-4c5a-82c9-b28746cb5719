package repository

import (
	"context"
	"database/sql"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/company/cdh/apps/inventory-service/internal/errors"
	"github.com/company/cdh/apps/inventory-service/models"
)

// MockInventoryRepository implements InventoryRepository for testing
type MockInventoryRepository struct {
	items  map[string]*models.Inventory
	nextID int
}

func NewMockInventoryRepository() *MockInventoryRepository {
	return &MockInventoryRepository{
		items:  make(map[string]*models.Inventory),
		nextID: 1,
	}
}

func (m *MockInventoryRepository) GetBySKU(ctx context.Context, sku string) (*models.Inventory, error) {
	item, exists := m.items[sku]
	if !exists {
		return nil, errors.ErrInventoryNotFound
	}
	// Return a copy to avoid mutation
	result := *item
	return &result, nil
}

func (m *MockInventoryRepository) Create(ctx context.Context, inventory *models.Inventory) error {
	if _, exists := m.items[inventory.SKU]; exists {
		return errors.NewDatabaseError("create inventory", sql.ErrNoRows)
	}

	// Simulate database behavior
	inventory.ID = m.nextID
	m.nextID++
	inventory.CalculateAvailableQuantity()

	// Store a copy
	item := *inventory
	m.items[inventory.SKU] = &item
	return nil
}

func (m *MockInventoryRepository) Update(ctx context.Context, inventory *models.Inventory) error {
	if _, exists := m.items[inventory.SKU]; !exists {
		return errors.ErrInventoryNotFound
	}

	inventory.CalculateAvailableQuantity()
	// Store a copy
	item := *inventory
	m.items[inventory.SKU] = &item
	return nil
}

func (m *MockInventoryRepository) ReserveStock(ctx context.Context, sku string, quantity int) error {
	if quantity <= 0 {
		return errors.ErrInvalidQuantity
	}

	item, exists := m.items[sku]
	if !exists {
		return errors.ErrInventoryNotFound
	}

	if item.StockQuantity-item.ReservedQuantity < quantity {
		return errors.ErrInsufficientStock
	}

	item.ReservedQuantity += quantity
	item.CalculateAvailableQuantity()
	return nil
}

func (m *MockInventoryRepository) ReleaseStock(ctx context.Context, sku string, quantity int) error {
	if quantity <= 0 {
		return errors.ErrInvalidQuantity
	}

	item, exists := m.items[sku]
	if !exists {
		return errors.ErrInventoryNotFound
	}

	item.ReservedQuantity -= quantity
	if item.ReservedQuantity < 0 {
		item.ReservedQuantity = 0
	}
	item.CalculateAvailableQuantity()
	return nil
}

func (m *MockInventoryRepository) DeductStock(ctx context.Context, sku string, quantity int) error {
	if quantity <= 0 {
		return errors.ErrInvalidQuantity
	}

	item, exists := m.items[sku]
	if !exists {
		return errors.ErrInventoryNotFound
	}

	if item.StockQuantity < quantity {
		return errors.ErrInsufficientStock
	}

	item.StockQuantity -= quantity
	if item.ReservedQuantity > quantity {
		item.ReservedQuantity -= quantity
	} else {
		item.ReservedQuantity = 0
	}
	item.CalculateAvailableQuantity()
	return nil
}

func (m *MockInventoryRepository) GetLowStockItems(ctx context.Context, threshold int) ([]*models.Inventory, error) {
	var lowStockItems []*models.Inventory

	for _, item := range m.items {
		if item.AvailableQuantity < threshold {
			// Return a copy
			itemCopy := *item
			lowStockItems = append(lowStockItems, &itemCopy)
		}
	}

	// Sort by available quantity (simple bubble sort for test)
	for i := 0; i < len(lowStockItems)-1; i++ {
		for j := 0; j < len(lowStockItems)-i-1; j++ {
			if lowStockItems[j].AvailableQuantity > lowStockItems[j+1].AvailableQuantity {
				lowStockItems[j], lowStockItems[j+1] = lowStockItems[j+1], lowStockItems[j]
			}
		}
	}

	return lowStockItems, nil
}

func TestInventoryRepository_GetBySKU(t *testing.T) {
	repo := NewMockInventoryRepository()
	ctx := context.Background()

	// Create test data
	inventory := &models.Inventory{
		SKU:              "LAPTOP-001",
		ProductName:      "Gaming Laptop",
		Description:      "High-end gaming laptop",
		StockQuantity:    100,
		ReservedQuantity: 20,
		UnitPrice:        999.99,
	}
	err := repo.Create(ctx, inventory)
	require.NoError(t, err)

	t.Run("Existing SKU", func(t *testing.T) {
		retrieved, err := repo.GetBySKU(ctx, "LAPTOP-001")
		require.NoError(t, err)
		assert.Equal(t, "LAPTOP-001", retrieved.SKU)
		assert.Equal(t, "Gaming Laptop", retrieved.ProductName)
		assert.Equal(t, 100, retrieved.StockQuantity)
		assert.Equal(t, 20, retrieved.ReservedQuantity)
		assert.Equal(t, 999.99, retrieved.UnitPrice)
		assert.Equal(t, 80, retrieved.AvailableQuantity)
	})

	t.Run("Non-existent SKU", func(t *testing.T) {
		_, err := repo.GetBySKU(ctx, "NON-EXISTENT")
		assert.Equal(t, errors.ErrInventoryNotFound, err)
	})
}

func TestInventoryRepository_Create(t *testing.T) {
	repo := NewMockInventoryRepository()
	ctx := context.Background()

	inventory := &models.Inventory{
		SKU:              "MOUSE-001",
		ProductName:      "Gaming Mouse",
		Description:      "RGB gaming mouse",
		StockQuantity:    50,
		ReservedQuantity: 0,
		UnitPrice:        79.99,
	}

	err := repo.Create(ctx, inventory)
	require.NoError(t, err)

	// Verify the item was created
	assert.NotZero(t, inventory.ID)

	// Verify we can retrieve it
	retrieved, err := repo.GetBySKU(ctx, "MOUSE-001")
	require.NoError(t, err)
	assert.Equal(t, inventory.SKU, retrieved.SKU)
	assert.Equal(t, inventory.ProductName, retrieved.ProductName)
	assert.Equal(t, 50, retrieved.AvailableQuantity)
}

func TestInventoryRepository_Update(t *testing.T) {
	repo := NewMockInventoryRepository()
	ctx := context.Background()

	// Create initial item
	inventory := &models.Inventory{
		SKU:              "KEYBOARD-001",
		ProductName:      "Gaming Keyboard",
		Description:      "Mechanical keyboard",
		StockQuantity:    30,
		ReservedQuantity: 5,
		UnitPrice:        149.99,
	}
	err := repo.Create(ctx, inventory)
	require.NoError(t, err)

	t.Run("Update existing item", func(t *testing.T) {
		inventory.ProductName = "Updated Gaming Keyboard"
		inventory.StockQuantity = 40
		inventory.UnitPrice = 159.99

		err := repo.Update(ctx, inventory)
		require.NoError(t, err)

		// Verify update
		updated, err := repo.GetBySKU(ctx, "KEYBOARD-001")
		require.NoError(t, err)
		assert.Equal(t, "Updated Gaming Keyboard", updated.ProductName)
		assert.Equal(t, 40, updated.StockQuantity)
		assert.Equal(t, 159.99, updated.UnitPrice)
		assert.Equal(t, 35, updated.AvailableQuantity) // 40 - 5
	})

	t.Run("Update non-existent item", func(t *testing.T) {
		nonExistent := &models.Inventory{
			SKU:         "NON-EXISTENT",
			ProductName: "Non-existent",
		}
		err := repo.Update(ctx, nonExistent)
		assert.Equal(t, errors.ErrInventoryNotFound, err)
	})
}

func TestInventoryRepository_ReserveStock(t *testing.T) {
	repo := NewMockInventoryRepository()
	ctx := context.Background()

	// Create test item
	inventory := &models.Inventory{
		SKU:              "MONITOR-001",
		ProductName:      "Gaming Monitor",
		StockQuantity:    20,
		ReservedQuantity: 0,
		UnitPrice:        299.99,
	}
	err := repo.Create(ctx, inventory)
	require.NoError(t, err)

	t.Run("Reserve available stock", func(t *testing.T) {
		err := repo.ReserveStock(ctx, "MONITOR-001", 5)
		require.NoError(t, err)

		// Verify reservation
		updated, err := repo.GetBySKU(ctx, "MONITOR-001")
		require.NoError(t, err)
		assert.Equal(t, 5, updated.ReservedQuantity)
		assert.Equal(t, 15, updated.AvailableQuantity)
	})

	t.Run("Reserve more stock", func(t *testing.T) {
		err := repo.ReserveStock(ctx, "MONITOR-001", 10)
		require.NoError(t, err)

		// Verify total reservation
		updated, err := repo.GetBySKU(ctx, "MONITOR-001")
		require.NoError(t, err)
		assert.Equal(t, 15, updated.ReservedQuantity)
		assert.Equal(t, 5, updated.AvailableQuantity)
	})

	t.Run("Reserve insufficient stock", func(t *testing.T) {
		err := repo.ReserveStock(ctx, "MONITOR-001", 10) // Only 5 available
		assert.Equal(t, errors.ErrInsufficientStock, err)
	})

	t.Run("Reserve invalid quantity", func(t *testing.T) {
		err := repo.ReserveStock(ctx, "MONITOR-001", -5)
		assert.Equal(t, errors.ErrInvalidQuantity, err)
	})

	t.Run("Reserve from non-existent item", func(t *testing.T) {
		err := repo.ReserveStock(ctx, "NON-EXISTENT", 5)
		assert.Equal(t, errors.ErrInventoryNotFound, err)
	})
}

func TestInventoryRepository_ReleaseStock(t *testing.T) {
	repo := NewMockInventoryRepository()
	ctx := context.Background()

	// Create test item with reserved stock
	inventory := &models.Inventory{
		SKU:              "HEADSET-001",
		ProductName:      "Gaming Headset",
		StockQuantity:    30,
		ReservedQuantity: 10,
		UnitPrice:        199.99,
	}
	err := repo.Create(ctx, inventory)
	require.NoError(t, err)

	t.Run("Release reserved stock", func(t *testing.T) {
		err := repo.ReleaseStock(ctx, "HEADSET-001", 5)
		require.NoError(t, err)

		// Verify release
		updated, err := repo.GetBySKU(ctx, "HEADSET-001")
		require.NoError(t, err)
		assert.Equal(t, 5, updated.ReservedQuantity)
		assert.Equal(t, 25, updated.AvailableQuantity)
	})

	t.Run("Release more than reserved", func(t *testing.T) {
		err := repo.ReleaseStock(ctx, "HEADSET-001", 10) // Only 5 reserved
		require.NoError(t, err)

		// Should not go below 0
		updated, err := repo.GetBySKU(ctx, "HEADSET-001")
		require.NoError(t, err)
		assert.Equal(t, 0, updated.ReservedQuantity)
		assert.Equal(t, 30, updated.AvailableQuantity)
	})

	t.Run("Release invalid quantity", func(t *testing.T) {
		err := repo.ReleaseStock(ctx, "HEADSET-001", -5)
		assert.Equal(t, errors.ErrInvalidQuantity, err)
	})

	t.Run("Release from non-existent item", func(t *testing.T) {
		err := repo.ReleaseStock(ctx, "NON-EXISTENT", 5)
		assert.Equal(t, errors.ErrInventoryNotFound, err)
	})
}

func TestInventoryRepository_DeductStock(t *testing.T) {
	repo := NewMockInventoryRepository()
	ctx := context.Background()

	// Create test item
	inventory := &models.Inventory{
		SKU:              "CHAIR-001",
		ProductName:      "Gaming Chair",
		StockQuantity:    15,
		ReservedQuantity: 5,
		UnitPrice:        399.99,
	}
	err := repo.Create(ctx, inventory)
	require.NoError(t, err)

	t.Run("Deduct available stock", func(t *testing.T) {
		err := repo.DeductStock(ctx, "CHAIR-001", 3)
		require.NoError(t, err)

		// Verify deduction
		updated, err := repo.GetBySKU(ctx, "CHAIR-001")
		require.NoError(t, err)
		assert.Equal(t, 12, updated.StockQuantity)
		assert.Equal(t, 2, updated.ReservedQuantity) // Should also reduce reserved
		assert.Equal(t, 10, updated.AvailableQuantity)
	})

	t.Run("Deduct insufficient stock", func(t *testing.T) {
		err := repo.DeductStock(ctx, "CHAIR-001", 15) // Only 12 available
		assert.Equal(t, errors.ErrInsufficientStock, err)
	})

	t.Run("Deduct invalid quantity", func(t *testing.T) {
		err := repo.DeductStock(ctx, "CHAIR-001", -5)
		assert.Equal(t, errors.ErrInvalidQuantity, err)
	})
}

func TestInventoryRepository_GetLowStockItems(t *testing.T) {
	repo := NewMockInventoryRepository()
	ctx := context.Background()

	// Create test items with different stock levels
	items := []*models.Inventory{
		{SKU: "LOW-001", ProductName: "Low Stock 1", StockQuantity: 2, ReservedQuantity: 0, UnitPrice: 10.00},
		{SKU: "LOW-002", ProductName: "Low Stock 2", StockQuantity: 8, ReservedQuantity: 6, UnitPrice: 20.00},       // Available: 2
		{SKU: "NORMAL-001", ProductName: "Normal Stock", StockQuantity: 50, ReservedQuantity: 10, UnitPrice: 30.00}, // Available: 40
		{SKU: "HIGH-001", ProductName: "High Stock", StockQuantity: 100, ReservedQuantity: 0, UnitPrice: 40.00},
	}

	for _, item := range items {
		err := repo.Create(ctx, item)
		require.NoError(t, err)
	}

	t.Run("Get items below threshold", func(t *testing.T) {
		lowStockItems, err := repo.GetLowStockItems(ctx, 5)
		require.NoError(t, err)

		assert.Len(t, lowStockItems, 2)

		// Should be ordered by available quantity ASC
		assert.Equal(t, "LOW-001", lowStockItems[0].SKU)
		assert.Equal(t, 2, lowStockItems[0].AvailableQuantity)
		assert.Equal(t, "LOW-002", lowStockItems[1].SKU)
		assert.Equal(t, 2, lowStockItems[1].AvailableQuantity)
	})

	t.Run("No items below high threshold", func(t *testing.T) {
		lowStockItems, err := repo.GetLowStockItems(ctx, 1)
		require.NoError(t, err)
		assert.Len(t, lowStockItems, 0)
	})
}
