package handlers

import (
	"encoding/json"
	"errors"
	"net/http"
	"strconv"
	"strings"

	orderErrors "github.com/company/cdh/apps/order-service/internal/errors"
	"github.com/company/cdh/apps/order-service/internal/response"
	"github.com/company/cdh/apps/order-service/models"
	"github.com/company/cdh/apps/order-service/services"
)

// OrderHandler holds dependencies for order operations
type OrderHandler struct {
	orderService services.OrderServiceInterface
}

// NewOrderHandler creates a new OrderHandler
func NewOrderHandler(orderService services.OrderServiceInterface) *OrderHandler {
	return &OrderHandler{
		orderService: orderService,
	}
}

// OrdersHandler handles POST /orders for creating orders
func (h *OrderHandler) OrdersHandler(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodPost:
		h.createOrder(w, r)
	default:
		response.WriteError(w, http.StatusMethodNotAllowed,
			errors.New("method not allowed"), "Only POST method is supported")
	}
}

// OrderByIDHandler handles GET /orders/{id} for retrieving orders
func (h *OrderHandler) OrderByIDHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		response.WriteError(w, http.StatusMethodNotAllowed,
			errors.New("method not allowed"), "Only GET method is supported")
		return
	}

	// Extract order ID from URL path
	path := strings.TrimPrefix(r.URL.Path, "/orders/")
	if path == "" {
		response.WriteError(w, http.StatusBadRequest,
			orderErrors.ErrInvalidOrderID, "Order ID is required in URL path")
		return
	}

	orderID, err := strconv.Atoi(path)
	if err != nil {
		response.WriteError(w, http.StatusBadRequest,
			orderErrors.ErrInvalidOrderID, "Order ID must be a valid integer")
		return
	}

	h.getOrder(w, r, orderID)
}

// createOrder handles order creation
func (h *OrderHandler) createOrder(w http.ResponseWriter, r *http.Request) {
	var req models.OrderRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		response.WriteError(w, http.StatusBadRequest,
			orderErrors.ErrInvalidJSON, "Request body must be valid JSON")
		return
	}

	// Validate request
	if err := req.Validate(); err != nil {
		response.WriteError(w, http.StatusBadRequest,
			orderErrors.ErrValidationFailed, err.Error())
		return
	}

	// Create order using service (includes event publishing)
	order, err := h.orderService.CreateOrder(r.Context(), &req)
	if err != nil {
		response.WriteError(w, http.StatusInternalServerError,
			orderErrors.ErrDatabaseOperation, "Failed to create order")
		return
	}

	response.WriteSuccess(w, http.StatusCreated, order, "Order created successfully")
}

// getOrder handles order retrieval
func (h *OrderHandler) getOrder(w http.ResponseWriter, r *http.Request, orderID int) {
	order, err := h.orderService.GetOrderByID(r.Context(), orderID)
	if err != nil {
		if errors.Is(err, orderErrors.ErrOrderNotFound) {
			response.WriteError(w, http.StatusNotFound,
				orderErrors.ErrOrderNotFound, "Order with specified ID does not exist")
		} else {
			response.WriteError(w, http.StatusInternalServerError,
				orderErrors.ErrDatabaseOperation, "Failed to retrieve order")
		}
		return
	}

	response.WriteSuccess(w, http.StatusOK, order, "Order retrieved successfully")
}
