package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"

	"github.com/company/cdh/apps/inventory-service/internal/config"
	"github.com/company/cdh/apps/inventory-service/models"
)

// DBInterface defines the interface for database operations needed by the server
type DBInterface interface {
	Ping() error
	Close() error
}

// RedisInterface defines the interface for Redis operations needed by the server
type RedisInterface interface {
	Ping(ctx context.Context) *redis.StatusCmd
	Close() error
}

// MockOrderEventProcessor implements OrderEventProcessor for testing
type MockOrderEventProcessor struct {
	shouldFail bool
}

func (m *MockOrderEventProcessor) ProcessOrderEvent(ctx context.Context, event *models.OrderCreatedEvent) error {
	if m.shouldFail {
		return fmt.Errorf("mock processor error")
	}
	return nil
}

func (m *MockOrderEventProcessor) ValidateOrderEvent(event *models.OrderCreatedEvent) error {
	if m.shouldFail {
		return fmt.Errorf("mock validation error")
	}
	return nil
}

// MockDB implements DBInterface for testing
type MockDB struct {
	shouldFail bool
}

func (m *MockDB) Ping() error {
	if m.shouldFail {
		return sql.ErrConnDone
	}
	return nil
}

func (m *MockDB) Close() error {
	return nil
}

// MockRedisClient implements RedisInterface for testing
type MockRedisClient struct {
	shouldFail bool
}

func (m *MockRedisClient) Ping(ctx context.Context) *redis.StatusCmd {
	cmd := redis.NewStatusCmd(ctx)
	if m.shouldFail {
		cmd.SetErr(redis.ErrClosed)
	} else {
		cmd.SetVal("PONG")
	}
	return cmd
}

func (m *MockRedisClient) Close() error {
	return nil
}

// TestServer wraps Server to allow dependency injection for testing
type TestServer struct {
	*Server
	mockDB    DBInterface
	mockRedis RedisInterface
}

func NewTestServer(cfg config.ServerConfig, db DBInterface, redisClient RedisInterface) *TestServer {
	// Create a real server but we'll override the dependencies
	realDB := &sql.DB{}          // This won't be used
	realRedis := &redis.Client{} // This won't be used
	mockProcessor := &MockOrderEventProcessor{}
	server := NewServer(cfg, realDB, realRedis, mockProcessor)

	return &TestServer{
		Server:    server,
		mockDB:    db,
		mockRedis: redisClient,
	}
}

// Override the health handler to use mock dependencies
func (ts *TestServer) healthHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	services := make(map[string]string)

	// Check database connection using mock
	if err := ts.mockDB.Ping(); err != nil {
		services["database"] = "unhealthy"
	} else {
		services["database"] = "healthy"
	}

	// Check Redis connection using mock
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	if err := ts.mockRedis.Ping(ctx).Err(); err != nil {
		services["redis"] = "unhealthy"
	} else {
		services["redis"] = "healthy"
	}

	// Determine overall status
	status := "healthy"
	for _, serviceStatus := range services {
		if serviceStatus == "unhealthy" {
			status = "unhealthy"
			break
		}
	}

	response := HealthResponse{
		Status:    status,
		Timestamp: time.Now(),
		Services:  services,
	}

	w.Header().Set("Content-Type", "application/json")
	if status == "unhealthy" {
		w.WriteHeader(http.StatusServiceUnavailable)
	}

	json.NewEncoder(w).Encode(response)
}

// Override the readiness handler to use mock dependencies
func (ts *TestServer) readinessHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Check if service is ready to accept traffic using mocks
	if err := ts.mockDB.Ping(); err != nil {
		http.Error(w, "Database not ready", http.StatusServiceUnavailable)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	if err := ts.mockRedis.Ping(ctx).Err(); err != nil {
		http.Error(w, "Redis not ready", http.StatusServiceUnavailable)
		return
	}

	w.WriteHeader(http.StatusOK)
	fmt.Fprint(w, "Ready")
}

func TestServer_healthHandler(t *testing.T) {
	cfg := config.ServerConfig{Port: "8081"}

	t.Run("All services healthy", func(t *testing.T) {
		mockDB := &MockDB{shouldFail: false}
		mockRedis := &MockRedisClient{shouldFail: false}

		server := NewTestServer(cfg, mockDB, mockRedis)

		req := httptest.NewRequest(http.MethodGet, "/health", nil)
		w := httptest.NewRecorder()

		server.healthHandler(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), `"status":"healthy"`)
		assert.Contains(t, w.Body.String(), `"database":"healthy"`)
		assert.Contains(t, w.Body.String(), `"redis":"healthy"`)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
	})

	t.Run("Database unhealthy", func(t *testing.T) {
		mockDB := &MockDB{shouldFail: true}
		mockRedis := &MockRedisClient{shouldFail: false}

		server := NewTestServer(cfg, mockDB, mockRedis)

		req := httptest.NewRequest(http.MethodGet, "/health", nil)
		w := httptest.NewRecorder()

		server.healthHandler(w, req)

		assert.Equal(t, http.StatusServiceUnavailable, w.Code)
		assert.Contains(t, w.Body.String(), `"status":"unhealthy"`)
		assert.Contains(t, w.Body.String(), `"database":"unhealthy"`)
		assert.Contains(t, w.Body.String(), `"redis":"healthy"`)
	})

	t.Run("Redis unhealthy", func(t *testing.T) {
		mockDB := &MockDB{shouldFail: false}
		mockRedis := &MockRedisClient{shouldFail: true}

		server := NewTestServer(cfg, mockDB, mockRedis)

		req := httptest.NewRequest(http.MethodGet, "/health", nil)
		w := httptest.NewRecorder()

		server.healthHandler(w, req)

		assert.Equal(t, http.StatusServiceUnavailable, w.Code)
		assert.Contains(t, w.Body.String(), `"status":"unhealthy"`)
		assert.Contains(t, w.Body.String(), `"database":"healthy"`)
		assert.Contains(t, w.Body.String(), `"redis":"unhealthy"`)
	})

	t.Run("Method not allowed", func(t *testing.T) {
		mockDB := &MockDB{shouldFail: false}
		mockRedis := &MockRedisClient{shouldFail: false}

		server := NewTestServer(cfg, mockDB, mockRedis)

		req := httptest.NewRequest(http.MethodPost, "/health", nil)
		w := httptest.NewRecorder()

		server.healthHandler(w, req)

		assert.Equal(t, http.StatusMethodNotAllowed, w.Code)
	})
}

func TestServer_readinessHandler(t *testing.T) {
	cfg := config.ServerConfig{Port: "8081"}

	t.Run("Service ready", func(t *testing.T) {
		mockDB := &MockDB{shouldFail: false}
		mockRedis := &MockRedisClient{shouldFail: false}

		server := NewTestServer(cfg, mockDB, mockRedis)

		req := httptest.NewRequest(http.MethodGet, "/health/ready", nil)
		w := httptest.NewRecorder()

		server.readinessHandler(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "Ready", w.Body.String())
	})

	t.Run("Database not ready", func(t *testing.T) {
		mockDB := &MockDB{shouldFail: true}
		mockRedis := &MockRedisClient{shouldFail: false}

		server := NewTestServer(cfg, mockDB, mockRedis)

		req := httptest.NewRequest(http.MethodGet, "/health/ready", nil)
		w := httptest.NewRecorder()

		server.readinessHandler(w, req)

		assert.Equal(t, http.StatusServiceUnavailable, w.Code)
		assert.Contains(t, w.Body.String(), "Database not ready")
	})

	t.Run("Redis not ready", func(t *testing.T) {
		mockDB := &MockDB{shouldFail: false}
		mockRedis := &MockRedisClient{shouldFail: true}

		server := NewTestServer(cfg, mockDB, mockRedis)

		req := httptest.NewRequest(http.MethodGet, "/health/ready", nil)
		w := httptest.NewRecorder()

		server.readinessHandler(w, req)

		assert.Equal(t, http.StatusServiceUnavailable, w.Code)
		assert.Contains(t, w.Body.String(), "Redis not ready")
	})

	t.Run("Method not allowed", func(t *testing.T) {
		mockDB := &MockDB{shouldFail: false}
		mockRedis := &MockRedisClient{shouldFail: false}

		server := NewTestServer(cfg, mockDB, mockRedis)

		req := httptest.NewRequest(http.MethodPost, "/health/ready", nil)
		w := httptest.NewRecorder()

		server.readinessHandler(w, req)

		assert.Equal(t, http.StatusMethodNotAllowed, w.Code)
	})
}

func TestServer_livenessHandler(t *testing.T) {
	server := &Server{}

	t.Run("Service alive", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/health/live", nil)
		w := httptest.NewRecorder()

		server.livenessHandler(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "Alive", w.Body.String())
	})

	t.Run("Method not allowed", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodPost, "/health/live", nil)
		w := httptest.NewRecorder()

		server.livenessHandler(w, req)

		assert.Equal(t, http.StatusMethodNotAllowed, w.Code)
	})
}

func TestNewTestServer(t *testing.T) {
	cfg := config.ServerConfig{Port: "8081"}
	mockDB := &MockDB{shouldFail: false}
	mockRedis := &MockRedisClient{shouldFail: false}

	server := NewTestServer(cfg, mockDB, mockRedis)

	assert.NotNil(t, server)
	assert.NotNil(t, server.Server)
	assert.Equal(t, ":8081", server.Server.server.Addr)
	assert.NotNil(t, server.Server.server.Handler)
}

func TestServer_StartAndShutdown(t *testing.T) {
	cfg := config.ServerConfig{Port: "0"} // Use port 0 for testing
	mockDB := &MockDB{shouldFail: false}
	mockRedis := &MockRedisClient{shouldFail: false}

	server := NewTestServer(cfg, mockDB, mockRedis)

	// Test that we can create and shutdown the server
	// Note: We don't actually start it to avoid port conflicts in tests
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	err := server.Shutdown(ctx)
	// Shutdown on a non-started server should not error
	assert.NoError(t, err)
}
