# 项目简报：中央数据中心 (CDH) 零售一体化平台

### 1. 执行摘要
本项目旨在设计并开发一个名为“中央数据中心 (CDH)”的云原生微服务平台。该平台的核心目标是整合公司现有的销售点 (POS)、仓库管理系统 (WMS) 和电子商务平台，创建一个统一、实时的“单一真实数据来源”。通过此平台，我们将解决数据孤岛问题，自动化关键业务流程，特别是实现与马来西亚税收局（LHDN）MyInvois 系统的强制性电子发票集成，并为未来的业务扩展奠定一个可扩展、高性能的基础。

### 2. 问题陈述
目前，公司的核心业务系统（POS、WMS、电商）相互独立，导致了以下几个关键痛点：
* **数据孤岛与不一致**：库存、销售和客户数据分散在不同系统中，导致数据不一致，需要大量人工核对与同步，效率低下且容易出错。
* **缺乏实时库存可见性**：无法实时了解全渠道的库存水平，这可能导致线上超卖或线下缺货，影响客户体验和销售机会。
* **财务流程繁琐**：订单和财务数据分离，财务对账和记录生成过程耗时耗力。
* **强制性合规风险**：马来西亚政府即将推行 LHDN MyInvois 电子发票系统。在当前架构下，我们无法高效、自动地满足这一强制性法规要求，面临合规风险。

### 3. 提议的解决方案
我们将采纳您提出的基于 Go 语言的、云原生的微服务架构。该方案将核心功能模块化，每个模块作为一个独立的服务运行，并通过 API 和消息队列进行通信。
* **核心服务**：一个 API 网关作为统一、安全的入口；一个用户与权限服务管理统一身份认证；一个库存服务作为全渠道库存的“单一真实来源”；一个订单服务集中处理所有订单；以及一个财务与税务服务自动化财务记录并与 LHDN MyInvois API 对接。
* **设计原则**：整个系统将遵循无状态、事件驱动和 API 优先的设计原则，确保高可用性、高响应速度和未来的可扩展性。

### 4. 目标用户
* **业务运营团队**：包括 POS 员工、仓库管理员、电商平台操作员。他们将受益于简化的流程和实时、准确的数据。
* **财务与管理团队**：他们需要准确、统一的业务数据来进行决策、财务报告和税务合规。
* **系统集成**：未来的 POS、WMS 和电商平台将作为系统的“用户”，通过 API 网关与 CDH 进行数据交互。

### 5. 目标与成功指标
* **业务目标**：
    * 在 LHDN 规定的截止日期前，实现与 MyInvois 电子发票系统的 100% 自动化合规对接。
    * 在平台上线后 3 个月内，将跨渠道库存差异率降低 90%。
    * 实现订单到财务记录的全流程自动化，将财务对账时间减少 80%。
* **关键绩效指标 (KPIs)**：
    * 电子发票上传 LHDN 的成功率 > 99.9%。
    * API 网关平均响应时间 < 200ms。
    * 系统正常运行时间 > 99.95%。

### 6. MVP (最小可行产品) 范围
为了尽快解决核心痛点并验证架构，MVP 将专注于以下核心功能：
* **包含功能 (In-Scope)**：
    1.  **API 网关**：实现基础的路由和安全认证功能。
    2.  **订单服务**：能够接收并处理来自至少一个核心渠道的订单。
    3.  **库存服务**：能够根据订单信息准确地扣减库存。
    4.  **财务与税务服务**：**必须**包含与 LHDN MyInvois API 的完整集成，实现电子发票的自动处理。
    5.  **用户服务**：提供基础的 API 访问令牌认证功能。
* **不包含功能 (Out-of-Scope)**：
    * 复杂的角色与权限管理系统（MVP 将使用基础角色）。
    * 高级数据分析和可视化仪表盘。
    * 会员、营销等非核心业务服务。

### 7. 技术考量 (已确认)
该项目将严格遵循已确认的技术选型：
* **语言**：Go
* **数据库**：为每个微服务提供独立的 PostgreSQL 数据库
* **缓存**：Redis
* **消息队列**：Kafka
* **容器化与编排**：Docker & Kubernetes (K8s)
* **API 网关**：Traefik
* **监控与日志**：Prometheus, Grafana, EFK Stack

### 8. 约束条件与基本假设
* **约束条件**:
    * 项目时间表受到 LHDN MyInvois 强制合规截止日期的限制。
    * 技术栈已根据确认列表固定。
* **基本假设**:
    * 未来要对接的 POS、WMS 和电商系统将提供稳定且文档齐全的 API。
    * 开发团队具备或能够获得 Go 和 Kubernetes 的必要技能。

### 9. 风险与开放性问题
* **主要风险**：与 LHDN MyInvois API 的集成复杂性可能超出预期。我们需要尽早获取其技术文档并进行原型测试。
* **开放性问题**：
    * LHDN 强制执行的确切截止日期是什么时候？
    * 未来系统连接到 CDH 的具体 API 规范是什么？