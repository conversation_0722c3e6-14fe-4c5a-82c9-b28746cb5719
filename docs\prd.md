# Central Data Hub (CDH) Product Requirements Document (PRD)

## 1. Goals and Background Context

### Goals
* **Compliance:** Achieve 100% automated compliance with the LHDN MyInvois system for e-invoicing.
* **Efficiency Improvement:** Automate the order-to-finance reconciliation process to significantly reduce manual work time.
* **Data Accuracy:** Consolidate omni-channel inventory data to eliminate data silos and provide a single, accurate source of truth for all systems.
* **System Performance:** Provide a high-performance, highly available unified API entry point for all integrated external systems (POS, WMS, e-commerce platforms).
* **Scalability:** Build a modular microservices foundation to support the future addition of new features (e.g., membership, marketing services).

### Background Context
This project aims to solve current issues such as data inconsistency and operational inefficiency caused by the separation of core business systems like POS, WMS, and e-commerce. By creating a Central Data Hub (CDH), we will establish a "single source of truth" for omni-channel data, thereby optimizing inventory management and simplifying financial processes.

The most urgent driver is to respond to the Malaysian government's mandatory implementation of the LHDN MyInvois e-invoicing policy. This project is not just a technological upgrade but a critical initiative to ensure the company's ongoing business compliance. This PRD will detail the functional and technical requirements for building this core platform.

### Change Log

| Date       | Version | Description      | Author   |
| :--------- | :------ | :--------------- | :------- |
| 2025-08-01 | 1.0     | Initial Draft    | John, PM |

## 2. Requirements (Revised)

### Functional Requirements (FR)
* **FR1**: The API Gateway must provide a unified, secure, and well-documented API entry point for all **future** external systems (POS, WMS, e-commerce, etc.).
* **FR2**: The system must be able to manage roles and permissions for different systems (users), e.g., a POS terminal can only call APIs related to orders and inventory queries.
* **FR3**: The Inventory Service must act as the single source of truth for omni-channel inventory, providing atomic APIs for querying, adding, deducting, and locking stock.
* **FR4**: The Order Service must be able to uniformly process orders from all channels and publish an "Order Created" event via the message queue upon successful order creation.
* **FR5**: The Inventory Service must subscribe to the "Order Created" event and automatically and asynchronously deduct the corresponding inventory based on the order content.
* **FR6**: The Finance Service must subscribe to the "Order Created" event and automatically and asynchronously generate preliminary financial entries based on the order content.
* **FR7**: The Finance & Tax Service must provide an **internal** interface capable of calling the LHDN MyInvois API to generate, upload, and validate e-invoices based on verified order information.
* **FR8**: The User & Permissions Service must provide functionalities for user registration, login, and API Key/JWT management.
* **FR9**: **All externally exposed APIs must have clear, accurate documentation with examples for use by future frontend and system developers.**

### Non-Functional Requirements (NFR)
* **NFR1**: **Performance**: All user-facing API response times should be under 500ms for 95% of requests, and the system must handle high concurrency during e-commerce sales peaks.
* **NFR2**: **Scalability**: Each microservice must be stateless and containerized (using Docker) to allow for independent horizontal scaling via Kubernetes.
* **NFR3**: **Reliability**: Communication between services should prioritize asynchronous message queues (e.g., Kafka) to achieve service decoupling and improve system fault tolerance and overall reliability.
* **NFR4**: **Data Isolation**: Each microservice must have its own independent database to ensure data model independence and service autonomy.
* **NFR5**: **Observability**: The system must provide comprehensive logging, monitoring, and alerting capabilities. Logs from all microservices should be centrally managed (e.g., using EFK), and key business metrics and system health should be monitored (e.g., using Prometheus & Grafana).
* **NFR6**: **Security**: The API Gateway must enforce HTTPS and provide basic authentication, authorization, rate limiting, and firewall functionalities. Sensitive data (like API keys, database passwords) must not be hardcoded.

## 3. User Interface Design Goals

### Overall UX Vision
* Future frontend applications should be **efficient, intuitive, and responsive**. The core design principle will be a **"simple but modern design that is understandable at a glance."** For high-frequency applications like POS and WMS, the focus is on simplifying workflows and reducing clicks; for the admin backend, the focus is on clear data presentation and easy-to-use management functions.

### Core Interaction Paradigms
* **Data-Driven**: The interface should reflect real-time data from the CDH (e.g., inventory, order status).
* **Task-Oriented**: Design highly optimized task interfaces for specific roles (e.g., cashier, warehouse manager).
* **Consistency**: All frontend applications developed based on the CDH should follow consistent patterns for displaying and interacting with core data (e.g., products, orders).

### Core Screens and Views (Conceptual Level)
* **POS Sales Terminal Interface**: For quickly creating orders.
* **WMS Inventory Management Dashboard**: For viewing real-time inventory and processing stock movements.
* **E-commerce Admin Backend**: For syncing orders and inventory.
* **CDH System Admin Backend**: For managing users, viewing system logs, and API call statuses.

### Accessibility
* **Target Standard**: WCAG AA. Ensure future frontend applications are usable by all users, including those with visual or motor impairments.

### Branding
* **Requirement**: No specific requirements at present. Future frontend applications can have independent brand styles based on their positioning, but a placeholder for the company logo is required.

### Target Devices and Platforms
* **Platform**: Cross-Platform.
* **Specific Requirements**:
    * **WMS and Admin Backends**: Designed primarily for desktop browsers, compatible with major browsers (Chrome, Firefox, Edge).
    * **POS System**: May need to be adapted for touchscreen devices or tablets.
    * All applications should consider usability on tablet devices.

## 4. Technical Assumptions

### Repository Structure
* **Monorepo**.

### Service Architecture
* **Microservices Architecture**.

### Testing Requirements
* **Unit + Integration Testing**.

### Additional Technical Assumptions
* **Language**: Go
* **Containerization**: Docker
* **Orchestration**: Kubernetes (K8s)
* **Database**: PostgreSQL (independent per service)
* **Cache**: Redis
* **Message Queue**: Kafka
* **API Gateway**: Traefik
* **Monitoring & Logging**: Prometheus, Grafana, EFK Stack

## 5. Epic List

* **Epic 1: Platform Foundation & Core Service Framework**
    * **Goal**: To build the basic framework of the project, including a CI/CD automated deployment pipeline using K8s, configuration of the API Gateway (Traefik), and launching the first core service (User & Permissions Service) to handle basic authentication.
* **Epic 2: Core Transactional Flow Implementation**
    * **Goal**: To develop the Order Service and Inventory Service, implementing the core business process of receiving orders via API and asynchronously updating inventory through the message queue (Kafka).
* **Epic 3: Financial Data Foundation & Accounting Platform Integration**
    * **Goal**: To develop the Finance Service for processing financial data from order events and establishing standardized financial event streams that enable seamless integration with future dedicated accounting platforms.
* **Epic 4: Platform Observability & Management**
    * **Goal**: To fully deploy and configure monitoring (Prometheus & Grafana) and logging (EFK) systems, and create a simple internal admin backend for viewing system status and managing basic configurations.

## 6. Epic Details

### Epic 1: Platform Foundation & Core Service Framework
**Epic Goal**: The goal of this epic is to build a solid, automated, and secure development and deployment foundation for the entire CDH platform. By the end of this epic, we will have a functional CI/CD pipeline, a configured API gateway, and a core user service capable of handling user registration and authentication. This will prove the viability of our technical architecture and pave the way for subsequent business feature development.

#### User Stories
**Story 1.1: Project Initialization & Repository Setup**
* **As a** developer, **I want** a standardized project structure using a Monorepo pattern, **so that** I can manage the code and dependencies for all microservices in a unified environment.
* **Acceptance Criteria**:
    1.  The Git repository has been created.
    2.  The repository contains a Monorepo directory structure (e.g., `apps/` and `packages/` folders) that aligns with our technology choices (Go, Kafka, Traefik).
    3.  A `README.md` file has been created in the root directory with a project overview.

**Story 1.2: Basic CI/CD Automated Deployment Pipeline**
* **As a** platform team member, **I want** a basic continuous integration/continuous deployment (CI/CD) pipeline, **so that** a "Hello World" application can be automatically built, tested, and deployed to the Kubernetes cluster after a code commit.
* **Acceptance Criteria**:
    1.  A CI/CD configuration file (e.g., `.github/workflows/deploy.yml`) has been created.
    2.  The pipeline is automatically triggered when code is pushed to the `main` branch.
    3.  The pipeline successfully deploys a simple application to K8s, which is accessible via a URL.

**Story 1.3: API Gateway (Traefik) Initial Deployment & Routing**
* **As a** platform team member, **I want** to deploy and configure Traefik in the Kubernetes cluster, **so that** it can act as the single entry point for all internal services and handle basic HTTP routing.
* **Acceptance Criteria**:
    1.  Traefik has been successfully deployed to the K8s cluster.
    2.  The Traefik dashboard is accessible.
    3.  A basic routing rule pointing to a "health check" endpoint is configured and working correctly.

**Story 1.4: User & Permissions Service Scaffolding**
* **As a** developer, **I want** to create the basic code structure for the User & Permissions service and implement a health check endpoint, **so that** we can deploy it via the CI/CD pipeline and verify that the service is accessible through the API Gateway.
* **Acceptance Criteria**:
    1.  A Go project structure has been created in the `apps/user-service` directory.
    2.  The service includes a `/health` endpoint that returns `{"status": "ok"}` when accessed.
    3.  A Dockerfile for the service has been created and can successfully build a Docker image.
    4.  After deployment, the service is successfully accessible via a path through the API Gateway (e.g., `https://api.yourdomain.com/user/health`).

**Story 1.5: Implement User Registration**
* **As a** future system developer, **I want** to call a user registration API endpoint, **so that** I can create a new user for future frontend applications or systems.
* **Acceptance Criteria**:
    1.  The User service provides a `POST /register` endpoint.
    2.  The endpoint accepts user information (e.g., username, password, email) and stores the user data with an encrypted password in the PostgreSQL database.
    3.  Upon successful user creation, the new user's ID is returned.
    4.  If the username or email already exists, an appropriate error message is returned.

**Story 1.6: Implement User Authentication (JWT)**
* **As a** future system user, **I want** to authenticate with a username and password to obtain an access token, **so that** I can use this token to access protected API resources.
* **Acceptance Criteria**:
    1.  The User service provides a `POST /login` endpoint.
    2.  The endpoint validates user credentials.
    3.  Upon successful validation, a JWT (JSON Web Token) containing the user ID and role is generated and returned to the client.
    4.  If credentials are incorrect, an authentication failure error message is returned.

**Story 1.7: Implement Basic Authentication Middleware**
* **As a** developer, **I want** an authentication middleware that can be integrated into the API Gateway or microservices, **so that** I can protect specific API endpoints, allowing access only to requests with a valid JWT.
* **Acceptance Criteria**:
    1.  The authentication middleware has been created.
    2.  When a protected endpoint is called, the middleware validates the JWT in the request header.
    3.  If the JWT is valid, the request is passed through to the target service.
    4.  If the JWT is invalid or missing, a 401 Unauthorized error is returned.

### Epic 2: Core Transactional Flow Implementation
**Epic Goal**: The goal of this epic is to build the CDH platform's capability to handle core business transactions. By the end of this epic, the system will be able to receive orders via an API, the order information will be reliably transmitted via Kafka to the inventory service, and inventory levels will be updated accurately and in real-time. This will validate the core pattern of our event-driven architecture and establish the data foundation for the subsequent financial integration.

#### User Stories
**Story 2.1: Order Service Scaffolding & API Definition**
* **As a** developer, **I want** to create the basic code structure for the Order service and define the API interfaces for creating and querying orders, **so that** future systems can begin interacting with the Order service.
* **Acceptance Criteria**:
    1.  A Go project structure and its Dockerfile have been created in the `apps/order-service` directory.
    2.  The service provides a `POST /orders` endpoint for creating orders and a `GET /orders/{id}` endpoint for querying order details.
    3.  The order data model (including order number, product info, quantity, price, etc.) has been defined in the code.
    4.  The service is connected to its independent PostgreSQL database.

**Story 2.2: Implement Order Creation & Event Publishing**
* **As a** future system developer, **I want** the system to publish an "Order Created" event after successfully creating an order via the `POST /orders` endpoint, **so that** other downstream services interested in this event (like the Inventory service) can receive and process it.
* **Acceptance Criteria**:
    1.  When `POST /orders` successfully processes a request, the order data is correctly stored in the Order service's database.
    2.  After the order is successfully stored, an event containing the complete order information is serialized (e.g., in JSON format) and published to the `orders.created` topic in Kafka.
    3.  Upon successful API call, the created order information and order ID are returned.

**Story 2.3: Inventory Service Scaffolding & Kafka Consumer**
* **As a** developer, **I want** to create the basic code structure for the Inventory service and implement a Kafka consumer to listen for order events, **so that** the Inventory service can receive and prepare to process messages from the Order service.
* **Acceptance Criteria**:
    1.  A Go project structure and its Dockerfile have been created in the `apps/inventory-service` directory.
    2.  The service is connected to its independent PostgreSQL database, and the inventory data model (e.g., SKU, stock quantity, reserved quantity) has been defined.
    3.  A Kafka consumer has been implemented in the service, subscribing to the `orders.created` topic.
    4.  When a new message arrives, the service can successfully receive it and print its content to the logs.

**Story 2.4: Implement Inventory Deduction based on Order Events**
* **As an** inventory manager, **I want** the system to automatically deduct the corresponding quantity from the total stock after each successful sale, **so that** the inventory levels seen across all channels are accurate and in real-time.
* **Acceptance Criteria**:
    1.  The Inventory service's Kafka consumer can successfully parse the product SKUs and quantities from the "Order Created" event.
    2.  The service finds the corresponding products in the inventory database and deducts the stock quantity based on the parsed information.
    3.  The inventory update operation is atomic to ensure data consistency.
    4.  If a product is out of stock, a failure event should be logged or sent to a specific Kafka topic for subsequent processing.

**Story 2.5: Provide Inventory Query API**
* **As a** future POS or e-commerce system developer, **I want** to call an API to query the current stock level of one or more products, **so that** I can display accurate inventory information to the user before they place an order, preventing overselling.
* **Acceptance Criteria**:
    1.  The Inventory service provides a `GET /inventory?sku={sku1},{sku2}` API endpoint.
    2.  The endpoint can return the real-time stock quantity for the specified product SKUs.
    3.  The API response is fast, using Redis as a query cache for optimization.

### Epic 3: Financial Data Foundation & Accounting Platform Integration
**Epic Goal**: The goal of this epic is to establish CDH's financial data processing capabilities and create standardized financial event streams that prepare for seamless integration with future dedicated accounting platforms. By the end of this epic, the platform will be able to listen for order events, automatically generate standardized financial records, and publish financial events in formats suitable for consumption by external accounting systems. This approach ensures clean separation of concerns where CDH focuses on transactional data processing while specialized accounting platforms handle tax compliance and e-invoicing.

#### User Stories
**Story 3.1: Finance Service Scaffolding & Basic Financial Recording**
* **As a** developer, **I want** to create the basic code structure for the Finance service and enable it to consume order events from Kafka to generate standardized financial records, **so that** the Finance service can provide clean financial data for future accounting platform integration.
* **Acceptance Criteria**:
    1.  A Go project structure and its Dockerfile have been created in the `apps/finance-service` directory.
    2.  The service is connected to its independent PostgreSQL database, and data models for financial entries have been defined.
    3.  A Kafka consumer has been implemented in the service, subscribing to the `orders.created` topic.
    4.  When a new order event arrives, the service can successfully receive it and generate basic financial records (revenue, tax amounts, etc.).

**Story 3.2: Financial Event Publishing & Data Standardization**
* **As a** future accounting platform, **I want** to receive standardized financial events from CDH in a consistent format, **so that** I can process financial records and handle tax compliance without needing to understand CDH's internal data structures.
* **Acceptance Criteria**:
    1.  The Finance service publishes standardized financial events to a `financial.records.created` Kafka topic after processing order events.
    2.  Financial events include standardized fields: transaction_id, order_reference, revenue_amount, tax_amount, currency, timestamp, and metadata.
    3.  The event format follows a documented schema that external accounting systems can easily consume.
    4.  The system can handle orders with different payment methods and tax-inclusive/exclusive pricing in the standardized format.

**Story 3.3: Accounting Platform Integration Interface**
* **As an** accounting platform developer, **I want** a well-defined API interface to retrieve financial data from CDH and send back processing status, **so that** I can integrate my accounting system with CDH without tight coupling.
* **Acceptance Criteria**:
    1.  The Finance service provides REST API endpoints for external accounting platforms to query financial records.
    2.  The API supports filtering by date range, order reference, and transaction status.
    3.  The service can receive status updates from accounting platforms (e.g., "processed", "requires_attention", "completed").
    4.  All API communications are secured with proper authentication and authorization mechanisms.

**Story 3.4: Financial Data Validation & Quality Assurance**
* **As a** finance manager, **I want** the system to validate financial data integrity and provide audit trails, **so that** I can ensure accurate financial reporting and maintain compliance readiness.
* **Acceptance Criteria**:
    1.  The Finance service validates that all financial records have complete required fields before publishing events.
    2.  The system maintains an audit trail of all financial record changes and event publications.
    3.  Data validation rules ensure consistency between order amounts and generated financial records.
    4.  The service provides reporting endpoints for financial data quality metrics and validation status.

### Epic 4: Platform Observability & Management
**Epic Goal**: The goal of this epic is to establish comprehensive "observability" capabilities for the CDH platform and provide basic management functionalities. By the end of this epic, we will have a centralized logging system to track issues, a real-time monitoring dashboard to observe system health, and a simple internal admin backend to manage users and view key configurations. This will ensure our operations team can easily monitor, maintain, and manage the entire platform, guaranteeing its long-term stability.

#### User Stories
**Story 4.1: Deploy Centralized Logging System (EFK Stack)**
* **As an** operations engineer, **I want** a system that centrally collects, stores, and queries the logs of all microservices (User, Order, Inventory, Finance), **so that** I can quickly track, locate, and diagnose failures when issues occur.
* **Acceptance Criteria**:
    1.  Elasticsearch, Fluentd, and Kibana (EFK) have been successfully deployed to the Kubernetes cluster.
    2.  All microservice logs (stdout/stderr) are automatically collected by Fluentd and sent to Elasticsearch.
    3.  Developers can query all logs via the Kibana web interface based on criteria like service name, time, and keywords.

**Story 4.2: Deploy Monitoring & Alerting System (Prometheus & Grafana)**
* **As an** operations engineer, **I want** a system that monitors key performance indicators (KPIs) of all microservices in real-time, **so that** I can intuitively understand the system's health and receive alerts before problems occur.
* **Acceptance Criteria**:
    1.  Prometheus and Grafana have been successfully deployed to the Kubernetes cluster.
    2.  Each Go microservice exposes a `/metrics` endpoint providing key metrics (e.g., API request latency, error rate, memory usage).
    3.  Prometheus can automatically scrape these metrics.
    4.  At least one basic dashboard has been created in Grafana, displaying the core health metrics of all services.
    5.  Basic alert rules have been set for critical metrics (e.g., API error rate > 5%).

**Story 4.3: Create Internal Admin Backend Framework**
* **As a** system administrator, **I want** a basic, password-protected web application to serve as the internal admin backend, **so that** I have a unified interface to perform administrative tasks.
* **Acceptance Criteria**:
    1.  A simple frontend application project (e.g., using React or Vue) has been created.
    2.  The application includes a login page, allowing administrators to log in using an account with an "admin" role created in the User service.
    3.  The basic layout of the application (e.g., side navigation bar, main content area) has been implemented.

**Story 4.4: Implement User Management in Admin Backend**
* **As a** system administrator, **I want** to view a list of all users in the admin backend and be able to create new users and assign roles, **so that** I can conveniently manage platform users and permissions without directly operating the database.
* **Acceptance Criteria**:
    1.  There is a "User Management" page in the admin backend.
    2.  The page displays a list of all registered users by calling the User service's API.
    3.  The page includes a form that can be used to create new users and assign them roles (e.g., "Admin," "System Integration Account").

## 7. Next Steps
### Handoff to UX Expert
> "Hello Sally (UX Expert), please review this PRD, especially the section on the internal admin backend mentioned in Epic 4. Based on the 'simple but modern' design principle, please create a basic UI/UX specification (`front-end-spec.md`) for this backend, focusing on the layout and interaction flow of the user management page."

### Handoff to Architect
> "Hello Winston (Architect), this PRD has been finalized from the product side. Please start creating the detailed system architecture document (`architecture.md`) based on all the functional requirements, non-functional requirements, technical assumptions, and the complete list of epics and stories defined in this document. Please ensure the architectural design can support every planned story and adheres to the confirmed technology choices."