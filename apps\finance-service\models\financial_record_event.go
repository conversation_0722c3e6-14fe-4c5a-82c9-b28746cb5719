package models

import (
	"encoding/json"
	"errors"
	"time"
)

// FinancialRecordEvent represents a standardized financial event for external consumption
type FinancialRecordEvent struct {
	TransactionID   string                 `json:"transaction_id"`
	OrderReference  string                 `json:"order_reference"`
	RevenueAmount   float64                `json:"revenue_amount"`
	TaxAmount       float64                `json:"tax_amount"`
	Currency        string                 `json:"currency"`
	Timestamp       time.Time              `json:"timestamp"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// Validate validates the financial record event data
func (fre *FinancialRecordEvent) Validate() error {
	if fre.TransactionID == "" {
		return errors.New("transaction_id is required")
	}
	if fre.OrderReference == "" {
		return errors.New("order_reference is required")
	}
	if fre.RevenueAmount < 0 {
		return errors.New("revenue_amount cannot be negative")
	}
	if fre.TaxAmount < 0 {
		return errors.New("tax_amount cannot be negative")
	}
	if fre.Currency == "" {
		return errors.New("currency is required")
	}
	if fre.Timestamp.IsZero() {
		return errors.New("timestamp is required")
	}
	return nil
}

// ToJSON serializes the financial record event to JSON
func (fre *FinancialRecordEvent) ToJSON() ([]byte, error) {
	return json.Marshal(fre)
}

// FromJSON deserializes JSON data into a financial record event
func FromJSON(data []byte) (*FinancialRecordEvent, error) {
	var event FinancialRecordEvent
	if err := json.Unmarshal(data, &event); err != nil {
		return nil, err
	}
	
	if err := event.Validate(); err != nil {
		return nil, err
	}
	
	return &event, nil
}

// CalculateTotalAmount returns the total amount (revenue + tax)
func (fre *FinancialRecordEvent) CalculateTotalAmount() float64 {
	return fre.RevenueAmount + fre.TaxAmount
}

// NewFinancialRecordEventFromEntry creates a FinancialRecordEvent from a FinancialEntry
func NewFinancialRecordEventFromEntry(entry *FinancialEntry) *FinancialRecordEvent {
	metadata := map[string]interface{}{
		"payment_method":    entry.PaymentMethod,
		"transaction_type":  entry.TransactionType,
		"description":       entry.Description,
		"source_service":    "finance-service",
		"event_version":     "1.0",
	}

	return &FinancialRecordEvent{
		TransactionID:  entry.TransactionID,
		OrderReference: entry.OrderID,
		RevenueAmount:  entry.RevenueAmount,
		TaxAmount:      entry.TaxAmount,
		Currency:       entry.Currency,
		Timestamp:      entry.CreatedAt,
		Metadata:       metadata,
	}
}
