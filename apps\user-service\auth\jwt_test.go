package auth

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewJWTConfig(t *testing.T) {
	tests := []struct {
		name               string
		secretKey          string
		expirationHours    string
		expectedSecretKey  string
		expectedExpiration time.Duration
	}{
		{
			name:               "Default values when env vars not set",
			secretKey:          "",
			expirationHours:    "",
			expectedSecretKey:  "default-secret-key-change-in-production",
			expectedExpiration: 24 * time.Hour,
		},
		{
			name:               "Custom values from env vars",
			secretKey:          "custom-secret-key",
			expirationHours:    "48",
			expectedSecretKey:  "custom-secret-key",
			expectedExpiration: 48 * time.Hour,
		},
		{
			name:               "Invalid expiration hours defaults to 24",
			secretKey:          "test-key",
			expirationHours:    "invalid",
			expectedSecretKey:  "test-key",
			expectedExpiration: 24 * time.Hour,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variables
			if tt.secretKey != "" {
				os.Setenv("JWT_SECRET_KEY", tt.secretKey)
			} else {
				os.Unsetenv("JWT_SECRET_KEY")
			}
			
			if tt.expirationHours != "" {
				os.Setenv("JWT_EXPIRATION_HOURS", tt.expirationHours)
			} else {
				os.Unsetenv("JWT_EXPIRATION_HOURS")
			}

			config := NewJWTConfig()

			assert.Equal(t, tt.expectedSecretKey, config.SecretKey)
			assert.Equal(t, tt.expectedExpiration, config.ExpirationTime)

			// Cleanup
			os.Unsetenv("JWT_SECRET_KEY")
			os.Unsetenv("JWT_EXPIRATION_HOURS")
		})
	}
}

func TestGenerateToken(t *testing.T) {
	config := &JWTConfig{
		SecretKey:      "test-secret-key",
		ExpirationTime: 1 * time.Hour,
	}

	userID := 123
	role := "user"

	token, err := config.GenerateToken(userID, role)

	require.NoError(t, err)
	assert.NotEmpty(t, token)
	
	// Verify token can be validated
	claims, err := config.ValidateToken(token)
	require.NoError(t, err)
	assert.Equal(t, userID, claims.UserID)
	assert.Equal(t, role, claims.Role)
	assert.Equal(t, "cdh-user-service", claims.Issuer)
}

func TestValidateToken(t *testing.T) {
	config := &JWTConfig{
		SecretKey:      "test-secret-key",
		ExpirationTime: 1 * time.Hour,
	}

	userID := 456
	role := "admin"

	// Generate a valid token
	token, err := config.GenerateToken(userID, role)
	require.NoError(t, err)

	// Test valid token
	claims, err := config.ValidateToken(token)
	require.NoError(t, err)
	assert.Equal(t, userID, claims.UserID)
	assert.Equal(t, role, claims.Role)
	assert.True(t, claims.ExpiresAt.After(time.Now()))
}

func TestValidateToken_InvalidToken(t *testing.T) {
	config := &JWTConfig{
		SecretKey:      "test-secret-key",
		ExpirationTime: 1 * time.Hour,
	}

	tests := []struct {
		name  string
		token string
	}{
		{
			name:  "Empty token",
			token: "",
		},
		{
			name:  "Invalid token format",
			token: "invalid.token.format",
		},
		{
			name:  "Malformed token",
			token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			claims, err := config.ValidateToken(tt.token)
			assert.Error(t, err)
			assert.Nil(t, claims)
		})
	}
}

func TestValidateToken_WrongSecret(t *testing.T) {
	config1 := &JWTConfig{
		SecretKey:      "secret-key-1",
		ExpirationTime: 1 * time.Hour,
	}
	
	config2 := &JWTConfig{
		SecretKey:      "secret-key-2",
		ExpirationTime: 1 * time.Hour,
	}

	// Generate token with first config
	token, err := config1.GenerateToken(123, "user")
	require.NoError(t, err)

	// Try to validate with second config (different secret)
	claims, err := config2.ValidateToken(token)
	assert.Error(t, err)
	assert.Nil(t, claims)
}

func TestTokenExpiration(t *testing.T) {
	config := &JWTConfig{
		SecretKey:      "test-secret-key",
		ExpirationTime: 1 * time.Millisecond, // Very short expiration for testing
	}

	token, err := config.GenerateToken(123, "user")
	require.NoError(t, err)

	// Wait for token to expire
	time.Sleep(2 * time.Millisecond)

	// Token should be expired
	claims, err := config.ValidateToken(token)
	assert.Error(t, err)
	assert.Nil(t, claims)
}
