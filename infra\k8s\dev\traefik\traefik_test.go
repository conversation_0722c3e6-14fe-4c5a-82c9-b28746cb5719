package traefik_test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestTraefikDeploymentHealth tests that Traefik is deployed and healthy
func TestTraefikDeploymentHealth(t *testing.T) {
	// This test assumes kubectl port-forward is running on localhost:8080 for Traefik dashboard
	// Run: kubectl port-forward -n cdh-dev svc/traefik-dashboard 8080:8080

	t.Run("Traefik Dashboard Accessibility", func(t *testing.T) {
		client := &http.Client{Timeout: 10 * time.Second}

		// Test dashboard endpoint
		resp, err := client.Get("http://localhost:8080/")
		if err != nil {
			t.Skip("Skipping test - Traefik dashboard not accessible. Run: kubectl port-forward -n cdh-dev svc/traefik-dashboard 8080:8080")
			return
		}
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode, "Traefik dashboard should be accessible")
	})

	t.Run("Traefik API Health", func(t *testing.T) {
		client := &http.Client{Timeout: 10 * time.Second}

		// Test ping endpoint
		resp, err := client.Get("http://localhost:8080/ping")
		if err != nil {
			t.Skip("Skipping test - Traefik API not accessible")
			return
		}
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode, "Traefik ping endpoint should return 200")
	})
}

// TestTraefikRouting tests that Traefik routing is working correctly
func TestTraefikRouting(t *testing.T) {
	// This test assumes kubectl port-forward is running on localhost:8082 for Traefik web traffic
	// Run: kubectl port-forward -n cdh-dev svc/traefik 8082:80

	client := &http.Client{Timeout: 10 * time.Second}

	testCases := []struct {
		name     string
		path     string
		expected int
	}{
		{"Health Check Root", "/health", http.StatusOK},
		{"Hello World Root", "/hello-world", http.StatusOK},
		{"Hello World Health", "/hello-world/health", http.StatusOK},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			url := fmt.Sprintf("http://localhost:8082%s", tc.path)
			resp, err := client.Get(url)
			if err != nil {
				t.Skipf("Skipping test - Traefik routing not accessible. Run: kubectl port-forward -n cdh-dev svc/traefik 8082:80")
				return
			}
			defer resp.Body.Close()

			assert.Equal(t, tc.expected, resp.StatusCode,
				"Route %s should return status %d", tc.path, tc.expected)

			// Verify content type for successful responses
			if resp.StatusCode == http.StatusOK {
				contentType := resp.Header.Get("Content-Type")
				assert.Equal(t, "application/json", contentType,
					"Response should be JSON for route %s", tc.path)
			}
		})
	}
}

// TestTraefikServiceDiscovery tests that Traefik can discover services
func TestTraefikServiceDiscovery(t *testing.T) {
	// This test checks if Traefik has discovered the hello-world service
	client := &http.Client{Timeout: 10 * time.Second}

	resp, err := client.Get("http://localhost:8080/api/http/services")
	if err != nil {
		t.Skip("Skipping test - Traefik API not accessible")
		return
	}
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode, "Services API should be accessible")

	// Note: In a more complete test, we would parse the JSON response
	// and verify that hello-world service is discovered
}

// TestTraefikConfiguration tests Traefik configuration endpoints
func TestTraefikConfiguration(t *testing.T) {
	client := &http.Client{Timeout: 10 * time.Second}

	endpoints := []string{
		"/api/http/routers",
		"/api/http/services",
		"/api/http/middlewares",
		"/api/overview",
	}

	for _, endpoint := range endpoints {
		t.Run(fmt.Sprintf("API Endpoint %s", endpoint), func(t *testing.T) {
			url := fmt.Sprintf("http://localhost:8080%s", endpoint)
			resp, err := client.Get(url)
			if err != nil {
				t.Skip("Skipping test - Traefik API not accessible")
				return
			}
			defer resp.Body.Close()

			assert.Equal(t, http.StatusOK, resp.StatusCode,
				"API endpoint %s should be accessible", endpoint)
		})
	}
}

// TestTraefikMetrics tests that Traefik metrics are available
func TestTraefikMetrics(t *testing.T) {
	client := &http.Client{Timeout: 10 * time.Second}

	resp, err := client.Get("http://localhost:8080/metrics")
	if err != nil {
		t.Skip("Skipping test - Traefik metrics not accessible")
		return
	}
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode, "Metrics endpoint should be accessible")

	// Verify it's Prometheus format (should contain "# HELP" comments)
	// In a more complete test, we would parse the response body
}

// Helper function to check if a service is available (for setup validation)
func isServiceAvailable(url string) bool {
	client := &http.Client{Timeout: 5 * time.Second}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return false
	}

	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}
