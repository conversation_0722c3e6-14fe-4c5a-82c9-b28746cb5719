package events

import (
	"testing"
	"time"

	"github.com/company/cdh/apps/order-service/models"
	"github.com/stretchr/testify/assert"
)

func TestNewOrderCreatedEvent(t *testing.T) {
	// Create a test order
	order := &models.Order{
		ID:          123,
		OrderNumber: "ORD-123456",
		ProductInfo: "Test Product",
		Quantity:    2,
		Price:       99.99,
		Status:      models.OrderStatusPending,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Create event from order
	event := NewOrderCreatedEvent(order)

	// Assertions
	assert.NotEmpty(t, event.EventID)
	assert.Equal(t, "order.created", event.EventType)
	assert.NotZero(t, event.Timestamp)
	assert.Equal(t, order.ID, event.OrderID)
	assert.Equal(t, order.OrderNumber, event.OrderNumber)
	assert.Equal(t, order.ProductInfo, event.ProductInfo)
	assert.Equal(t, order.Quantity, event.Quantity)
	assert.Equal(t, order.Price, event.Price)
	assert.Equal(t, string(order.Status), event.Status)
	assert.Equal(t, order.CreatedAt, event.CreatedAt)
}

func TestGenerateEventID(t *testing.T) {
	// Generate multiple event IDs
	id1 := generateEventID()
	time.Sleep(1 * time.Millisecond) // Ensure different timestamps
	id2 := generateEventID()

	// Assertions
	assert.NotEmpty(t, id1)
	assert.NotEmpty(t, id2)
	assert.NotEqual(t, id1, id2)
	assert.Contains(t, id1, "evt_")
	assert.Contains(t, id2, "evt_")
}

func TestOrderCreatedEventSerialization(t *testing.T) {
	// Create a test order
	order := &models.Order{
		ID:          456,
		OrderNumber: "ORD-789012",
		ProductInfo: "Another Product",
		Quantity:    1,
		Price:       49.99,
		Status:      models.OrderStatusConfirmed,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Create event
	event := NewOrderCreatedEvent(order)

	// Test that all required fields are present for JSON serialization
	assert.NotEmpty(t, event.EventID)
	assert.NotEmpty(t, event.EventType)
	assert.NotZero(t, event.Timestamp)
	assert.NotZero(t, event.OrderID)
	assert.NotEmpty(t, event.OrderNumber)
	assert.NotEmpty(t, event.ProductInfo)
	assert.Greater(t, event.Quantity, 0)
	assert.Greater(t, event.Price, 0.0)
	assert.NotEmpty(t, event.Status)
	assert.NotZero(t, event.CreatedAt)
}
