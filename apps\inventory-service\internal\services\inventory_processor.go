package services

import (
	"context"
	"database/sql"
	"fmt"
	"log"

	"github.com/company/cdh/apps/inventory-service/internal/errors"
	"github.com/company/cdh/apps/inventory-service/internal/producer"
	"github.com/company/cdh/apps/inventory-service/internal/repository"
	"github.com/company/cdh/apps/inventory-service/models"
)

// OrderEventProcessor defines the interface for processing order events
type OrderEventProcessor interface {
	ProcessOrderEvent(ctx context.Context, event *models.OrderCreatedEvent) error
	ValidateOrderEvent(event *models.OrderCreatedEvent) error
}

// InventoryProcessor handles inventory operations for order events
type InventoryProcessor struct {
	repo     repository.InventoryRepository
	db       *sql.DB
	producer producer.InventoryFailureProducer
}

// NewInventoryProcessor creates a new inventory processor
func NewInventoryProcessor(repo repository.InventoryRepository, db *sql.DB, producer producer.InventoryFailureProducer) *InventoryProcessor {
	return &InventoryProcessor{
		repo:     repo,
		db:       db,
		producer: producer,
	}
}

// ProcessOrderEvent processes an order created event and deducts inventory
func (ip *InventoryProcessor) ProcessOrderEvent(ctx context.Context, event *models.OrderCreatedEvent) error {
	log.Printf("Processing order event: OrderID=%s, Items=%d", event.OrderID, len(event.Items))

	// Start a database transaction for atomic operations (if database is available)
	var tx *sql.Tx
	if ip.db != nil {
		var err error
		tx, err = ip.db.BeginTx(ctx, nil)
		if err != nil {
			return errors.NewDatabaseError("begin transaction", err)
		}
		defer tx.Rollback() // Will be ignored if tx.Commit() succeeds
	}

	// First pass: Validate all items and check stock availability
	for i, item := range event.Items {
		log.Printf("Validating item %d: SKU=%s, Quantity=%d", i+1, item.SKU, item.Quantity)

		// Validate item fields
		if err := item.Validate(); err != nil {
			log.Printf("Invalid item at index %d: %v", i, err)
			return errors.NewValidationError("order_item", fmt.Sprintf("Invalid item at index %d: %v", i, err))
		}

		// Check if inventory exists and has sufficient stock
		inventory, err := ip.repo.GetBySKU(ctx, item.SKU)
		if err != nil {
			if err == errors.ErrInventoryNotFound {
				log.Printf("Product not found: SKU=%s", item.SKU)
				// Publish failure event for product not found
				if ip.producer != nil {
					if publishErr := ip.producer.PublishInventoryFailure(event.OrderID, item.SKU, item.Quantity, 0, "product_not_found"); publishErr != nil {
						log.Printf("Failed to publish failure event for product not found: %v", publishErr)
					}
				}
				return errors.NewValidationError("sku", fmt.Sprintf("Product not found: %s", item.SKU))
			}
			return err
		}

		// Check if sufficient stock is available
		if inventory.AvailableQuantity < item.Quantity {
			log.Printf("Insufficient stock for SKU=%s: Available=%d, Requested=%d",
				item.SKU, inventory.AvailableQuantity, item.Quantity)
			// Publish failure event for out of stock
			if ip.producer != nil {
				if publishErr := ip.producer.PublishInventoryFailure(event.OrderID, item.SKU, item.Quantity, inventory.AvailableQuantity, "out_of_stock"); publishErr != nil {
					log.Printf("Failed to publish failure event for out of stock: %v", publishErr)
				}
			}
			return errors.ErrInsufficientStock
		}

		log.Printf("Item %d validation passed: SKU=%s", i+1, item.SKU)
	}

	// Second pass: Deduct stock for all items (only if all validations passed)
	for i, item := range event.Items {
		log.Printf("Deducting stock for item %d: SKU=%s, Quantity=%d", i+1, item.SKU, item.Quantity)

		// Deduct stock
		if err := ip.repo.DeductStock(ctx, item.SKU, item.Quantity); err != nil {
			log.Printf("Failed to deduct stock for SKU=%s: %v", item.SKU, err)
			return err
		}

		log.Printf("Successfully deducted %d units of SKU=%s", item.Quantity, item.SKU)
	}

	// Commit the transaction (if we have one)
	if tx != nil {
		if err := tx.Commit(); err != nil {
			return errors.NewDatabaseError("commit transaction", err)
		}
	}

	log.Printf("Order event processed successfully for order %s", event.OrderID)
	return nil
}

// ValidateOrderEvent validates an order event before processing
func (ip *InventoryProcessor) ValidateOrderEvent(event *models.OrderCreatedEvent) error {
	if event == nil {
		return errors.NewValidationError("event", "Order event cannot be nil")
	}

	// Use the existing validation method
	return event.Validate()
}
