package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/company/cdh/apps/finance-service/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// Note: MockFinanceService is defined in server_test.go
// We'll extend it with the new methods there

func TestServer_GetFinancialRecords(t *testing.T) {
	tests := []struct {
		name           string
		queryParams    string
		mockEntries    []*models.FinancialEntry
		mockTotal      int
		mockError      error
		expectedStatus int
		expectedData   bool
	}{
		{
			name:        "successful request with default pagination",
			queryParams: "",
			mockEntries: []*models.FinancialEntry{
				{
					ID:              1,
					OrderID:         "ORDER-123",
					TransactionID:   "TXN-123",
					RevenueAmount:   100.00,
					TaxAmount:       10.00,
					Currency:        "MYR",
					PaymentMethod:   "credit_card",
					TransactionType: "sale",
					Description:     "Test transaction",
					CreatedAt:       time.Now(),
					UpdatedAt:       time.Now(),
				},
			},
			mockTotal:      1,
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedData:   true,
		},
		{
			name:           "successful request with pagination",
			queryParams:    "?page=2&limit=10",
			mockEntries:    []*models.FinancialEntry{},
			mockTotal:      0,
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedData:   true,
		},
		{
			name:           "successful request with date filters",
			queryParams:    "?date_from=2024-01-01&date_to=2024-12-31",
			mockEntries:    []*models.FinancialEntry{},
			mockTotal:      0,
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedData:   true,
		},
		{
			name:           "invalid page parameter",
			queryParams:    "?page=invalid",
			mockEntries:    nil,
			mockTotal:      0,
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
			expectedData:   false,
		},
		{
			name:           "invalid date format",
			queryParams:    "?date_from=invalid-date",
			mockEntries:    nil,
			mockTotal:      0,
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
			expectedData:   false,
		},
		{
			name:           "service error",
			queryParams:    "",
			mockEntries:    nil,
			mockTotal:      0,
			mockError:      fmt.Errorf("database connection error"),
			expectedStatus: http.StatusInternalServerError,
			expectedData:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock service
			mockService := new(MockFinanceService)

			// Set up expectations for requests that reach the service layer
			if tt.expectedStatus == http.StatusOK || tt.expectedStatus == http.StatusInternalServerError {
				mockService.On("GetFinancialRecords", mock.AnythingOfType("*models.QueryFilters")).
					Return(tt.mockEntries, tt.mockTotal, tt.mockError)
			}

			// Create server with mock service
			server := &Server{
				financeService: mockService,
			}

			// Create request
			req := httptest.NewRequest(http.MethodGet, "/api/v1/financial-records"+tt.queryParams, nil)
			w := httptest.NewRecorder()

			// Execute request
			server.GetFinancialRecords(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedData {
				var response models.FinancialRecordsListResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, len(tt.mockEntries), len(response.Data))
			} else {
				var errorResponse models.ErrorResponse
				err := json.Unmarshal(w.Body.Bytes(), &errorResponse)
				assert.NoError(t, err)
				assert.NotEmpty(t, errorResponse.Message)
			}

			// Verify mock expectations
			mockService.AssertExpectations(t)
		})
	}
}

func TestServer_GetFinancialRecords_MethodNotAllowed(t *testing.T) {
	server := &Server{}

	req := httptest.NewRequest(http.MethodPost, "/api/v1/financial-records", nil)
	w := httptest.NewRecorder()

	server.GetFinancialRecords(w, req)

	assert.Equal(t, http.StatusMethodNotAllowed, w.Code)
}

func TestServer_UpdateFinancialRecordStatus(t *testing.T) {
	tests := []struct {
		name           string
		method         string
		queryParams    string
		requestBody    interface{}
		mockError      error
		expectedStatus int
	}{
		{
			name:        "successful status update",
			method:      http.MethodPatch,
			queryParams: "?id=1",
			requestBody: models.StatusUpdateRequest{
				Status: "processed",
				Notes:  "Successfully processed",
			},
			mockError:      nil,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "missing ID parameter",
			method:         http.MethodPatch,
			queryParams:    "",
			requestBody:    models.StatusUpdateRequest{Status: "processed"},
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid ID parameter",
			method:         http.MethodPatch,
			queryParams:    "?id=invalid",
			requestBody:    models.StatusUpdateRequest{Status: "processed"},
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid request body",
			method:         http.MethodPatch,
			queryParams:    "?id=1",
			requestBody:    "invalid json",
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:        "invalid status value",
			method:      http.MethodPatch,
			queryParams: "?id=1",
			requestBody: models.StatusUpdateRequest{
				Status: "invalid_status",
			},
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:        "record not found",
			method:      http.MethodPatch,
			queryParams: "?id=999",
			requestBody: models.StatusUpdateRequest{
				Status: "processed",
			},
			mockError:      fmt.Errorf("financial record not found for ID: 999"),
			expectedStatus: http.StatusNotFound,
		},
		{
			name:        "service error",
			method:      http.MethodPatch,
			queryParams: "?id=1",
			requestBody: models.StatusUpdateRequest{
				Status: "processed",
			},
			mockError:      fmt.Errorf("database connection error"),
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:           "method not allowed",
			method:         http.MethodGet,
			queryParams:    "?id=1",
			requestBody:    nil,
			mockError:      nil,
			expectedStatus: http.StatusMethodNotAllowed,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock service
			mockService := new(MockFinanceService)

			// Set up expectations for valid requests
			if tt.expectedStatus == http.StatusOK || tt.expectedStatus == http.StatusNotFound || tt.expectedStatus == http.StatusInternalServerError {
				if tt.expectedStatus == http.StatusOK || tt.expectedStatus == http.StatusInternalServerError {
					mockService.On("UpdateFinancialRecordStatus", 1, "processed", mock.AnythingOfType("string")).
						Return(tt.mockError)
				} else if tt.expectedStatus == http.StatusNotFound {
					mockService.On("UpdateFinancialRecordStatus", 999, "processed", mock.AnythingOfType("string")).
						Return(tt.mockError)
				}
			}

			// Create server with mock service
			server := &Server{
				financeService: mockService,
			}

			// Prepare request body
			var body []byte
			if tt.requestBody != nil {
				if str, ok := tt.requestBody.(string); ok {
					body = []byte(str)
				} else {
					body, _ = json.Marshal(tt.requestBody)
				}
			}

			// Create request
			req := httptest.NewRequest(tt.method, "/api/v1/financial-records/status"+tt.queryParams, bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// Execute request
			server.UpdateFinancialRecordStatus(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response models.StatusUpdateResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, 1, response.ID)
				assert.Equal(t, "processed", response.Status)
			} else if tt.expectedStatus != http.StatusMethodNotAllowed {
				var errorResponse models.ErrorResponse
				err := json.Unmarshal(w.Body.Bytes(), &errorResponse)
				assert.NoError(t, err)
				assert.NotEmpty(t, errorResponse.Message)
			}

			// Verify mock expectations
			mockService.AssertExpectations(t)
		})
	}
}

func TestServer_UpdateFinancialRecordStatusByID(t *testing.T) {
	tests := []struct {
		name           string
		path           string
		method         string
		requestBody    string
		mockError      error
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "successful status update",
			path:           "/api/v1/financial-records/123/status",
			method:         http.MethodPatch,
			requestBody:    `{"status": "processed", "notes": "Payment verified"}`,
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedBody:   `"status":"processed"`,
		},
		{
			name:           "invalid URL path - missing ID",
			path:           "/api/v1/financial-records//status",
			method:         http.MethodPatch,
			requestBody:    `{"status": "processed"}`,
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   `"message":"Missing record ID"`,
		},
		{
			name:           "invalid URL path - wrong format",
			path:           "/api/v1/financial-records/123",
			method:         http.MethodPatch,
			requestBody:    `{"status": "processed"}`,
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   `"message":"Invalid URL path"`,
		},
		{
			name:           "invalid ID parameter",
			path:           "/api/v1/financial-records/abc/status",
			method:         http.MethodPatch,
			requestBody:    `{"status": "processed"}`,
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   `"message":"Invalid record ID"`,
		},
		{
			name:           "negative ID parameter",
			path:           "/api/v1/financial-records/-1/status",
			method:         http.MethodPatch,
			requestBody:    `{"status": "processed"}`,
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   `"message":"Invalid record ID"`,
		},
		{
			name:           "invalid request body",
			path:           "/api/v1/financial-records/123/status",
			method:         http.MethodPatch,
			requestBody:    `{"invalid": "json"}`,
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   `"message":"Invalid request body"`,
		},
		{
			name:           "invalid status value",
			path:           "/api/v1/financial-records/123/status",
			method:         http.MethodPatch,
			requestBody:    `{"status": "invalid_status"}`,
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   `"message":"Invalid status value"`,
		},
		{
			name:           "record not found",
			path:           "/api/v1/financial-records/999/status",
			method:         http.MethodPatch,
			requestBody:    `{"status": "processed"}`,
			mockError:      fmt.Errorf("financial record not found for ID: 999"),
			expectedStatus: http.StatusNotFound,
			expectedBody:   `"message":"Financial record not found"`,
		},
		{
			name:           "service error",
			path:           "/api/v1/financial-records/123/status",
			method:         http.MethodPatch,
			requestBody:    `{"status": "processed"}`,
			mockError:      fmt.Errorf("database connection error"),
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   `"message":"Failed to update financial record status"`,
		},
		{
			name:           "method not allowed",
			path:           "/api/v1/financial-records/123/status",
			method:         http.MethodGet,
			requestBody:    `{"status": "processed"}`,
			mockError:      nil,
			expectedStatus: http.StatusMethodNotAllowed,
			expectedBody:   `"message":"Method not allowed"`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock service
			mockService := new(MockFinanceService)

			// Set up expectations for requests that reach the service layer
			if tt.expectedStatus == http.StatusOK || tt.expectedStatus == http.StatusNotFound || tt.expectedStatus == http.StatusInternalServerError {
				mockService.On("UpdateFinancialRecordStatus", mock.AnythingOfType("int"), mock.AnythingOfType("string"), mock.AnythingOfType("string")).
					Return(tt.mockError)
			}

			// Create server with mock service
			server := &Server{
				financeService: mockService,
			}

			// Create request
			req, err := http.NewRequest(tt.method, tt.path, strings.NewReader(tt.requestBody))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call handler
			server.UpdateFinancialRecordStatusByID(rr, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, rr.Code)

			// Check response body contains expected content
			assert.Contains(t, rr.Body.String(), tt.expectedBody)

			// Verify mock expectations
			if tt.expectedStatus == http.StatusOK || tt.expectedStatus == http.StatusNotFound || tt.expectedStatus == http.StatusInternalServerError {
				mockService.AssertExpectations(t)
			}
		})
	}
}
