package repository

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/company/cdh/apps/order-service/internal/errors"
	"github.com/company/cdh/apps/order-service/models"
)

// MockOrderRepository is a mock implementation of OrderRepository for testing
type MockOrderRepository struct {
	mock.Mock
}

func (m *MockOrderRepository) CreateOrder(req *models.OrderRequest) (*models.Order, error) {
	args := m.Called(req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Order), args.Error(1)
}

func (m *MockOrderRepository) GetOrderByID(id int) (*models.Order, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Order), args.Error(1)
}

func (m *MockOrderRepository) GetOrderByNumber(orderNumber string) (*models.Order, error) {
	args := m.Called(orderNumber)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Order), args.Error(1)
}

func (m *MockOrderRepository) UpdateOrderStatus(id int, status models.OrderStatus) error {
	args := m.Called(id, status)
	return args.Error(0)
}

func TestOrderRepositoryInterface(t *testing.T) {
	t.Run("Mock implements OrderRepository interface", func(t *testing.T) {
		var repo OrderRepository = &MockOrderRepository{}
		assert.NotNil(t, repo)
	})
}

func TestMockOrderRepository_CreateOrder(t *testing.T) {
	t.Run("Successful order creation", func(t *testing.T) {
		mockRepo := &MockOrderRepository{}
		req := &models.OrderRequest{
			ProductInfo: "Test Product",
			Quantity:    2,
			Price:       99.99,
		}

		expectedOrder := &models.Order{
			ID:          1,
			OrderNumber: "ORD-123456",
			ProductInfo: "Test Product",
			Quantity:    2,
			Price:       99.99,
			Status:      models.OrderStatusPending,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		mockRepo.On("CreateOrder", req).Return(expectedOrder, nil)

		order, err := mockRepo.CreateOrder(req)

		assert.NoError(t, err)
		assert.Equal(t, expectedOrder, order)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Failed order creation", func(t *testing.T) {
		mockRepo := &MockOrderRepository{}
		req := &models.OrderRequest{
			ProductInfo: "Test Product",
			Quantity:    2,
			Price:       99.99,
		}

		expectedError := errors.NewDatabaseError("create order", errors.ErrDatabaseOperation)

		mockRepo.On("CreateOrder", req).Return(nil, expectedError)

		order, err := mockRepo.CreateOrder(req)

		assert.Error(t, err)
		assert.Nil(t, order)
		assert.Equal(t, expectedError, err)
		mockRepo.AssertExpectations(t)
	})
}

func TestMockOrderRepository_GetOrderByID(t *testing.T) {
	t.Run("Successful order retrieval", func(t *testing.T) {
		mockRepo := &MockOrderRepository{}
		orderID := 1
		expectedOrder := &models.Order{
			ID:          orderID,
			OrderNumber: "ORD-123456",
			ProductInfo: "Test Product",
			Quantity:    2,
			Price:       99.99,
			Status:      models.OrderStatusPending,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		mockRepo.On("GetOrderByID", orderID).Return(expectedOrder, nil)

		order, err := mockRepo.GetOrderByID(orderID)

		assert.NoError(t, err)
		assert.Equal(t, expectedOrder, order)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Order not found", func(t *testing.T) {
		mockRepo := &MockOrderRepository{}
		orderID := 999

		mockRepo.On("GetOrderByID", orderID).Return(nil, errors.ErrOrderNotFound)

		order, err := mockRepo.GetOrderByID(orderID)

		assert.Error(t, err)
		assert.Nil(t, order)
		assert.Equal(t, errors.ErrOrderNotFound, err)
		mockRepo.AssertExpectations(t)
	})
}

func TestMockOrderRepository_GetOrderByNumber(t *testing.T) {
	t.Run("Successful order retrieval by number", func(t *testing.T) {
		mockRepo := &MockOrderRepository{}
		orderNumber := "ORD-123456"
		expectedOrder := &models.Order{
			ID:          1,
			OrderNumber: orderNumber,
			ProductInfo: "Test Product",
			Quantity:    2,
			Price:       99.99,
			Status:      models.OrderStatusPending,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		mockRepo.On("GetOrderByNumber", orderNumber).Return(expectedOrder, nil)

		order, err := mockRepo.GetOrderByNumber(orderNumber)

		assert.NoError(t, err)
		assert.Equal(t, expectedOrder, order)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Order not found by number", func(t *testing.T) {
		mockRepo := &MockOrderRepository{}
		orderNumber := "NON-EXISTENT"

		mockRepo.On("GetOrderByNumber", orderNumber).Return(nil, errors.ErrOrderNotFound)

		order, err := mockRepo.GetOrderByNumber(orderNumber)

		assert.Error(t, err)
		assert.Nil(t, order)
		assert.Equal(t, errors.ErrOrderNotFound, err)
		mockRepo.AssertExpectations(t)
	})
}

func TestMockOrderRepository_UpdateOrderStatus(t *testing.T) {
	t.Run("Successful status update", func(t *testing.T) {
		mockRepo := &MockOrderRepository{}
		orderID := 1
		newStatus := models.OrderStatusConfirmed

		mockRepo.On("UpdateOrderStatus", orderID, newStatus).Return(nil)

		err := mockRepo.UpdateOrderStatus(orderID, newStatus)

		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Order not found for status update", func(t *testing.T) {
		mockRepo := &MockOrderRepository{}
		orderID := 999
		newStatus := models.OrderStatusConfirmed

		mockRepo.On("UpdateOrderStatus", orderID, newStatus).Return(errors.ErrOrderNotFound)

		err := mockRepo.UpdateOrderStatus(orderID, newStatus)

		assert.Error(t, err)
		assert.Equal(t, errors.ErrOrderNotFound, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Invalid status update", func(t *testing.T) {
		mockRepo := &MockOrderRepository{}
		orderID := 1
		invalidStatus := models.OrderStatus("invalid")

		validationErr := errors.NewValidationError("status", "invalid order status")
		mockRepo.On("UpdateOrderStatus", orderID, invalidStatus).Return(validationErr)

		err := mockRepo.UpdateOrderStatus(orderID, invalidStatus)

		assert.Error(t, err)
		var validationError errors.ValidationError
		assert.ErrorAs(t, err, &validationError)
		mockRepo.AssertExpectations(t)
	})
}
