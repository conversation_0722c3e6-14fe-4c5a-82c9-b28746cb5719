package shared

import (
	"encoding/json"
	"testing"
)

func TestBaseResponse_JSON(t *testing.T) {
	tests := []struct {
		name     string
		response BaseResponse
		expected string
	}{
		{
			name: "success response with data",
			response: BaseResponse{
				Success: true,
				Message: "Operation successful",
				Data:    map[string]string{"key": "value"},
			},
			expected: `{"success":true,"message":"Operation successful","data":{"key":"value"}}`,
		},
		{
			name: "error response",
			response: BaseResponse{
				Success: false,
				Error:   "Something went wrong",
			},
			expected: `{"success":false,"error":"Something went wrong"}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonData, err := json.Marshal(tt.response)
			if err != nil {
				t.Fatalf("Failed to marshal response: %v", err)
			}

			if string(jsonData) != tt.expected {
				t.<PERSON>rf("Expected %s, got %s", tt.expected, string(jsonData))
			}
		})
	}
}

func TestPaginationRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		request PaginationRequest
		valid   bool
	}{
		{
			name:    "valid pagination",
			request: PaginationRequest{Page: 1, PageSize: 10},
			valid:   true,
		},
		{
			name:    "valid max page size",
			request: PaginationRequest{Page: 1, PageSize: 100},
			valid:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Basic validation - in real implementation would use validator package
			if tt.request.Page < 1 || tt.request.PageSize < 1 || tt.request.PageSize > 100 {
				if tt.valid {
					t.Error("Expected valid request to pass validation")
				}
			} else {
				if !tt.valid {
					t.Error("Expected invalid request to fail validation")
				}
			}
		})
	}
}
