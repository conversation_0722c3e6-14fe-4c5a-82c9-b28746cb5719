package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/company/cdh/apps/finance-service/models"
)

// MockDB is a mock implementation of database interface
type MockDB struct {
	mock.Mock
}

func (m *MockDB) Ping() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockDB) Close() error {
	args := m.Called()
	return args.Error(0)
}

// MockFinanceService is a mock implementation of FinanceServiceInterface
type MockFinanceService struct {
	mock.Mock
}

func (m *MockFinanceService) ProcessOrderEvent(messageValue []byte) error {
	args := m.Called(messageValue)
	return args.Error(0)
}

func (m *MockFinanceService) PublishFinancialEvent(financialEntry *models.FinancialEntry) error {
	args := m.Called(financialEntry)
	return args.Error(0)
}

func (m *MockFinanceService) GetFinancialRecords(filters *models.QueryFilters) ([]*models.FinancialEntry, int, error) {
	args := m.Called(filters)
	return args.Get(0).([]*models.FinancialEntry), args.Int(1), args.Error(2)
}

func (m *MockFinanceService) UpdateFinancialRecordStatus(id int, status string, notes string) error {
	args := m.Called(id, status, notes)
	return args.Error(0)
}

// TestServer wraps the actual server for testing
type TestServer struct {
	*Server
	mockDB *MockDB
}

func NewTestServer() *TestServer {
	mockDB := &MockDB{}
	mockService := &MockFinanceService{}

	// Create a test server with mock dependencies
	server := &Server{
		db:             nil, // We'll mock the database calls directly
		financeService: mockService,
	}

	return &TestServer{
		Server: server,
		mockDB: mockDB,
	}
}

func TestServer_healthHandler(t *testing.T) {
	testServer := NewTestServer()

	t.Run("method not allowed", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodPost, "/health", nil)
		w := httptest.NewRecorder()

		testServer.healthHandler(w, req)

		assert.Equal(t, http.StatusMethodNotAllowed, w.Code)
	})
}

func TestServer_livenessHandler(t *testing.T) {
	testServer := NewTestServer()

	t.Run("successful liveness check", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/health/live", nil)
		w := httptest.NewRecorder()

		testServer.livenessHandler(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response HealthResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "alive", response.Status)
		assert.Equal(t, "finance-service", response.Service)
	})

	t.Run("method not allowed", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodPost, "/health/live", nil)
		w := httptest.NewRecorder()

		testServer.livenessHandler(w, req)

		assert.Equal(t, http.StatusMethodNotAllowed, w.Code)
	})
}

func TestServer_readinessHandler(t *testing.T) {
	testServer := NewTestServer()

	t.Run("method not allowed", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodPost, "/health/ready", nil)
		w := httptest.NewRecorder()

		testServer.readinessHandler(w, req)

		assert.Equal(t, http.StatusMethodNotAllowed, w.Code)
	})
}

func TestHealthResponse_Structure(t *testing.T) {
	response := HealthResponse{
		Status:  "healthy",
		Service: "finance-service",
		Version: "1.0.0",
		Dependencies: map[string]string{
			"database": "healthy",
		},
	}

	assert.Equal(t, "healthy", response.Status)
	assert.Equal(t, "finance-service", response.Service)
	assert.Equal(t, "1.0.0", response.Version)
	assert.Equal(t, "healthy", response.Dependencies["database"])
}
