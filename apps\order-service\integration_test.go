package main

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/company/cdh/apps/order-service/database"
	"github.com/company/cdh/apps/order-service/events"
	"github.com/company/cdh/apps/order-service/handlers"
	"github.com/company/cdh/apps/order-service/internal/config"
	"github.com/company/cdh/apps/order-service/models"
	"github.com/company/cdh/apps/order-service/services"
)

// SimpleMockProducer is a simple mock for integration testing
type SimpleMockProducer struct{}

func (p *SimpleMockProducer) PublishOrderCreated(event *events.OrderCreatedEvent) error {
	// For integration tests, just log that event would be published
	return nil
}

func (p *SimpleMockProducer) Close() error {
	return nil
}

func setupTestDB(t *testing.T) *sql.DB {
	// Set test environment variables
	os.Setenv("DB_HOST", "localhost")
	os.Setenv("DB_PORT", "5433")
	os.Setenv("DB_USER", "postgres")
	os.Setenv("DB_PASSWORD", "postgres")
	os.Setenv("DB_NAME", "order_db")
	os.Setenv("DB_SSLMODE", "disable")

	// Load config and connect to database
	cfg, err := config.Load()
	require.NoError(t, err, "Failed to load config")

	err = database.Connect(&cfg.Database)
	require.NoError(t, err, "Failed to connect to test database")

	// Run migrations
	err = database.RunMigrations(database.DB)
	require.NoError(t, err, "Failed to run migrations")

	return database.DB
}

func cleanupTestDB(t *testing.T) {
	if database.DB != nil {
		// Clean up test data
		_, err := database.DB.Exec("DELETE FROM orders WHERE order_number LIKE 'TEST-%'")
		if err != nil {
			t.Logf("Warning: Failed to clean up test data: %v", err)
		}

		database.Close()
	}
}

func TestOrderServiceIntegration(t *testing.T) {
	_ = setupTestDB(t)
	defer cleanupTestDB(t)

	// Create mock producer for integration test
	mockProducer := &SimpleMockProducer{}

	// Initialize repository and service
	repo := database.NewOrderRepository(database.DB)
	orderService := services.NewOrderService(repo, mockProducer)

	// Initialize handlers
	orderHandler := handlers.NewOrderHandler(orderService)

	// Create HTTP server mux
	mux := http.NewServeMux()
	mux.HandleFunc("/health", handlers.HealthHandler)
	mux.HandleFunc("/orders", orderHandler.OrdersHandler)
	mux.HandleFunc("/orders/", orderHandler.OrderByIDHandler)

	// Create test server
	server := httptest.NewServer(mux)
	defer server.Close()

	t.Run("Health Check", func(t *testing.T) {
		resp, err := http.Get(server.URL + "/health")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "application/json", resp.Header.Get("Content-Type"))

		var health handlers.HealthResponse
		err = json.NewDecoder(resp.Body).Decode(&health)
		require.NoError(t, err)
		assert.Equal(t, "ok", health.Status)
		assert.Equal(t, "order-service", health.Service)
	})

	t.Run("Create and Retrieve Order", func(t *testing.T) {
		// Create order
		orderReq := models.OrderRequest{
			ProductInfo: "Test Product Integration",
			Quantity:    2,
			Price:       29.99,
		}

		jsonData, err := json.Marshal(orderReq)
		require.NoError(t, err)

		resp, err := http.Post(server.URL+"/orders", "application/json", bytes.NewBuffer(jsonData))
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusCreated, resp.StatusCode)
		assert.Equal(t, "application/json", resp.Header.Get("Content-Type"))

		var createResponse struct {
			Data    models.Order `json:"data"`
			Message string       `json:"message"`
		}
		err = json.NewDecoder(resp.Body).Decode(&createResponse)
		require.NoError(t, err)

		createdOrder := createResponse.Data

		// Verify created order
		assert.NotZero(t, createdOrder.ID)
		assert.NotEmpty(t, createdOrder.OrderNumber)
		assert.Equal(t, orderReq.ProductInfo, createdOrder.ProductInfo)
		assert.Equal(t, orderReq.Quantity, createdOrder.Quantity)
		assert.Equal(t, orderReq.Price, createdOrder.Price)
		assert.Equal(t, models.OrderStatusPending, createdOrder.Status)

		// Retrieve order by ID
		getURL := fmt.Sprintf("%s/orders/%d", server.URL, createdOrder.ID)
		resp, err = http.Get(getURL)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var getResponse struct {
			Data    models.Order `json:"data"`
			Message string       `json:"message"`
		}
		err = json.NewDecoder(resp.Body).Decode(&getResponse)
		require.NoError(t, err)

		retrievedOrder := getResponse.Data

		// Verify retrieved order matches created order
		assert.Equal(t, createdOrder.ID, retrievedOrder.ID)
		assert.Equal(t, createdOrder.OrderNumber, retrievedOrder.OrderNumber)
		assert.Equal(t, createdOrder.ProductInfo, retrievedOrder.ProductInfo)
		assert.Equal(t, createdOrder.Quantity, retrievedOrder.Quantity)
		assert.Equal(t, createdOrder.Price, retrievedOrder.Price)
		assert.Equal(t, createdOrder.Status, retrievedOrder.Status)
	})

	t.Run("Get Non-existent Order", func(t *testing.T) {
		resp, err := http.Get(server.URL + "/orders/99999")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
	})
}
