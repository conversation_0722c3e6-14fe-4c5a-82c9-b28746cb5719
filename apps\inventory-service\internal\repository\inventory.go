package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/company/cdh/apps/inventory-service/internal/errors"
	"github.com/company/cdh/apps/inventory-service/models"
)

// inventoryRepository implements the InventoryRepository interface
type inventoryRepository struct {
	db *sql.DB
}

// NewInventoryRepository creates a new inventory repository
func NewInventoryRepository(db *sql.DB) InventoryRepository {
	return &inventoryRepository{
		db: db,
	}
}

// GetBySKU retrieves an inventory item by SKU
func (r *inventoryRepository) GetBySKU(ctx context.Context, sku string) (*models.Inventory, error) {
	query := `
		SELECT id, sku, product_name, description, stock_quantity, reserved_quantity, 
		       available_quantity, unit_price, created_at, updated_at
		FROM inventory 
		WHERE sku = $1
	`

	var inventory models.Inventory
	err := r.db.QueryRowContext(ctx, query, sku).Scan(
		&inventory.ID,
		&inventory.SKU,
		&inventory.ProductName,
		&inventory.Description,
		&inventory.StockQuantity,
		&inventory.ReservedQuantity,
		&inventory.AvailableQuantity,
		&inventory.UnitPrice,
		&inventory.CreatedAt,
		&inventory.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.ErrInventoryNotFound
		}
		return nil, errors.NewDatabaseError("get inventory by SKU", err)
	}

	return &inventory, nil
}

// GetBySKUs retrieves multiple inventory items by SKUs
func (r *inventoryRepository) GetBySKUs(ctx context.Context, skus []string) ([]*models.Inventory, error) {
	if len(skus) == 0 {
		return []*models.Inventory{}, nil
	}

	// Create placeholders for the IN clause
	placeholders := make([]string, len(skus))
	args := make([]interface{}, len(skus))
	for i, sku := range skus {
		placeholders[i] = fmt.Sprintf("$%d", i+1)
		args[i] = sku
	}

	query := fmt.Sprintf(`
		SELECT id, sku, product_name, description, stock_quantity, reserved_quantity,
		       available_quantity, unit_price, created_at, updated_at
		FROM inventory
		WHERE sku IN (%s)
		ORDER BY sku
	`, strings.Join(placeholders, ","))

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errors.NewDatabaseError("get inventory by SKUs", err)
	}
	defer rows.Close()

	var inventories []*models.Inventory
	for rows.Next() {
		var inventory models.Inventory
		err := rows.Scan(
			&inventory.ID,
			&inventory.SKU,
			&inventory.ProductName,
			&inventory.Description,
			&inventory.StockQuantity,
			&inventory.ReservedQuantity,
			&inventory.AvailableQuantity,
			&inventory.UnitPrice,
			&inventory.CreatedAt,
			&inventory.UpdatedAt,
		)
		if err != nil {
			return nil, errors.NewDatabaseError("scan inventory item", err)
		}
		inventories = append(inventories, &inventory)
	}

	if err := rows.Err(); err != nil {
		return nil, errors.NewDatabaseError("iterate inventory items", err)
	}

	return inventories, nil
}

// Create creates a new inventory item
func (r *inventoryRepository) Create(ctx context.Context, inventory *models.Inventory) error {
	query := `
		INSERT INTO inventory (sku, product_name, description, stock_quantity, reserved_quantity, unit_price)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING id, created_at, updated_at
	`

	err := r.db.QueryRowContext(ctx, query,
		inventory.SKU,
		inventory.ProductName,
		inventory.Description,
		inventory.StockQuantity,
		inventory.ReservedQuantity,
		inventory.UnitPrice,
	).Scan(&inventory.ID, &inventory.CreatedAt, &inventory.UpdatedAt)

	if err != nil {
		return errors.NewDatabaseError("create inventory", err)
	}

	return nil
}

// Update updates an existing inventory item
func (r *inventoryRepository) Update(ctx context.Context, inventory *models.Inventory) error {
	query := `
		UPDATE inventory 
		SET product_name = $2, description = $3, stock_quantity = $4, 
		    reserved_quantity = $5, unit_price = $6, updated_at = CURRENT_TIMESTAMP
		WHERE sku = $1
	`

	result, err := r.db.ExecContext(ctx, query,
		inventory.SKU,
		inventory.ProductName,
		inventory.Description,
		inventory.StockQuantity,
		inventory.ReservedQuantity,
		inventory.UnitPrice,
	)

	if err != nil {
		return errors.NewDatabaseError("update inventory", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.NewDatabaseError("check update result", err)
	}

	if rowsAffected == 0 {
		return errors.ErrInventoryNotFound
	}

	return nil
}

// ReserveStock reserves stock for an order
func (r *inventoryRepository) ReserveStock(ctx context.Context, sku string, quantity int) error {
	if quantity <= 0 {
		return errors.ErrInvalidQuantity
	}

	query := `
		UPDATE inventory 
		SET reserved_quantity = reserved_quantity + $2, updated_at = CURRENT_TIMESTAMP
		WHERE sku = $1 AND (stock_quantity - reserved_quantity) >= $2
	`

	result, err := r.db.ExecContext(ctx, query, sku, quantity)
	if err != nil {
		return errors.NewDatabaseError("reserve stock", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.NewDatabaseError("check reserve result", err)
	}

	if rowsAffected == 0 {
		// Check if item exists
		_, err := r.GetBySKU(ctx, sku)
		if err != nil {
			return err
		}
		return errors.ErrInsufficientStock
	}

	return nil
}

// ReleaseStock releases reserved stock
func (r *inventoryRepository) ReleaseStock(ctx context.Context, sku string, quantity int) error {
	if quantity <= 0 {
		return errors.ErrInvalidQuantity
	}

	query := `
		UPDATE inventory 
		SET reserved_quantity = GREATEST(0, reserved_quantity - $2), updated_at = CURRENT_TIMESTAMP
		WHERE sku = $1
	`

	result, err := r.db.ExecContext(ctx, query, sku, quantity)
	if err != nil {
		return errors.NewDatabaseError("release stock", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.NewDatabaseError("check release result", err)
	}

	if rowsAffected == 0 {
		return errors.ErrInventoryNotFound
	}

	return nil
}

// DeductStock deducts stock from inventory
func (r *inventoryRepository) DeductStock(ctx context.Context, sku string, quantity int) error {
	if quantity <= 0 {
		return errors.ErrInvalidQuantity
	}

	query := `
		UPDATE inventory 
		SET stock_quantity = stock_quantity - $2, 
		    reserved_quantity = GREATEST(0, reserved_quantity - $2),
		    updated_at = CURRENT_TIMESTAMP
		WHERE sku = $1 AND stock_quantity >= $2
	`

	result, err := r.db.ExecContext(ctx, query, sku, quantity)
	if err != nil {
		return errors.NewDatabaseError("deduct stock", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.NewDatabaseError("check deduct result", err)
	}

	if rowsAffected == 0 {
		// Check if item exists
		_, err := r.GetBySKU(ctx, sku)
		if err != nil {
			return err
		}
		return errors.ErrInsufficientStock
	}

	return nil
}

// GetLowStockItems returns items with stock below threshold
func (r *inventoryRepository) GetLowStockItems(ctx context.Context, threshold int) ([]*models.Inventory, error) {
	query := `
		SELECT id, sku, product_name, description, stock_quantity, reserved_quantity, 
		       available_quantity, unit_price, created_at, updated_at
		FROM inventory 
		WHERE available_quantity < $1
		ORDER BY available_quantity ASC
	`

	rows, err := r.db.QueryContext(ctx, query, threshold)
	if err != nil {
		return nil, errors.NewDatabaseError("get low stock items", err)
	}
	defer rows.Close()

	var items []*models.Inventory
	for rows.Next() {
		var inventory models.Inventory
		err := rows.Scan(
			&inventory.ID,
			&inventory.SKU,
			&inventory.ProductName,
			&inventory.Description,
			&inventory.StockQuantity,
			&inventory.ReservedQuantity,
			&inventory.AvailableQuantity,
			&inventory.UnitPrice,
			&inventory.CreatedAt,
			&inventory.UpdatedAt,
		)
		if err != nil {
			return nil, errors.NewDatabaseError("scan low stock item", err)
		}
		items = append(items, &inventory)
	}

	if err := rows.Err(); err != nil {
		return nil, errors.NewDatabaseError("iterate low stock items", err)
	}

	return items, nil
}
