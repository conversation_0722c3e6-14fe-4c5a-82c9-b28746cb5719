# Build stage
FROM golang:1.21-alpine AS builder

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

# Create app directory
WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/main .

# Expose port
EXPOSE 8082

# Set environment variables
ENV PORT=8082
ENV DB_HOST=localhost
ENV DB_PORT=5435
ENV DB_USER=postgres
ENV DB_PASSWORD=postgres
ENV DB_NAME=finance_db
ENV DB_SSLMODE=disable
ENV KAFKA_BROKERS=localhost:9092
ENV KAFKA_GROUP_ID=finance-service-group
ENV KAFKA_TOPIC=orders.created

# Run the application
CMD ["./main"]