# Deployment Guide - Independent Database Infrastructure

## Overview

This guide covers the deployment of the CDH platform with independent PostgreSQL databases for each microservice.

## Development Environment

### Prerequisites

- Docker and Docker Compose
- Go 1.21+
- kubectl (for Kubernetes deployment)

### Local Development Setup

1. **Start Database Infrastructure:**
   ```bash
   # Start all independent databases
   docker-compose -f docker-compose.databases.yml up -d
   
   # Verify all databases are healthy
   docker-compose -f docker-compose.databases.yml ps
   ```

2. **Configure User Service:**
   ```bash
   cd apps/user-service
   
   # Copy environment template
   cp .env.example .env
   
   # Edit .env with your configuration
   # DB_HOST=localhost
   # DB_PORT=5432
   # DB_USER=postgres
   # DB_PASSWORD=postgres
   # DB_NAME=user_db
   # DB_SSLMODE=disable
   ```

3. **Run User Service:**
   ```bash
   # Install dependencies
   go mod tidy
   
   # Run tests
   go test ./...
   
   # Start service
   go run main.go
   ```

4. **Configure Inventory Service:**
   ```bash
   cd apps/inventory-service

   # Copy environment template (if exists) or set environment variables
   export DB_HOST=localhost
   export DB_PORT=5434
   export DB_USER=postgres
   export DB_PASSWORD=postgres
   export DB_NAME=inventory_db
   export DB_SSLMODE=disable
   export REDIS_HOST=127.0.0.1
   export REDIS_PORT=6379
   export KAFKA_BROKERS=127.0.0.1:9092
   ```

5. **Start External Services (Redis & Kafka):**
   ```bash
   # Start Redis and Kafka services
   docker-compose -f docker-compose.services.yml up -d

   # Or use the management script
   .\scripts\dev\start-services.ps1 -Services messaging,cache

   # Verify services are running
   docker-compose -f docker-compose.services.yml ps
   ```

6. **Run Inventory Service:**
   ```bash
   # Install dependencies
   go mod tidy

   # Run tests
   go test ./...

   # Run integration tests (requires running services)
   go test ./integration_test.go -v

   # Start service
   go run main.go
   ```

7. **Verify Setup:**
   ```bash
   # Test health endpoint
   curl http://localhost:8080/health

   # Test registration
   curl -X POST http://localhost:8080/register \
     -H "Content-Type: application/json" \
     -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'

   # Test inventory service health
   curl http://localhost:8081/health

   # Test inventory query
   curl "http://localhost:8081/inventory?sku=LAPTOP-001"
   ```

## Production Environment

### Prerequisites

- Kubernetes cluster (1.25+)
- Persistent storage class configured
- SSL certificates for database connections
- Monitoring and logging infrastructure

### Database Deployment

1. **Create Production Namespace:**
   ```bash
   kubectl create namespace cdh-prod
   ```

2. **Deploy User Database:**
   ```bash
   # Update secrets in user-db-statefulset.yaml before deployment
   kubectl apply -f infra/k8s/prod/databases/user-db-statefulset.yaml
   
   # Verify deployment
   kubectl get statefulset -n cdh-prod
   kubectl get pods -n cdh-prod -l app=user-db
   ```

3. **Verify Database Connectivity:**
   ```bash
   # Port forward to test connection
   kubectl port-forward -n cdh-prod svc/user-db 5432:5432
   
   # Test connection (in another terminal)
   psql -h localhost -p 5432 -U postgres -d user_db
   ```

### Service Deployment

1. **Deploy User Service:**
   ```bash
   # Update secrets in deployment.yaml before deployment
   kubectl apply -f infra/k8s/prod/user-service/deployment.yaml
   
   # Verify deployment
   kubectl get deployment -n cdh-prod
   kubectl get pods -n cdh-prod -l app=user-service
   ```

2. **Deploy Inventory Service:**
   ```bash
   # Update secrets in deployment.yaml before deployment
   kubectl apply -f infra/k8s/dev/inventory-service/deployment.yaml

   # Verify deployment
   kubectl get deployment -n cdh-dev
   kubectl get pods -n cdh-dev -l app=inventory-service
   ```

3. **Deploy Traefik API Gateway:**
   ```bash
   # Deploy Traefik with inventory service routes
   kubectl apply -f infra/k8s/dev/traefik/

   # Verify Traefik deployment
   kubectl get pods -n cdh-dev -l app=traefik
   ```

4. **Verify Service Health:**
   ```bash
   # Port forward to test user service
   kubectl port-forward -n cdh-dev svc/user-service 8080:8080

   # Test user service health endpoint
   curl http://localhost:8080/health

   # Port forward to test inventory service
   kubectl port-forward -n cdh-dev svc/inventory-service 8081:8081

   # Test inventory service health endpoint
   curl http://localhost:8081/health

   # Test inventory query endpoint
   curl "http://localhost:8081/inventory?sku=LAPTOP-001"
   ```

## Migration from Supabase

### Data Export

1. **Export User Data from Supabase:**
   ```bash
   # Connect to Supabase database
   psql "postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require"
   
   # Run export script
   \i scripts/export-supabase-data.sql
   ```

2. **Prepare Import Data:**
   ```bash
   # Edit scripts/import-user-data.sql with actual exported data
   # Replace placeholder comments with actual INSERT statements
   ```

### Data Import

1. **Import to Independent Database:**
   ```bash
   # For development
   psql -h localhost -p 5432 -U postgres -d user_db -f scripts/import-user-data.sql
   
   # For production (via port forward)
   kubectl port-forward -n cdh-prod svc/user-db 5432:5432
   psql -h localhost -p 5432 -U postgres -d user_db -f scripts/import-user-data.sql
   ```

2. **Verify Migration:**
   ```bash
   # Check user count
   psql -h localhost -p 5432 -U postgres -d user_db -c "SELECT COUNT(*) FROM users;"
   
   # Test authentication with migrated user
   curl -X POST http://localhost:8080/login \
     -H "Content-Type: application/json" \
     -d '{"username":"existing_user","password":"their_password"}'
   ```

## Security Configuration

### Production Secrets

1. **Database Secrets:**
   ```bash
   # Generate secure passwords
   openssl rand -base64 32
   
   # Update secrets in StatefulSet configuration
   echo -n "your-secure-password" | base64
   ```

2. **JWT Secrets:**
   ```bash
   # Generate JWT secret
   openssl rand -base64 64
   
   # Update JWT secret in service configuration
   echo -n "your-jwt-secret" | base64
   ```

### SSL Configuration

1. **Database SSL:**
   ```yaml
   # Update service environment variables
   - name: DB_SSLMODE
     value: "require"
   ```

2. **Service SSL:**
   ```bash
   # Configure TLS certificates for service endpoints
   # Update ingress configuration with SSL certificates
   ```

## Monitoring and Maintenance

### Health Checks

```bash
# Check database health
kubectl exec -n cdh-prod user-db-0 -- pg_isready -U postgres -d user_db

# Check service health
kubectl get pods -n cdh-prod -l app=user-service
curl http://service-endpoint/health
```

### Backup and Recovery

```bash
# Database backup
kubectl exec -n cdh-prod user-db-0 -- pg_dump -U postgres user_db > backup.sql

# Database restore
kubectl exec -i -n cdh-prod user-db-0 -- psql -U postgres user_db < backup.sql
```

### Scaling

```bash
# Scale service replicas
kubectl scale deployment user-service -n cdh-prod --replicas=5

# Monitor resource usage
kubectl top pods -n cdh-prod
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed:**
   ```bash
   # Check database pod status
   kubectl describe pod -n cdh-prod user-db-0
   
   # Check database logs
   kubectl logs -n cdh-prod user-db-0
   ```

2. **Service Startup Failed:**
   ```bash
   # Check service logs
   kubectl logs -n cdh-prod -l app=user-service
   
   # Check environment variables
   kubectl describe pod -n cdh-prod -l app=user-service
   ```

3. **Authentication Issues:**
   ```bash
   # Verify JWT secret configuration
   kubectl get secret -n cdh-prod user-service-secret -o yaml
   
   # Test database connectivity
   kubectl exec -n cdh-prod user-db-0 -- psql -U postgres -d user_db -c "SELECT COUNT(*) FROM users;"
   ```

### Performance Optimization

1. **Database Performance:**
   ```sql
   -- Check database performance
   SELECT * FROM pg_stat_activity;
   
   -- Analyze query performance
   EXPLAIN ANALYZE SELECT * FROM users WHERE username = 'example';
   ```

2. **Service Performance:**
   ```bash
   # Monitor resource usage
   kubectl top pods -n cdh-prod -l app=user-service
   
   # Check service metrics
   curl http://service-endpoint/metrics
   ```