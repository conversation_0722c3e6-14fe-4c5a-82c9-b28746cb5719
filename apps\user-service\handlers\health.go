package handlers

import (
	"encoding/json"
	"log"
	"net/http"
)

// HealthResponse represents the health check response
type HealthResponse struct {
	Status string `json:"status"`
}

// HealthHandler handles the health check endpoint
func HealthHandler(w http.ResponseWriter, r *http.Request) {
	// Set content type first
	w.Header().Set("Content-Type", "application/json")
	
	// Log health check access for monitoring
	log.Printf("Health check accessed from %s", r.RemoteAddr)

	response := HealthResponse{
		Status: "ok",
	}

	// Set status code explicitly
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		// Log the error but don't change response since headers are already written
		log.Printf("Failed to encode health response: %v", err)
		return
	}
}
