package services

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/company/cdh/apps/inventory-service/models"
)

func TestInventoryService_GetInventoryBySKUs(t *testing.T) {
	tests := []struct {
		name          string
		skus          []string
		mockSetup     func(*MockInventoryRepository)
		expectedCount int
		expectError   bool
		errorContains string
	}{
		{
			name: "successful query with multiple SKUs",
			skus: []string{"SKU001", "SKU002"},
			mockSetup: func(m *MockInventoryRepository) {
				inventories := []*models.Inventory{
					{
						ID:               1,
						SKU:              "SKU001",
						ProductName:      "Product 1",
						StockQuantity:    100,
						ReservedQuantity: 10,
						UnitPrice:        29.99,
						CreatedAt:        time.Now(),
						UpdatedAt:        time.Now(),
					},
					{
						ID:               2,
						SKU:              "SKU002",
						ProductName:      "Product 2",
						StockQuantity:    50,
						ReservedQuantity: 5,
						UnitPrice:        19.99,
						CreatedAt:        time.Now(),
						UpdatedAt:        time.Now(),
					},
				}
				m.On("GetBySKUs", mock.Anything, []string{"SKU001", "SKU002"}).Return(inventories, nil)
			},
			expectedCount: 2,
			expectError:   false,
		},
		{
			name: "successful query with single SKU",
			skus: []string{"SKU001"},
			mockSetup: func(m *MockInventoryRepository) {
				inventories := []*models.Inventory{
					{
						ID:               1,
						SKU:              "SKU001",
						ProductName:      "Product 1",
						StockQuantity:    100,
						ReservedQuantity: 10,
						UnitPrice:        29.99,
					},
				}
				m.On("GetBySKUs", mock.Anything, []string{"SKU001"}).Return(inventories, nil)
			},
			expectedCount: 1,
			expectError:   false,
		},
		{
			name:          "empty SKU list",
			skus:          []string{},
			mockSetup:     func(m *MockInventoryRepository) {},
			expectedCount: 0,
			expectError:   false,
		},
		{
			name:          "SKU list with empty string",
			skus:          []string{"SKU001", "", "SKU003"},
			mockSetup:     func(m *MockInventoryRepository) {},
			expectedCount: 0,
			expectError:   true,
			errorContains: "SKU at index 1 is empty",
		},
		{
			name: "repository error",
			skus: []string{"SKU001"},
			mockSetup: func(m *MockInventoryRepository) {
				m.On("GetBySKUs", mock.Anything, []string{"SKU001"}).Return(nil, errors.New("database error"))
			},
			expectedCount: 0,
			expectError:   true,
			errorContains: "failed to query inventories",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockRepo := new(MockInventoryRepository)
			tt.mockSetup(mockRepo)

			service := NewInventoryService(mockRepo, nil) // nil Redis client for tests
			ctx := context.Background()

			// Execute
			result, err := service.GetInventoryBySKUs(ctx, tt.skus)

			// Assert
			if tt.expectError {
				require.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
				assert.Nil(t, result)
			} else {
				require.NoError(t, err)
				assert.Len(t, result, tt.expectedCount)

				// Verify available quantities are calculated
				for _, inventory := range result {
					expectedAvailable := inventory.StockQuantity - inventory.ReservedQuantity
					assert.Equal(t, expectedAvailable, inventory.AvailableQuantity)
				}
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestInventoryService_GetInventoryBySKU(t *testing.T) {
	tests := []struct {
		name          string
		sku           string
		mockSetup     func(*MockInventoryRepository)
		expectError   bool
		errorContains string
	}{
		{
			name: "successful query",
			sku:  "SKU001",
			mockSetup: func(m *MockInventoryRepository) {
				inventory := &models.Inventory{
					ID:               1,
					SKU:              "SKU001",
					ProductName:      "Product 1",
					StockQuantity:    100,
					ReservedQuantity: 10,
					UnitPrice:        29.99,
				}
				m.On("GetBySKU", mock.Anything, "SKU001").Return(inventory, nil)
			},
			expectError: false,
		},
		{
			name:          "empty SKU",
			sku:           "",
			mockSetup:     func(m *MockInventoryRepository) {},
			expectError:   true,
			errorContains: "SKU cannot be empty",
		},
		{
			name: "repository error",
			sku:  "SKU001",
			mockSetup: func(m *MockInventoryRepository) {
				m.On("GetBySKU", mock.Anything, "SKU001").Return(nil, errors.New("not found"))
			},
			expectError:   true,
			errorContains: "failed to query inventory",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockRepo := new(MockInventoryRepository)
			tt.mockSetup(mockRepo)

			service := NewInventoryService(mockRepo, nil) // nil Redis client for tests
			ctx := context.Background()

			// Execute
			result, err := service.GetInventoryBySKU(ctx, tt.sku)

			// Assert
			if tt.expectError {
				require.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
				assert.Nil(t, result)
			} else {
				require.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.sku, result.SKU)

				// Verify available quantity is calculated
				expectedAvailable := result.StockQuantity - result.ReservedQuantity
				assert.Equal(t, expectedAvailable, result.AvailableQuantity)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestInventoryService_CacheKeyGeneration(t *testing.T) {
	service := &inventoryService{}

	tests := []struct {
		sku      string
		expected string
	}{
		{"SKU001", "inventory:SKU001"},
		{"PROD-123", "inventory:PROD-123"},
		{"", "inventory:"},
	}

	for _, tt := range tests {
		t.Run(tt.sku, func(t *testing.T) {
			result := service.getCacheKey(tt.sku)
			assert.Equal(t, tt.expected, result)
		})
	}
}
