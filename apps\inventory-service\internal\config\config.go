package config

import (
	"os"
	"strconv"
	"time"
)

// Config holds all configuration for the inventory service
type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	Redis    RedisConfig
	Kafka    KafkaConfig
}

// ServerConfig holds HTTP server configuration
type ServerConfig struct {
	Port string
}

// DatabaseConfig holds PostgreSQL database configuration
type DatabaseConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	Name     string
	SSLMode  string
}

// RedisConfig holds Redis cache configuration
type RedisConfig struct {
	Host     string
	Port     string
	Password string
	DB       int
}

// KafkaConfig holds Kafka consumer configuration
type KafkaConfig struct {
	Brokers           []string
	GroupID           string
	Topic             string
	FailureTopic      string
	SessionTimeout    time.Duration
	HeartbeatInterval time.Duration
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	redisDB := 0
	if dbStr := os.Getenv("REDIS_DB"); dbStr != "" {
		if db, err := strconv.Atoi(dbStr); err == nil {
			redisDB = db
		}
	}

	return &Config{
		Server: ServerConfig{
			Port: getEnv("PORT", "8081"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5434"),
			User:     getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", "postgres"),
			Name:     getEnv("DB_NAME", "inventory_db"),
			SSLMode:  getEnv("DB_SSLMODE", "disable"),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "127.0.0.1"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       redisDB,
		},
		Kafka: KafkaConfig{
			Brokers:           []string{getEnv("KAFKA_BROKERS", "127.0.0.1:9092")},
			GroupID:           getEnv("KAFKA_GROUP_ID", "inventory-service-group"),
			Topic:             getEnv("KAFKA_TOPIC", "orders.created"),
			FailureTopic:      getEnv("KAFKA_FAILURE_TOPIC", "inventory.failures"),
			SessionTimeout:    parseDuration(getEnv("KAFKA_SESSION_TIMEOUT", "10s")),
			HeartbeatInterval: parseDuration(getEnv("KAFKA_HEARTBEAT_INTERVAL", "3s")),
		},
	}, nil
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}

// parseDuration parses a duration string with fallback to default
func parseDuration(durationStr string) time.Duration {
	duration, err := time.ParseDuration(durationStr)
	if err != nil {
		// Return a sensible default if parsing fails
		if durationStr == "10s" {
			return 10 * time.Second
		}
		return 3 * time.Second
	}
	return duration
}
