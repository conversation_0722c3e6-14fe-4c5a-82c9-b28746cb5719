-- Import script for loading exported user data into independent User Service database
-- Run this script after starting the independent database infrastructure

-- Import users from CSV export (if using CSV method)
-- \copy users (id, username, email, password_hash, created_at, updated_at) FROM 'users_export.csv' WITH CSV HEADER;

-- Reset sequence to continue from the highest imported ID
-- SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));

-- Verify import
-- SELECT COUNT(*) as total_users FROM users;
-- SELECT username, email, created_at FROM users ORDER BY created_at DESC LIMIT 5;

-- Note: This script should be customized based on the actual data export format
-- For manual migration, replace this with the actual INSERT statements from export