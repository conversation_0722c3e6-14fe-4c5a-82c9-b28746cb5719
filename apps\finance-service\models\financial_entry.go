package models

import (
	"time"
	"errors"
)

// FinancialEntry represents a financial record generated from an order
type FinancialEntry struct {
	ID              int       `json:"id" db:"id"`
	OrderID         string    `json:"order_id" db:"order_id"`
	TransactionID   string    `json:"transaction_id" db:"transaction_id"`
	RevenueAmount   float64   `json:"revenue_amount" db:"revenue_amount"`
	TaxAmount       float64   `json:"tax_amount" db:"tax_amount"`
	Currency        string    `json:"currency" db:"currency"`
	PaymentMethod   string    `json:"payment_method" db:"payment_method"`
	TransactionType string    `json:"transaction_type" db:"transaction_type"`
	Description     string    `json:"description" db:"description"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time `json:"updated_at" db:"updated_at"`
}

// Validate validates the financial entry data
func (fe *FinancialEntry) Validate() error {
	if fe.OrderID == "" {
		return errors.New("order_id is required")
	}
	if fe.TransactionID == "" {
		return errors.New("transaction_id is required")
	}
	if fe.RevenueAmount < 0 {
		return errors.New("revenue_amount cannot be negative")
	}
	if fe.TaxAmount < 0 {
		return errors.New("tax_amount cannot be negative")
	}
	if fe.Currency == "" {
		return errors.New("currency is required")
	}
	if fe.PaymentMethod == "" {
		return errors.New("payment_method is required")
	}
	if fe.TransactionType == "" {
		return errors.New("transaction_type is required")
	}
	return nil
}

// CalculateTotalAmount returns the total amount (revenue + tax)
func (fe *FinancialEntry) CalculateTotalAmount() float64 {
	return fe.RevenueAmount + fe.TaxAmount
}