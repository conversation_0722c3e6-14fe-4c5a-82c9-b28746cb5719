# 2. High-Level Architecture

### Technical Summary
This system uses an event-driven microservices architecture based on Kubernetes (K8s). All services are developed in Go and deployed as Docker containers. Asynchronous communication between services is achieved via Kafka for decoupling and high reliability. Traefik serves as the API Gateway, acting as the single entry point for all external requests, handling routing, security, and authentication. Each service has its own independent PostgreSQL database to ensure data isolation.

### Architecture Diagram
```mermaid
graph TD
    subgraph "External Systems (Future Development)"
        POS[POS System]
        WMS[WMS System]
        ECOM[E-commerce Platform]
    end

    subgraph "CDH Platform"
        subgraph "Infrastructure"
            K8S[Kubernetes Cluster]
            KAFKA[Kafka]
            MONITORING["Monitoring & Logs<br/>(Prometheus, Grafana, EFK)"]
        end

        subgraph "Microservices"
            GW["API Gateway - Traefik"]
            USER["User & Permissions Service<br/>(Go)"]
            ORDER["Order Service<br/>(Go)"]
            INV["Inventory Service<br/>(Go)"]
            FIN["Finance & Tax Service<br/>(Go)"]
        end
        
        subgraph "Databases (PostgreSQL)"
            DB_USER[User DB]
            DB_ORDER[Order DB]
            DB_INV[Inventory DB]
            DB_FIN[Finance DB]
        end

        GW --> USER
        GW --> ORDER
        GW --> INV

        USER -- CRUD --> DB_USER
        ORDER -- CRUD --> DB_ORDER
        INV -- CRUD --> DB_INV
        FIN -- CRUD --> DB_FIN
        
        ORDER -- "orders.created" Event --> KAFKA
        KAFKA -- Consumes --> INV
        KAFKA -- Consumes --> FIN
    end

    subgraph "Third-Party Integration"
      LHDN[LHDN MyInvois API]
    end

    POS --> GW
    WMS --> GW
    ECOM --> GW
    FIN --> LHDN
```

### Architecture and Design Patterns
* **Microservices Architecture**: Decomposes functionality into independent, autonomous services for easier development, deployment, and scaling.
* **Event-Driven Architecture**: Services communicate asynchronously by publishing and subscribing to events via Kafka, improving system resilience and responsiveness.
* **API First**: Each service exposes its functionality through a well-defined API, which serves as the contract for inter-service communication.
* **Database per Service**: Ensures loose coupling between services and the independence of their data models.

