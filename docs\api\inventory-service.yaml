openapi: 3.0.0
info:
  title: Inventory Service API
  version: 1.0.0
  description: |
    The Inventory Service provides real-time inventory management and stock tracking capabilities.
    It serves as the single source of truth for all inventory data across omni-channel operations.
    
    ## Features
    - Real-time inventory queries with Redis caching
    - Atomic inventory operations
    - Event-driven inventory updates via Kafka
    - Graceful degradation when external services are unavailable
    
  contact:
    name: CDH Development Team
    email: <EMAIL>
  license:
    name: Proprietary
    
servers:
  - url: http://localhost:8081
    description: Local development server
  - url: https://api-dev.cdh.com
    description: Development environment
  - url: https://api.cdh.com
    description: Production environment

paths:
  /inventory:
    get:
      summary: Query inventory by SKUs
      description: |
        Retrieve inventory information for one or more SKUs. This endpoint supports:
        - Single or multiple SKU queries
        - Redis caching for improved performance
        - Graceful degradation when cache is unavailable
        
      parameters:
        - name: sku
          in: query
          required: true
          description: Comma-separated list of SKU codes to query
          schema:
            type: string
            pattern: '^[A-Z0-9\-,]+$'
            example: "LAPTOP-001,MOUSE-002"
          examples:
            single_sku:
              summary: Single SKU query
              value: "LAPTOP-001"
            multiple_skus:
              summary: Multiple SKU query
              value: "LAPTOP-001,MOUSE-002,KEYBOARD-003"
              
      responses:
        '200':
          description: Successful inventory query
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryResponse'
              examples:
                single_item:
                  summary: Single item response
                  value:
                    data:
                      - id: 1
                        sku: "LAPTOP-001"
                        product_name: "Gaming Laptop"
                        description: "High-performance gaming laptop"
                        stock_quantity: 50
                        reserved_quantity: 5
                        available_quantity: 45
                        unit_price: 2999.99
                        created_at: "2024-01-01T12:00:00Z"
                        updated_at: "2024-01-01T12:00:00Z"
                    cache_hit: true
                multiple_items:
                  summary: Multiple items response
                  value:
                    data:
                      - id: 1
                        sku: "LAPTOP-001"
                        product_name: "Gaming Laptop"
                        description: "High-performance gaming laptop"
                        stock_quantity: 50
                        reserved_quantity: 5
                        available_quantity: 45
                        unit_price: 2999.99
                        created_at: "2024-01-01T12:00:00Z"
                        updated_at: "2024-01-01T12:00:00Z"
                      - id: 2
                        sku: "MOUSE-002"
                        product_name: "Wireless Mouse"
                        description: "Ergonomic wireless mouse"
                        stock_quantity: 100
                        reserved_quantity: 10
                        available_quantity: 90
                        unit_price: 49.99
                        created_at: "2024-01-01T12:00:00Z"
                        updated_at: "2024-01-01T12:00:00Z"
                    cache_hit: false
                    
        '400':
          description: Bad request - missing or invalid SKU parameter
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "Invalid SKU parameter"
                message: "SKU parameter is required and must contain valid SKU codes"
                timestamp: "2024-01-01T12:00:00Z"
                
        '404':
          description: One or more SKUs not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "SKUs not found"
                message: "The following SKUs were not found: INVALID-001"
                timestamp: "2024-01-01T12:00:00Z"
                
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "Internal server error"
                message: "Database connection failed"
                timestamp: "2024-01-01T12:00:00Z"

  /health:
    get:
      summary: Comprehensive health check
      description: Returns the health status of the service and all its dependencies
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              example:
                status: "healthy"
                timestamp: "2024-01-01T12:00:00Z"
                dependencies:
                  database: "healthy"
                  redis: "healthy"
                  kafka: "healthy"
                  
        '503':
          description: Service is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              example:
                status: "unhealthy"
                timestamp: "2024-01-01T12:00:00Z"
                dependencies:
                  database: "healthy"
                  redis: "unhealthy"
                  kafka: "healthy"

  /health/live:
    get:
      summary: Liveness probe
      description: Returns whether the service is alive and running
      responses:
        '200':
          description: Service is alive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LivenessResponse'
              example:
                status: "alive"
                timestamp: "2024-01-01T12:00:00Z"

  /health/ready:
    get:
      summary: Readiness probe
      description: Returns whether the service is ready to handle requests
      responses:
        '200':
          description: Service is ready
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadinessResponse'
              example:
                status: "ready"
                timestamp: "2024-01-01T12:00:00Z"
                dependencies:
                  database: "healthy"
                  redis: "healthy"
                  
        '503':
          description: Service is not ready
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadinessResponse'
              example:
                status: "not_ready"
                timestamp: "2024-01-01T12:00:00Z"
                dependencies:
                  database: "unhealthy"
                  redis: "healthy"

components:
  schemas:
    InventoryItem:
      type: object
      required:
        - id
        - sku
        - product_name
        - stock_quantity
        - reserved_quantity
        - available_quantity
        - unit_price
        - created_at
        - updated_at
      properties:
        id:
          type: integer
          format: int64
          description: Unique identifier for the inventory item
          example: 1
        sku:
          type: string
          description: Stock Keeping Unit code
          pattern: '^[A-Z0-9\-]+$'
          example: "LAPTOP-001"
        product_name:
          type: string
          description: Name of the product
          example: "Gaming Laptop"
        description:
          type: string
          nullable: true
          description: Product description
          example: "High-performance gaming laptop"
        stock_quantity:
          type: integer
          minimum: 0
          description: Total stock quantity
          example: 50
        reserved_quantity:
          type: integer
          minimum: 0
          description: Quantity reserved for pending orders
          example: 5
        available_quantity:
          type: integer
          minimum: 0
          description: Available quantity (stock - reserved)
          example: 45
        unit_price:
          type: number
          format: decimal
          minimum: 0
          description: Unit price in the base currency
          example: 2999.99
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2024-01-01T12:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-01T12:00:00Z"

    InventoryResponse:
      type: object
      required:
        - data
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/InventoryItem'
          description: Array of inventory items
        cache_hit:
          type: boolean
          description: Whether the response was served from cache
          example: true

    ErrorResponse:
      type: object
      required:
        - error
        - message
        - timestamp
      properties:
        error:
          type: string
          description: Error type
          example: "Invalid SKU parameter"
        message:
          type: string
          description: Detailed error message
          example: "SKU parameter is required and must contain valid SKU codes"
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
          example: "2024-01-01T12:00:00Z"

    HealthResponse:
      type: object
      required:
        - status
        - timestamp
      properties:
        status:
          type: string
          enum: [healthy, unhealthy]
          description: Overall health status
          example: "healthy"
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
          example: "2024-01-01T12:00:00Z"
        dependencies:
          type: object
          description: Health status of dependencies
          properties:
            database:
              type: string
              enum: [healthy, unhealthy]
              example: "healthy"
            redis:
              type: string
              enum: [healthy, unhealthy]
              example: "healthy"
            kafka:
              type: string
              enum: [healthy, unhealthy]
              example: "healthy"

    LivenessResponse:
      type: object
      required:
        - status
        - timestamp
      properties:
        status:
          type: string
          enum: [alive]
          description: Liveness status
          example: "alive"
        timestamp:
          type: string
          format: date-time
          description: Check timestamp
          example: "2024-01-01T12:00:00Z"

    ReadinessResponse:
      type: object
      required:
        - status
        - timestamp
      properties:
        status:
          type: string
          enum: [ready, not_ready]
          description: Readiness status
          example: "ready"
        timestamp:
          type: string
          format: date-time
          description: Check timestamp
          example: "2024-01-01T12:00:00Z"
        dependencies:
          type: object
          description: Readiness status of dependencies
          properties:
            database:
              type: string
              enum: [healthy, unhealthy]
              example: "healthy"
            redis:
              type: string
              enum: [healthy, unhealthy]
              example: "healthy"

tags:
  - name: inventory
    description: Inventory management operations
  - name: health
    description: Health check endpoints
