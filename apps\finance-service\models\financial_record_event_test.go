package models

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFinancialRecordEvent_Validate(t *testing.T) {
	tests := []struct {
		name    string
		event   FinancialRecordEvent
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid event",
			event: FinancialRecordEvent{
				TransactionID:  "txn-123",
				OrderReference: "order-456",
				RevenueAmount:  100.00,
				TaxAmount:      6.00,
				Currency:       "MYR",
				Timestamp:      time.Now(),
				Metadata:       map[string]interface{}{"test": "value"},
			},
			wantErr: false,
		},
		{
			name: "missing transaction_id",
			event: FinancialRecordEvent{
				OrderReference: "order-456",
				RevenueAmount:  100.00,
				TaxAmount:      6.00,
				Currency:       "MYR",
				Timestamp:      time.Now(),
			},
			wantErr: true,
			errMsg:  "transaction_id is required",
		},
		{
			name: "missing order_reference",
			event: FinancialRecordEvent{
				TransactionID: "txn-123",
				RevenueAmount: 100.00,
				TaxAmount:     6.00,
				Currency:      "MYR",
				Timestamp:     time.Now(),
			},
			wantErr: true,
			errMsg:  "order_reference is required",
		},
		{
			name: "negative revenue_amount",
			event: FinancialRecordEvent{
				TransactionID:  "txn-123",
				OrderReference: "order-456",
				RevenueAmount:  -100.00,
				TaxAmount:      6.00,
				Currency:       "MYR",
				Timestamp:      time.Now(),
			},
			wantErr: true,
			errMsg:  "revenue_amount cannot be negative",
		},
		{
			name: "negative tax_amount",
			event: FinancialRecordEvent{
				TransactionID:  "txn-123",
				OrderReference: "order-456",
				RevenueAmount:  100.00,
				TaxAmount:      -6.00,
				Currency:       "MYR",
				Timestamp:      time.Now(),
			},
			wantErr: true,
			errMsg:  "tax_amount cannot be negative",
		},
		{
			name: "missing currency",
			event: FinancialRecordEvent{
				TransactionID:  "txn-123",
				OrderReference: "order-456",
				RevenueAmount:  100.00,
				TaxAmount:      6.00,
				Timestamp:      time.Now(),
			},
			wantErr: true,
			errMsg:  "currency is required",
		},
		{
			name: "zero timestamp",
			event: FinancialRecordEvent{
				TransactionID:  "txn-123",
				OrderReference: "order-456",
				RevenueAmount:  100.00,
				TaxAmount:      6.00,
				Currency:       "MYR",
			},
			wantErr: true,
			errMsg:  "timestamp is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.event.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestFinancialRecordEvent_ToJSON(t *testing.T) {
	timestamp := time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC)
	event := FinancialRecordEvent{
		TransactionID:  "txn-123",
		OrderReference: "order-456",
		RevenueAmount:  100.00,
		TaxAmount:      6.00,
		Currency:       "MYR",
		Timestamp:      timestamp,
		Metadata: map[string]interface{}{
			"payment_method": "credit_card",
			"source":         "test",
		},
	}

	jsonData, err := event.ToJSON()
	require.NoError(t, err)

	// Verify JSON structure
	var result map[string]interface{}
	err = json.Unmarshal(jsonData, &result)
	require.NoError(t, err)

	assert.Equal(t, "txn-123", result["transaction_id"])
	assert.Equal(t, "order-456", result["order_reference"])
	assert.Equal(t, 100.0, result["revenue_amount"])
	assert.Equal(t, 6.0, result["tax_amount"])
	assert.Equal(t, "MYR", result["currency"])
	assert.NotNil(t, result["metadata"])
}

func TestFromJSON(t *testing.T) {
	tests := []struct {
		name    string
		json    string
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid JSON",
			json: `{
				"transaction_id": "txn-123",
				"order_reference": "order-456",
				"revenue_amount": 100.00,
				"tax_amount": 6.00,
				"currency": "MYR",
				"timestamp": "2023-12-25T10:30:00Z",
				"metadata": {"test": "value"}
			}`,
			wantErr: false,
		},
		{
			name: "invalid JSON",
			json: `{
				"transaction_id": "txn-123",
				"order_reference": "order-456",
				"revenue_amount": "invalid",
			}`,
			wantErr: true,
		},
		{
			name: "missing required field",
			json: `{
				"order_reference": "order-456",
				"revenue_amount": 100.00,
				"tax_amount": 6.00,
				"currency": "MYR",
				"timestamp": "2023-12-25T10:30:00Z"
			}`,
			wantErr: true,
			errMsg:  "transaction_id is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event, err := FromJSON([]byte(tt.json))
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
				assert.Nil(t, event)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, event)
			}
		})
	}
}

func TestFinancialRecordEvent_CalculateTotalAmount(t *testing.T) {
	event := FinancialRecordEvent{
		RevenueAmount: 100.00,
		TaxAmount:     6.00,
	}

	total := event.CalculateTotalAmount()
	assert.Equal(t, 106.00, total)
}

func TestNewFinancialRecordEventFromEntry(t *testing.T) {
	timestamp := time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC)
	entry := &FinancialEntry{
		ID:              1,
		OrderID:         "order-456",
		TransactionID:   "txn-123",
		RevenueAmount:   100.00,
		TaxAmount:       6.00,
		Currency:        "MYR",
		PaymentMethod:   "credit_card",
		TransactionType: "sale",
		Description:     "Test transaction",
		CreatedAt:       timestamp,
		UpdatedAt:       timestamp,
	}

	event := NewFinancialRecordEventFromEntry(entry)

	assert.Equal(t, "txn-123", event.TransactionID)
	assert.Equal(t, "order-456", event.OrderReference)
	assert.Equal(t, 100.00, event.RevenueAmount)
	assert.Equal(t, 6.00, event.TaxAmount)
	assert.Equal(t, "MYR", event.Currency)
	assert.Equal(t, timestamp, event.Timestamp)

	// Verify metadata
	assert.Equal(t, "credit_card", event.Metadata["payment_method"])
	assert.Equal(t, "sale", event.Metadata["transaction_type"])
	assert.Equal(t, "Test transaction", event.Metadata["description"])
	assert.Equal(t, "finance-service", event.Metadata["source_service"])
	assert.Equal(t, "1.0", event.Metadata["event_version"])

	// Validate the created event
	err := event.Validate()
	assert.NoError(t, err)
}
