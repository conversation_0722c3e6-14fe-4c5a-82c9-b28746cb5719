---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: user-service-route
  namespace: cdh-dev
  labels:
    app: user-service
    component: routing
spec:
  entryPoints:
    - web
  routes:
    - match: PathPrefix(`/user`)
      kind: Rule
      services:
        - name: user-service
          port: 8080
    - match: Path(`/user/health`)
      kind: Rule
      services:
        - name: user-service
          port: 8080
    - match: Path(`/user/register`)
      kind: Rule
      services:
        - name: user-service
          port: 8080
    - match: Path(`/user/login`)
      kind: Rule
      services:
        - name: user-service
          port: 8080
