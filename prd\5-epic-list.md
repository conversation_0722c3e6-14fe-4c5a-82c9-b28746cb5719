# 5. Epic List

* **Epic 1: Platform Foundation & Core Service Framework**
    * **Goal**: To build the basic framework of the project, including a CI/CD automated deployment pipeline using K8s, configuration of the API Gateway (Traefik), and launching the first core service (User & Permissions Service) to handle basic authentication.
* **Epic 2: Core Transactional Flow Implementation**
    * **Goal**: To develop the Order Service and Inventory Service, implementing the core business process of receiving orders via API and asynchronously updating inventory through the message queue (Kafka).
* **Epic 3: Financial Integration & E-Invoicing Compliance**
    * **Goal**: To develop the Finance & Tax Service, enabling it to listen to order events and completing the critical integration with the LHDN MyInvois system for fully automated e-invoice processing.
* **Epic 4: Platform Observability & Management**
    * **Goal**: To fully deploy and configure monitoring (Prometheus & Grafana) and logging (EFK) systems, and create a simple internal admin backend for viewing system status and managing basic configurations.

