package errors

import (
	"database/sql"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPredefinedErrors(t *testing.T) {
	t.Run("ErrOrderNotFound", func(t *testing.T) {
		assert.Error(t, ErrOrderNotFound)
		assert.Contains(t, ErrOrderNotFound.Error(), "order not found")
	})

	t.Run("ErrInvalidOrderID", func(t *testing.T) {
		assert.Error(t, ErrInvalidOrderID)
		assert.Contains(t, ErrInvalidOrderID.Error(), "invalid order ID")
	})

	t.Run("ErrInvalidJSON", func(t *testing.T) {
		assert.Error(t, ErrInvalidJSON)
		assert.Contains(t, ErrInvalidJSON.Error(), "invalid JSON")
	})

	t.Run("ErrValidationFailed", func(t *testing.T) {
		assert.Error(t, ErrValidationFailed)
		assert.Contains(t, ErrValidationFailed.Error(), "validation failed")
	})

	t.Run("ErrDatabaseOperation", func(t *testing.T) {
		assert.Error(t, ErrDatabaseOperation)
		assert.Contains(t, ErrDatabaseOperation.Error(), "database operation failed")
	})
}

func TestValidationError(t *testing.T) {
	t.Run("Create validation error", func(t *testing.T) {
		err := NewValidationError("field", "message")
		
		assert.Equal(t, "field", err.Field)
		assert.Equal(t, "message", err.Message)
		assert.Equal(t, "field: message", err.Error())
	})

	t.Run("Validation error implements error interface", func(t *testing.T) {
		err := NewValidationError("test_field", "test message")
		
		var e error = err
		assert.NotNil(t, e)
		assert.Equal(t, "test_field: test message", e.Error())
	})

	t.Run("Validation error with empty field", func(t *testing.T) {
		err := NewValidationError("", "message")
		
		assert.Equal(t, "", err.Field)
		assert.Equal(t, "message", err.Message)
		assert.Equal(t, ": message", err.Error())
	})

	t.Run("Validation error with empty message", func(t *testing.T) {
		err := NewValidationError("field", "")
		
		assert.Equal(t, "field", err.Field)
		assert.Equal(t, "", err.Message)
		assert.Equal(t, "field: ", err.Error())
	})
}

func TestDatabaseError(t *testing.T) {
	t.Run("Create database error with wrapped error", func(t *testing.T) {
		originalErr := sql.ErrNoRows
		err := NewDatabaseError("operation", originalErr)
		
		assert.Equal(t, "operation", err.Operation)
		assert.Equal(t, originalErr, err.Err)
		assert.Contains(t, err.Error(), "database operation failed")
		assert.Contains(t, err.Error(), originalErr.Error())
	})

	t.Run("Database error implements error interface", func(t *testing.T) {
		originalErr := errors.New("test error")
		err := NewDatabaseError("test_operation", originalErr)
		
		var e error = err
		assert.NotNil(t, e)
		assert.Contains(t, e.Error(), "database test_operation failed")
	})

	t.Run("Database error with nil wrapped error", func(t *testing.T) {
		err := NewDatabaseError("operation", nil)
		
		assert.Equal(t, "operation", err.Operation)
		assert.Nil(t, err.Err)
		assert.Contains(t, err.Error(), "database operation failed")
	})

	t.Run("Database error unwrapping", func(t *testing.T) {
		originalErr := errors.New("original error")
		err := NewDatabaseError("operation", originalErr)
		
		unwrapped := err.Unwrap()
		assert.Equal(t, originalErr, unwrapped)
	})
}

func TestErrorTypes(t *testing.T) {
	t.Run("ValidationError type assertion", func(t *testing.T) {
		err := NewValidationError("field", "message")
		
		var validationErr ValidationError
		assert.True(t, errors.As(err, &validationErr))
		assert.Equal(t, "field", validationErr.Field)
		assert.Equal(t, "message", validationErr.Message)
	})

	t.Run("DatabaseError type assertion", func(t *testing.T) {
		originalErr := sql.ErrNoRows
		err := NewDatabaseError("operation", originalErr)
		
		var dbErr DatabaseError
		assert.True(t, errors.As(err, &dbErr))
		assert.Equal(t, "operation", dbErr.Operation)
		assert.Equal(t, originalErr, dbErr.Err)
	})
}

func TestErrorChaining(t *testing.T) {
	t.Run("Chain validation and database errors", func(t *testing.T) {
		validationErr := NewValidationError("field", "invalid value")
		dbErr := NewDatabaseError("create order", validationErr)
		
		assert.Error(t, dbErr)
		assert.Contains(t, dbErr.Error(), "database create order failed")
		assert.Contains(t, dbErr.Error(), "field: invalid value")
		
		// Should be able to unwrap to get the validation error
		var validationErrUnwrapped ValidationError
		assert.True(t, errors.As(dbErr, &validationErrUnwrapped))
	})

	t.Run("Chain multiple database errors", func(t *testing.T) {
		innerErr := NewDatabaseError("connect", sql.ErrConnDone)
		outerErr := NewDatabaseError("transaction", innerErr)
		
		assert.Error(t, outerErr)
		assert.Contains(t, outerErr.Error(), "database transaction failed")
		assert.Contains(t, outerErr.Error(), "database connect failed")
		
		// Should be able to unwrap to get the inner database error
		var dbErrUnwrapped DatabaseError
		assert.True(t, errors.As(outerErr, &dbErrUnwrapped))
	})
}

func TestErrorComparison(t *testing.T) {
	t.Run("Compare predefined errors", func(t *testing.T) {
		err1 := ErrOrderNotFound
		err2 := ErrOrderNotFound
		
		assert.True(t, errors.Is(err1, err2))
		assert.False(t, errors.Is(err1, ErrInvalidOrderID))
	})

	t.Run("Compare wrapped errors", func(t *testing.T) {
		originalErr := ErrOrderNotFound
		wrappedErr := NewDatabaseError("get order", originalErr)
		
		assert.True(t, errors.Is(wrappedErr, ErrOrderNotFound))
		assert.False(t, errors.Is(wrappedErr, ErrInvalidOrderID))
	})
}
