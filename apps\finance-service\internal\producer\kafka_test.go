package producer

import (
	"context"
	"testing"
	"time"

	"github.com/IBM/sarama"
	"github.com/company/cdh/apps/finance-service/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockSyncProducer implements sarama.SyncProducer for testing
type MockSyncProducer struct {
	messages []MockMessage
	closed   bool
	sendErr  error
}

type MockMessage struct {
	Topic     string
	Key       string
	Value     []byte
	Headers   map[string]string
	Timestamp time.Time
}

func (m *MockSyncProducer) SendMessage(msg *sarama.ProducerMessage) (partition int32, offset int64, err error) {
	if m.sendErr != nil {
		return 0, 0, m.sendErr
	}

	headers := make(map[string]string)
	for _, header := range msg.Headers {
		headers[string(header.Key)] = string(header.Value)
	}

	mockMsg := MockMessage{
		Topic:     msg.Topic,
		Key:       string(msg.Key.(sarama.StringEncoder)),
		Value:     []byte(msg.Value.(sarama.ByteEncoder)),
		Headers:   headers,
		Timestamp: msg.Timestamp,
	}

	m.messages = append(m.messages, mockMsg)
	return 0, int64(len(m.messages) - 1), nil
}

func (m *MockSyncProducer) SendMessages(msgs []*sarama.ProducerMessage) error {
	for _, msg := range msgs {
		_, _, err := m.SendMessage(msg)
		if err != nil {
			return err
		}
	}
	return nil
}

func (m *MockSyncProducer) Close() error {
	m.closed = true
	return nil
}

func (m *MockSyncProducer) GetMetadata() (*sarama.MetadataResponse, error) {
	return nil, nil
}

func (m *MockSyncProducer) IsTransactional() bool {
	return false
}

func (m *MockSyncProducer) TxnStatus() sarama.ProducerTxnStatusFlag {
	return 0
}

func (m *MockSyncProducer) BeginTxn() error {
	return nil
}

func (m *MockSyncProducer) CommitTxn() error {
	return nil
}

func (m *MockSyncProducer) AbortTxn() error {
	return nil
}

func (m *MockSyncProducer) AddOffsetsToTxn(offsets map[string][]*sarama.PartitionOffsetMetadata, groupId string) error {
	return nil
}

func (m *MockSyncProducer) AddMessageToTxn(msg *sarama.ConsumerMessage, groupId string, metadata *string) error {
	return nil
}

func TestNewKafkaProducer(t *testing.T) {
	// This test would require a real Kafka broker, so we'll test the configuration
	cfg := ProducerConfig{
		Brokers: []string{"localhost:9092"},
		Topic:   "test-topic",
	}

	// We can't test the actual creation without a real Kafka broker
	// but we can test the configuration structure
	assert.Equal(t, []string{"localhost:9092"}, cfg.Brokers)
	assert.Equal(t, "test-topic", cfg.Topic)
}

func TestKafkaProducer_PublishFinancialRecord(t *testing.T) {
	mockProducer := &MockSyncProducer{}
	
	producer := &KafkaProducer{
		producer: mockProducer,
		topic:    "financial.records.created",
	}

	timestamp := time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC)
	event := &models.FinancialRecordEvent{
		TransactionID:  "txn-123",
		OrderReference: "order-456",
		RevenueAmount:  100.00,
		TaxAmount:      6.00,
		Currency:       "MYR",
		Timestamp:      timestamp,
		Metadata: map[string]interface{}{
			"payment_method": "credit_card",
			"source_service": "finance-service",
			"event_version":  "1.0",
		},
	}

	ctx := context.Background()
	err := producer.PublishFinancialRecord(ctx, event)

	require.NoError(t, err)
	assert.Len(t, mockProducer.messages, 1)

	msg := mockProducer.messages[0]
	assert.Equal(t, "financial.records.created", msg.Topic)
	assert.Equal(t, "order-456", msg.Key)
	assert.Equal(t, "financial.record.created", msg.Headers["event_type"])
	assert.Equal(t, "1.0", msg.Headers["event_version"])
	assert.Equal(t, "finance-service", msg.Headers["source_service"])
	assert.Equal(t, timestamp, msg.Timestamp)

	// Verify the message content can be deserialized
	deserializedEvent, err := models.FromJSON(msg.Value)
	require.NoError(t, err)
	assert.Equal(t, event.TransactionID, deserializedEvent.TransactionID)
	assert.Equal(t, event.OrderReference, deserializedEvent.OrderReference)
}

func TestKafkaProducer_PublishFinancialRecord_InvalidEvent(t *testing.T) {
	mockProducer := &MockSyncProducer{}
	
	producer := &KafkaProducer{
		producer: mockProducer,
		topic:    "financial.records.created",
	}

	// Create invalid event (missing required fields)
	event := &models.FinancialRecordEvent{
		RevenueAmount: 100.00,
		TaxAmount:     6.00,
		// Missing required fields
	}

	ctx := context.Background()
	err := producer.PublishFinancialRecord(ctx, event)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid financial record event")
	assert.Len(t, mockProducer.messages, 0)
}

func TestKafkaProducer_PublishFinancialRecord_ProducerError(t *testing.T) {
	mockProducer := &MockSyncProducer{
		sendErr: assert.AnError,
	}
	
	producer := &KafkaProducer{
		producer: mockProducer,
		topic:    "financial.records.created",
	}

	timestamp := time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC)
	event := &models.FinancialRecordEvent{
		TransactionID:  "txn-123",
		OrderReference: "order-456",
		RevenueAmount:  100.00,
		TaxAmount:      6.00,
		Currency:       "MYR",
		Timestamp:      timestamp,
		Metadata: map[string]interface{}{
			"payment_method": "credit_card",
			"source_service": "finance-service",
			"event_version":  "1.0",
		},
	}

	ctx := context.Background()
	err := producer.PublishFinancialRecord(ctx, event)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to publish financial record event after 3 attempts")
}

func TestKafkaProducer_PublishFinancialRecordBatch(t *testing.T) {
	mockProducer := &MockSyncProducer{}
	
	producer := &KafkaProducer{
		producer: mockProducer,
		topic:    "financial.records.created",
	}

	timestamp := time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC)
	events := []*models.FinancialRecordEvent{
		{
			TransactionID:  "txn-123",
			OrderReference: "order-456",
			RevenueAmount:  100.00,
			TaxAmount:      6.00,
			Currency:       "MYR",
			Timestamp:      timestamp,
			Metadata: map[string]interface{}{
				"payment_method": "credit_card",
				"source_service": "finance-service",
				"event_version":  "1.0",
			},
		},
		{
			TransactionID:  "txn-124",
			OrderReference: "order-457",
			RevenueAmount:  200.00,
			TaxAmount:      12.00,
			Currency:       "MYR",
			Timestamp:      timestamp,
			Metadata: map[string]interface{}{
				"payment_method": "bank_transfer",
				"source_service": "finance-service",
				"event_version":  "1.0",
			},
		},
	}

	ctx := context.Background()
	err := producer.PublishFinancialRecordBatch(ctx, events)

	require.NoError(t, err)
	assert.Len(t, mockProducer.messages, 2)

	// Verify first message
	msg1 := mockProducer.messages[0]
	assert.Equal(t, "financial.records.created", msg1.Topic)
	assert.Equal(t, "order-456", msg1.Key)

	// Verify second message
	msg2 := mockProducer.messages[1]
	assert.Equal(t, "financial.records.created", msg2.Topic)
	assert.Equal(t, "order-457", msg2.Key)
}

func TestKafkaProducer_PublishFinancialRecordBatch_EmptySlice(t *testing.T) {
	mockProducer := &MockSyncProducer{}
	
	producer := &KafkaProducer{
		producer: mockProducer,
		topic:    "financial.records.created",
	}

	ctx := context.Background()
	err := producer.PublishFinancialRecordBatch(ctx, []*models.FinancialRecordEvent{})

	assert.NoError(t, err)
	assert.Len(t, mockProducer.messages, 0)
}

func TestKafkaProducer_Close(t *testing.T) {
	mockProducer := &MockSyncProducer{}
	
	producer := &KafkaProducer{
		producer: mockProducer,
		topic:    "financial.records.created",
	}

	err := producer.Close()

	assert.NoError(t, err)
	assert.True(t, mockProducer.closed)
}
