package response

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestErrorResponse(t *testing.T) {
	t.Run("Create error response", func(t *testing.T) {
		errResp := ErrorResponse{
			Error:   "test error",
			Message: "test message",
			Code:    400,
		}

		assert.Equal(t, "test error", errResp.Error)
		assert.Equal(t, "test message", errResp.Message)
		assert.Equal(t, 400, errResp.Code)
	})

	t.Run("Error response JSON serialization", func(t *testing.T) {
		errResp := ErrorResponse{
			Error:   "validation failed",
			Message: "invalid input",
			Code:    400,
		}

		jsonData, err := json.Marshal(errResp)
		require.NoError(t, err)

		var unmarshaled ErrorResponse
		err = json.Unmarshal(jsonData, &unmarshaled)
		require.NoError(t, err)

		assert.Equal(t, errResp.Error, unmarshaled.Error)
		assert.Equal(t, errResp.Message, unmarshaled.Message)
		assert.Equal(t, errResp.Code, unmarshaled.Code)
	})
}

func TestSuccessResponse(t *testing.T) {
	t.Run("Create success response", func(t *testing.T) {
		data := map[string]interface{}{
			"id":   1,
			"name": "test",
		}

		successResp := SuccessResponse{
			Data:    data,
			Message: "success message",
		}

		assert.Equal(t, data, successResp.Data)
		assert.Equal(t, "success message", successResp.Message)
	})

	t.Run("Success response JSON serialization", func(t *testing.T) {
		data := map[string]string{
			"status": "created",
		}

		successResp := SuccessResponse{
			Data:    data,
			Message: "order created successfully",
		}

		jsonData, err := json.Marshal(successResp)
		require.NoError(t, err)

		var unmarshaled SuccessResponse
		err = json.Unmarshal(jsonData, &unmarshaled)
		require.NoError(t, err)

		assert.Equal(t, successResp.Message, unmarshaled.Message)
		// Data is interface{}, so we need to compare as map
		dataMap := unmarshaled.Data.(map[string]interface{})
		assert.Equal(t, "created", dataMap["status"])
	})
}

func TestWriteJSON(t *testing.T) {
	t.Run("Write JSON response", func(t *testing.T) {
		w := httptest.NewRecorder()
		data := map[string]string{
			"message": "hello world",
		}

		WriteJSON(w, http.StatusOK, data)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response map[string]string
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.Equal(t, "hello world", response["message"])
	})

	t.Run("Write JSON with different status code", func(t *testing.T) {
		w := httptest.NewRecorder()
		data := map[string]int{
			"count": 42,
		}

		WriteJSON(w, http.StatusCreated, data)

		assert.Equal(t, http.StatusCreated, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response map[string]int
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.Equal(t, 42, response["count"])
	})
}

func TestWriteError(t *testing.T) {
	t.Run("Write error response without message", func(t *testing.T) {
		w := httptest.NewRecorder()
		err := errors.New("test error")

		WriteError(w, http.StatusBadRequest, err)

		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response ErrorResponse
		jsonErr := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, jsonErr)

		assert.Equal(t, "test error", response.Error)
		assert.Equal(t, "", response.Message)
		assert.Equal(t, http.StatusBadRequest, response.Code)
	})

	t.Run("Write error response with message", func(t *testing.T) {
		w := httptest.NewRecorder()
		err := errors.New("validation failed")

		WriteError(w, http.StatusUnprocessableEntity, err, "Invalid input provided")

		assert.Equal(t, http.StatusUnprocessableEntity, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response ErrorResponse
		jsonErr := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, jsonErr)

		assert.Equal(t, "validation failed", response.Error)
		assert.Equal(t, "Invalid input provided", response.Message)
		assert.Equal(t, http.StatusUnprocessableEntity, response.Code)
	})

	t.Run("Write error response with multiple messages", func(t *testing.T) {
		w := httptest.NewRecorder()
		err := errors.New("database error")

		// Only the first message should be used
		WriteError(w, http.StatusInternalServerError, err, "First message", "Second message")

		assert.Equal(t, http.StatusInternalServerError, w.Code)

		var response ErrorResponse
		jsonErr := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, jsonErr)

		assert.Equal(t, "database error", response.Error)
		assert.Equal(t, "First message", response.Message)
		assert.Equal(t, http.StatusInternalServerError, response.Code)
	})
}

func TestWriteSuccess(t *testing.T) {
	t.Run("Write success response without message", func(t *testing.T) {
		w := httptest.NewRecorder()
		data := map[string]string{
			"id": "123",
		}

		WriteSuccess(w, http.StatusOK, data)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response SuccessResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "", response.Message)
		dataMap := response.Data.(map[string]interface{})
		assert.Equal(t, "123", dataMap["id"])
	})

	t.Run("Write success response with message", func(t *testing.T) {
		w := httptest.NewRecorder()
		data := map[string]interface{}{
			"order_id": 42,
			"status":   "created",
		}

		WriteSuccess(w, http.StatusCreated, data, "Order created successfully")

		assert.Equal(t, http.StatusCreated, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response SuccessResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "Order created successfully", response.Message)
		dataMap := response.Data.(map[string]interface{})
		assert.Equal(t, float64(42), dataMap["order_id"]) // JSON numbers are float64
		assert.Equal(t, "created", dataMap["status"])
	})

	t.Run("Write success response with multiple messages", func(t *testing.T) {
		w := httptest.NewRecorder()
		data := "simple string data"

		// Only the first message should be used
		WriteSuccess(w, http.StatusOK, data, "First message", "Second message")

		assert.Equal(t, http.StatusOK, w.Code)

		var response SuccessResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "First message", response.Message)
		assert.Equal(t, "simple string data", response.Data)
	})

	t.Run("Write success response with nil data", func(t *testing.T) {
		w := httptest.NewRecorder()

		WriteSuccess(w, http.StatusNoContent, nil, "No content")

		assert.Equal(t, http.StatusNoContent, w.Code)

		var response SuccessResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "No content", response.Message)
		assert.Nil(t, response.Data)
	})
}
