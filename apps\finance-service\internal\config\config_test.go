package config

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestLoad_DefaultValues(t *testing.T) {
	// Clear environment variables
	envVars := []string{
		"PORT", "READ_TIMEOUT", "WRITE_TIMEOUT",
		"DB_HOST", "DB_PORT", "DB_USER", "DB_PASSWORD", "DB_NAME", "DB_SSLMODE",
		"KAFKA_BROKERS", "KAFKA_GROUP_ID", "KAFKA_TOPIC",
	}
	
	for _, env := range envVars {
		os.Unsetenv(env)
	}

	cfg := Load()

	// Test server config defaults
	assert.Equal(t, "8082", cfg.Server.Port)
	assert.Equal(t, 15*time.Second, cfg.Server.ReadTimeout)
	assert.Equal(t, 15*time.Second, cfg.Server.WriteTimeout)

	// Test database config defaults
	assert.Equal(t, "localhost", cfg.Database.Host)
	assert.Equal(t, "5435", cfg.Database.Port)
	assert.Equal(t, "postgres", cfg.Database.User)
	assert.Equal(t, "postgres", cfg.Database.Password)
	assert.Equal(t, "finance_db", cfg.Database.Name)
	assert.Equal(t, "disable", cfg.Database.SSLMode)

	// Test Kafka config defaults
	assert.Equal(t, []string{"localhost:9092"}, cfg.Kafka.Brokers)
	assert.Equal(t, "finance-service-group", cfg.Kafka.GroupID)
	assert.Equal(t, "orders.created", cfg.Kafka.Topic)
}

func TestLoad_EnvironmentVariables(t *testing.T) {
	// Set environment variables
	os.Setenv("PORT", "9000")
	os.Setenv("READ_TIMEOUT", "30s")
	os.Setenv("WRITE_TIMEOUT", "45s")
	os.Setenv("DB_HOST", "test-db")
	os.Setenv("DB_PORT", "5432")
	os.Setenv("DB_USER", "testuser")
	os.Setenv("DB_PASSWORD", "testpass")
	os.Setenv("DB_NAME", "testdb")
	os.Setenv("DB_SSLMODE", "require")
	os.Setenv("KAFKA_BROKERS", "kafka1:9092,kafka2:9092")
	os.Setenv("KAFKA_GROUP_ID", "test-group")
	os.Setenv("KAFKA_TOPIC", "test.topic")

	defer func() {
		// Clean up environment variables
		envVars := []string{
			"PORT", "READ_TIMEOUT", "WRITE_TIMEOUT",
			"DB_HOST", "DB_PORT", "DB_USER", "DB_PASSWORD", "DB_NAME", "DB_SSLMODE",
			"KAFKA_BROKERS", "KAFKA_GROUP_ID", "KAFKA_TOPIC",
		}
		for _, env := range envVars {
			os.Unsetenv(env)
		}
	}()

	cfg := Load()

	// Test server config from environment
	assert.Equal(t, "9000", cfg.Server.Port)
	assert.Equal(t, 30*time.Second, cfg.Server.ReadTimeout)
	assert.Equal(t, 45*time.Second, cfg.Server.WriteTimeout)

	// Test database config from environment
	assert.Equal(t, "test-db", cfg.Database.Host)
	assert.Equal(t, "5432", cfg.Database.Port)
	assert.Equal(t, "testuser", cfg.Database.User)
	assert.Equal(t, "testpass", cfg.Database.Password)
	assert.Equal(t, "testdb", cfg.Database.Name)
	assert.Equal(t, "require", cfg.Database.SSLMode)

	// Test Kafka config from environment
	assert.Equal(t, []string{"kafka1:9092,kafka2:9092"}, cfg.Kafka.Brokers)
	assert.Equal(t, "test-group", cfg.Kafka.GroupID)
	assert.Equal(t, "test.topic", cfg.Kafka.Topic)
}

func TestDatabaseConfig_DSN(t *testing.T) {
	cfg := DatabaseConfig{
		Host:     "localhost",
		Port:     "5435",
		User:     "postgres",
		Password: "postgres",
		Name:     "finance_db",
		SSLMode:  "disable",
	}

	expected := "host=localhost port=5435 user=postgres password=postgres dbname=finance_db sslmode=disable"
	assert.Equal(t, expected, cfg.DSN())
}

func TestGetEnv(t *testing.T) {
	// Test with existing environment variable
	os.Setenv("TEST_VAR", "test_value")
	defer os.Unsetenv("TEST_VAR")

	result := getEnv("TEST_VAR", "default")
	assert.Equal(t, "test_value", result)

	// Test with non-existing environment variable
	result = getEnv("NON_EXISTING_VAR", "default")
	assert.Equal(t, "default", result)
}

func TestParseDuration(t *testing.T) {
	tests := []struct {
		input    string
		expected time.Duration
	}{
		{"30s", 30 * time.Second},
		{"1m", 1 * time.Minute},
		{"2h", 2 * time.Hour},
		{"invalid", 15 * time.Second}, // Should return default
		{"", 15 * time.Second},        // Should return default
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := parseDuration(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}