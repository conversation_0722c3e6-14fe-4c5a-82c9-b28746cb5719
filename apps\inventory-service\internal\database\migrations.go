package database

import (
	"database/sql"
	"fmt"
)

// RunMigrations runs all database migrations
func RunMigrations(db *sql.DB) error {
	migrations := []string{
		createInventoryTable,
		createInventoryIndexes,
	}

	for i, migration := range migrations {
		if err := runMigration(db, migration); err != nil {
			return fmt.Errorf("failed to run migration %d: %w", i+1, err)
		}
	}

	return nil
}

// runMigration executes a single migration
func runMigration(db *sql.DB, migration string) error {
	_, err := db.Exec(migration)
	return err
}

// createInventoryTable creates the inventory table
const createInventoryTable = `
CREATE TABLE IF NOT EXISTS inventory (
    id SERIAL PRIMARY KEY,
    sku VARCHAR(100) UNIQUE NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    description TEXT,
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    reserved_quantity INTEGER NOT NULL DEFAULT 0,
    available_quantity INTEGER GENERATED ALWAYS AS (stock_quantity - reserved_quantity) STORED,
    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT positive_stock CHECK (stock_quantity >= 0),
    CONSTRAINT positive_reserved CHECK (reserved_quantity >= 0),
    CONSTRAINT reserved_not_exceed_stock CHECK (reserved_quantity <= stock_quantity)
);
`

// createInventoryIndexes creates indexes for the inventory table
const createInventoryIndexes = `
CREATE INDEX IF NOT EXISTS idx_inventory_sku ON inventory(sku);
CREATE INDEX IF NOT EXISTS idx_inventory_available_quantity ON inventory(available_quantity);
CREATE INDEX IF NOT EXISTS idx_inventory_created_at ON inventory(created_at);
`
