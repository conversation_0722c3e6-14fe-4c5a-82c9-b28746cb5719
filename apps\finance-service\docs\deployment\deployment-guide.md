# Finance Service Deployment Guide

## Overview

This guide covers deployment procedures, monitoring setup, and operational guidelines for the Finance Service with Kafka event publishing.

## Prerequisites

### Infrastructure Requirements

- **Kubernetes Cluster**: v1.24+
- **PostgreSQL**: v13+ (for finance database)
- **Apache Kafka**: v3.0+ (for event streaming)
- **Redis**: v6+ (for caching, optional)
- **Prometheus**: v2.30+ (for metrics)
- **Grafana**: v8.0+ (for dashboards)

### Resource Requirements

#### Development Environment
- **CPU**: 0.5 cores
- **Memory**: 512 MB
- **Storage**: 1 GB

#### Production Environment
- **CPU**: 2 cores
- **Memory**: 2 GB
- **Storage**: 10 GB
- **Replicas**: 3 (minimum for HA)

## Deployment Configurations

### Kubernetes Deployment

#### Namespace

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: finance-service
  labels:
    app: finance-service
    environment: production
```

#### ConfigMap

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: finance-service-config
  namespace: finance-service
data:
  config.yaml: |
    database:
      host: postgres-service
      port: 5432
      name: finance_db
      ssl_mode: require
      max_connections: 50
      connection_timeout: 30s
    
    kafka:
      producer:
        brokers:
          - kafka-1:9092
          - kafka-2:9092
          - kafka-3:9092
        topic: financial.records.created
        client_id: finance-service
        acks: all
        retries: 10
        enable_idempotence: true
        compression_type: lz4
    
    logging:
      level: info
      format: json
    
    metrics:
      enabled: true
      port: 8081
      path: /metrics
```

#### Secret

```yaml
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: finance-service-secrets
  namespace: finance-service
type: Opaque
data:
  db-username: <base64-encoded-username>
  db-password: <base64-encoded-password>
  kafka-ssl-truststore-password: <base64-encoded-password>
  kafka-ssl-keystore-password: <base64-encoded-password>
```

#### Deployment

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: finance-service
  namespace: finance-service
  labels:
    app: finance-service
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: finance-service
  template:
    metadata:
      labels:
        app: finance-service
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8081"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: finance-service
        image: finance-service:v1.0.0
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081
          name: metrics
        - containerPort: 8082
          name: health
        env:
        - name: DB_HOST
          value: "postgres-service"
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: "finance_db"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: finance-service-secrets
              key: db-username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: finance-service-secrets
              key: db-password
        - name: KAFKA_BROKERS
          value: "kafka-1:9092,kafka-2:9092,kafka-3:9092"
        volumeMounts:
        - name: config
          mountPath: /app/config
        - name: ssl-certs
          mountPath: /etc/kafka/ssl
          readOnly: true
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 2Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8082
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8082
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config
        configMap:
          name: finance-service-config
      - name: ssl-certs
        secret:
          secretName: kafka-ssl-certs
```

#### Service

```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: finance-service
  namespace: finance-service
  labels:
    app: finance-service
spec:
  selector:
    app: finance-service
  ports:
  - name: http
    port: 80
    targetPort: 8080
  - name: metrics
    port: 8081
    targetPort: 8081
  - name: health
    port: 8082
    targetPort: 8082
  type: ClusterIP
```

#### Horizontal Pod Autoscaler

```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: finance-service-hpa
  namespace: finance-service
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: finance-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Database Migration

#### Migration Job

```yaml
# migration-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: finance-service-migration
  namespace: finance-service
spec:
  template:
    spec:
      containers:
      - name: migration
        image: finance-service:v1.0.0
        command: ["/app/migrate"]
        args: ["up"]
        env:
        - name: DB_HOST
          value: "postgres-service"
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: "finance_db"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: finance-service-secrets
              key: db-username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: finance-service-secrets
              key: db-password
      restartPolicy: OnFailure
  backoffLimit: 3
```

## Monitoring and Observability

### Prometheus Configuration

#### ServiceMonitor

```yaml
# servicemonitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: finance-service
  namespace: finance-service
  labels:
    app: finance-service
spec:
  selector:
    matchLabels:
      app: finance-service
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
```

### Grafana Dashboards

#### Finance Service Dashboard

```json
{
  "dashboard": {
    "id": null,
    "title": "Finance Service - Event Publishing",
    "tags": ["finance", "kafka", "events"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Event Publishing Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(finance_service_events_published_total[5m])",
            "legendFormat": "Events/sec"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "reqps",
            "min": 0
          }
        }
      },
      {
        "id": 2,
        "title": "Event Publishing Errors",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(finance_service_events_failed_total[5m])",
            "legendFormat": "Errors/sec"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "reqps",
            "min": 0,
            "color": {
              "mode": "thresholds",
              "thresholds": [
                {"color": "green", "value": 0},
                {"color": "red", "value": 0.1}
              ]
            }
          }
        }
      },
      {
        "id": 3,
        "title": "Kafka Producer Latency",
        "type": "timeseries",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, rate(finance_service_kafka_publish_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          },
          {
            "expr": "histogram_quantile(0.95, rate(finance_service_kafka_publish_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.99, rate(finance_service_kafka_publish_duration_seconds_bucket[5m]))",
            "legendFormat": "99th percentile"
          }
        ]
      },
      {
        "id": 4,
        "title": "Database Connection Pool",
        "type": "timeseries",
        "targets": [
          {
            "expr": "finance_service_db_connections_active",
            "legendFormat": "Active Connections"
          },
          {
            "expr": "finance_service_db_connections_idle",
            "legendFormat": "Idle Connections"
          }
        ]
      }
    ]
  }
}
```

### Alerting Rules

#### Prometheus Alerts

```yaml
# alerts.yaml
groups:
- name: finance-service
  rules:
  - alert: FinanceServiceDown
    expr: up{job="finance-service"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Finance Service is down"
      description: "Finance Service has been down for more than 1 minute"

  - alert: HighEventPublishingErrors
    expr: rate(finance_service_events_failed_total[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High event publishing error rate"
      description: "Event publishing error rate is {{ $value }} errors/sec"

  - alert: KafkaProducerHighLatency
    expr: histogram_quantile(0.95, rate(finance_service_kafka_publish_duration_seconds_bucket[5m])) > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High Kafka producer latency"
      description: "95th percentile latency is {{ $value }}s"

  - alert: DatabaseConnectionPoolExhausted
    expr: finance_service_db_connections_active / finance_service_db_connections_max > 0.9
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Database connection pool nearly exhausted"
      description: "{{ $value | humanizePercentage }} of database connections are in use"
```

## Deployment Procedures

### Rolling Deployment

```bash
#!/bin/bash
# deploy.sh

set -e

VERSION=$1
NAMESPACE="finance-service"

if [ -z "$VERSION" ]; then
    echo "Usage: $0 <version>"
    exit 1
fi

echo "Deploying Finance Service version $VERSION..."

# Update image in deployment
kubectl set image deployment/finance-service \
    finance-service=finance-service:$VERSION \
    -n $NAMESPACE

# Wait for rollout to complete
kubectl rollout status deployment/finance-service -n $NAMESPACE --timeout=300s

# Verify deployment
kubectl get pods -n $NAMESPACE -l app=finance-service

echo "Deployment completed successfully!"
```

### Blue-Green Deployment

```bash
#!/bin/bash
# blue-green-deploy.sh

set -e

VERSION=$1
NAMESPACE="finance-service"
CURRENT_COLOR=$(kubectl get service finance-service -n $NAMESPACE -o jsonpath='{.spec.selector.color}')
NEW_COLOR=$([ "$CURRENT_COLOR" = "blue" ] && echo "green" || echo "blue")

echo "Current color: $CURRENT_COLOR"
echo "Deploying to: $NEW_COLOR"

# Deploy new version
kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: finance-service-$NEW_COLOR
  namespace: $NAMESPACE
spec:
  replicas: 3
  selector:
    matchLabels:
      app: finance-service
      color: $NEW_COLOR
  template:
    metadata:
      labels:
        app: finance-service
        color: $NEW_COLOR
    spec:
      containers:
      - name: finance-service
        image: finance-service:$VERSION
        # ... rest of container spec
EOF

# Wait for new deployment to be ready
kubectl rollout status deployment/finance-service-$NEW_COLOR -n $NAMESPACE

# Switch traffic
kubectl patch service finance-service -n $NAMESPACE -p '{"spec":{"selector":{"color":"'$NEW_COLOR'"}}}'

# Clean up old deployment
kubectl delete deployment finance-service-$CURRENT_COLOR -n $NAMESPACE

echo "Blue-green deployment completed!"
```

### Rollback Procedure

```bash
#!/bin/bash
# rollback.sh

NAMESPACE="finance-service"

echo "Rolling back Finance Service deployment..."

# Rollback to previous version
kubectl rollout undo deployment/finance-service -n $NAMESPACE

# Wait for rollback to complete
kubectl rollout status deployment/finance-service -n $NAMESPACE

# Verify rollback
kubectl get pods -n $NAMESPACE -l app=finance-service

echo "Rollback completed successfully!"
```

## Health Checks

### Application Health Endpoints

```go
// Health check implementation
func (h *HealthHandler) LivenessCheck(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusOK)
    json.NewEncoder(w).Encode(map[string]string{
        "status": "alive",
        "timestamp": time.Now().UTC().Format(time.RFC3339),
    })
}

func (h *HealthHandler) ReadinessCheck(w http.ResponseWriter, r *http.Request) {
    checks := map[string]bool{
        "database": h.checkDatabase(),
        "kafka":    h.checkKafka(),
    }
    
    allHealthy := true
    for _, healthy := range checks {
        if !healthy {
            allHealthy = false
            break
        }
    }
    
    status := http.StatusOK
    if !allHealthy {
        status = http.StatusServiceUnavailable
    }
    
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(status)
    json.NewEncoder(w).Encode(map[string]interface{}{
        "status": map[string]string{
            "overall": func() string {
                if allHealthy { return "healthy" }
                return "unhealthy"
            }(),
        },
        "checks": checks,
        "timestamp": time.Now().UTC().Format(time.RFC3339),
    })
}
```

---

*This deployment guide is maintained by the Finance Service team. Last updated: 2025-08-02*
