package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestLoad(t *testing.T) {
	// Save original env vars
	originalVars := map[string]string{
		"PORT":        os.Getenv("PORT"),
		"DB_HOST":     os.Getenv("DB_HOST"),
		"DB_PORT":     os.<PERSON>env("DB_PORT"),
		"DB_USER":     os.<PERSON>env("DB_USER"),
		"DB_PASSWORD": os.<PERSON>env("DB_PASSWORD"),
		"DB_NAME":     os.<PERSON>env("DB_NAME"),
		"DB_SSLMODE":  os.<PERSON>env("DB_SSLMODE"),
	}

	// Clean up after test
	defer func() {
		for key, value := range originalVars {
			if value == "" {
				os.Unsetenv(key)
			} else {
				os.Setenv(key, value)
			}
		}
	}()

	t.Run("default configuration", func(t *testing.T) {
		// Clear all env vars
		for key := range originalVars {
			os.Unsetenv(key)
		}

		cfg, err := Load()
		require.NoError(t, err)

		assert.Equal(t, "8080", cfg.Server.Port)
		assert.Equal(t, "localhost", cfg.Database.Host)
		assert.Equal(t, "5433", cfg.Database.Port)
		assert.Equal(t, "postgres", cfg.Database.User)
		assert.Equal(t, "postgres", cfg.Database.Password)
		assert.Equal(t, "order_db", cfg.Database.DBName)
		assert.Equal(t, "disable", cfg.Database.SSLMode)
	})

	t.Run("custom configuration", func(t *testing.T) {
		os.Setenv("PORT", "9000")
		os.Setenv("DB_HOST", "custom-host")
		os.Setenv("DB_PORT", "5432")
		os.Setenv("DB_USER", "custom-user")
		os.Setenv("DB_PASSWORD", "custom-pass")
		os.Setenv("DB_NAME", "custom_db")
		os.Setenv("DB_SSLMODE", "require")

		cfg, err := Load()
		require.NoError(t, err)

		assert.Equal(t, "9000", cfg.Server.Port)
		assert.Equal(t, "custom-host", cfg.Database.Host)
		assert.Equal(t, "5432", cfg.Database.Port)
		assert.Equal(t, "custom-user", cfg.Database.User)
		assert.Equal(t, "custom-pass", cfg.Database.Password)
		assert.Equal(t, "custom_db", cfg.Database.DBName)
		assert.Equal(t, "require", cfg.Database.SSLMode)
	})

	t.Run("invalid port", func(t *testing.T) {
		os.Setenv("DB_PORT", "invalid")

		_, err := Load()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database port must be numeric")
	})
}

func TestDatabaseConfig_DSN(t *testing.T) {
	cfg := &DatabaseConfig{
		Host:     "localhost",
		Port:     "5433",
		User:     "postgres",
		Password: "secret",
		DBName:   "test_db",
		SSLMode:  "disable",
	}

	expected := "host=localhost port=5433 user=postgres password=secret dbname=test_db sslmode=disable"
	assert.Equal(t, expected, cfg.DSN())
}