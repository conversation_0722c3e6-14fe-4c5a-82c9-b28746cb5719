package models

import (
	"strings"
	"time"
)

// User represents a user in the system
type User struct {
	ID           int       `json:"id" db:"id"`
	Username     string    `json:"username" db:"username"`
	Email        string    `json:"email" db:"email"`
	PasswordHash string    `json:"-" db:"password_hash"` // Never include in JSON responses
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// UserRegistrationRequest represents the request payload for user registration
type UserRegistrationRequest struct {
	Username string `json:"username"`
	Email    string `json:"email"`
	Password string `json:"password"`
}

// UserRegistrationResponse represents the response for successful user registration
type UserRegistrationResponse struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	Message  string `json:"message"`
}

// UserLoginRequest represents the request payload for user login
type UserLoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// UserLoginResponse represents the response for successful user login
type UserLoginResponse struct {
	Token   string `json:"token"`
	UserID  int    `json:"user_id"`
	Message string `json:"message"`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
}

// ErrorCode represents standardized error codes
type ErrorCode string

const (
	ErrorCodeValidation           ErrorCode = "validation_error"
	ErrorCodeInvalidRequest       ErrorCode = "invalid_request"
	ErrorCodeUserExists           ErrorCode = "user_exists"
	ErrorCodeInternalError        ErrorCode = "internal_error"
	ErrorCodeMethodNotAllowed     ErrorCode = "method_not_allowed"
	ErrorCodeAuthenticationFailed ErrorCode = "authentication_failed"
)

// NewErrorResponse creates a standardized error response
func NewErrorResponse(code ErrorCode, message string) ErrorResponse {
	return ErrorResponse{
		Error:   string(code),
		Message: message,
	}
}

// Validate validates the user registration request
func (u *UserRegistrationRequest) Validate() error {
	if u.Username == "" {
		return &ValidationError{Field: "username", Message: "Username is required"}
	}
	if len(u.Username) < 3 {
		return &ValidationError{Field: "username", Message: "Username must be at least 3 characters long"}
	}
	if len(u.Username) > 50 {
		return &ValidationError{Field: "username", Message: "Username must be less than 50 characters"}
	}
	if u.Email == "" {
		return &ValidationError{Field: "email", Message: "Email is required"}
	}
	if !isValidEmail(u.Email) {
		return &ValidationError{Field: "email", Message: "Email format is invalid"}
	}
	if u.Password == "" {
		return &ValidationError{Field: "password", Message: "Password is required"}
	}
	if len(u.Password) < 8 {
		return &ValidationError{Field: "password", Message: "Password must be at least 8 characters long"}
	}
	if len(u.Password) > 128 {
		return &ValidationError{Field: "password", Message: "Password must be less than 128 characters"}
	}
	return nil
}

// Sanitize normalizes and cleans the registration request data
func (u *UserRegistrationRequest) Sanitize() {
	u.Username = strings.TrimSpace(strings.ToLower(u.Username))
	u.Email = strings.TrimSpace(strings.ToLower(u.Email))
	// Note: Password is not trimmed to preserve intentional whitespace
}

// Validate validates the user login request
func (u *UserLoginRequest) Validate() error {
	if u.Username == "" {
		return &ValidationError{Field: "username", Message: "Username is required"}
	}
	if u.Password == "" {
		return &ValidationError{Field: "password", Message: "Password is required"}
	}
	return nil
}

// Sanitize normalizes and cleans the login request data
func (u *UserLoginRequest) Sanitize() {
	u.Username = strings.TrimSpace(strings.ToLower(u.Username))
	// Note: Password is not trimmed to preserve intentional whitespace
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return e.Message
}

// isValidEmail performs basic email validation
func isValidEmail(email string) bool {
	// Basic email validation - contains @ and has parts before and after
	if len(email) < 5 || len(email) > 254 {
		return false
	}

	atIndex := strings.Index(email, "@")
	if atIndex <= 0 || atIndex >= len(email)-1 {
		return false
	}

	// Check for multiple @ symbols
	if strings.Count(email, "@") != 1 {
		return false
	}

	localPart := email[:atIndex]
	domainPart := email[atIndex+1:]

	// Basic checks for local and domain parts
	if len(localPart) == 0 || len(domainPart) == 0 {
		return false
	}

	// Domain must contain at least one dot
	if !strings.Contains(domainPart, ".") {
		return false
	}

	return true
}
