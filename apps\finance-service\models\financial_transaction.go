package models

import (
	"time"
	"errors"
)

// ProcessingStatus represents the status of financial transaction processing
type ProcessingStatus string

const (
	ProcessingStatusPending   ProcessingStatus = "pending"
	ProcessingStatusCompleted ProcessingStatus = "completed"
	ProcessingStatusFailed    ProcessingStatus = "failed"
	ProcessingStatusRetrying  ProcessingStatus = "retrying"
)

// FinancialTransaction represents an audit trail for financial processing
type FinancialTransaction struct {
	ID                int              `json:"id" db:"id"`
	OrderReference    string           `json:"order_reference" db:"order_reference"`
	EventID           string           `json:"event_id" db:"event_id"`
	ProcessingStatus  ProcessingStatus `json:"processing_status" db:"processing_status"`
	FinancialEntryID  *int             `json:"financial_entry_id" db:"financial_entry_id"`
	ProcessedAt       *time.Time       `json:"processed_at" db:"processed_at"`
	ErrorMessage      *string          `json:"error_message" db:"error_message"`
	CreatedAt         time.Time        `json:"created_at" db:"created_at"`
}

// Validate validates the financial transaction data
func (ft *FinancialTransaction) Validate() error {
	if ft.OrderReference == "" {
		return errors.New("order_reference is required")
	}
	if ft.EventID == "" {
		return errors.New("event_id is required")
	}
	if !isValidProcessingStatus(ft.ProcessingStatus) {
		return errors.New("invalid processing_status")
	}
	return nil
}

// MarkCompleted marks the transaction as completed
func (ft *FinancialTransaction) MarkCompleted(financialEntryID int) {
	ft.ProcessingStatus = ProcessingStatusCompleted
	ft.FinancialEntryID = &financialEntryID
	now := time.Now()
	ft.ProcessedAt = &now
	ft.ErrorMessage = nil
}

// MarkFailed marks the transaction as failed with an error message
func (ft *FinancialTransaction) MarkFailed(errorMsg string) {
	ft.ProcessingStatus = ProcessingStatusFailed
	ft.ErrorMessage = &errorMsg
	now := time.Now()
	ft.ProcessedAt = &now
}

// isValidProcessingStatus checks if the processing status is valid
func isValidProcessingStatus(status ProcessingStatus) bool {
	switch status {
	case ProcessingStatusPending, ProcessingStatusCompleted, ProcessingStatusFailed, ProcessingStatusRetrying:
		return true
	default:
		return false
	}
}