package tests

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/company/cdh/apps/finance-service/handlers"
	"github.com/company/cdh/apps/finance-service/internal/config"
	"github.com/company/cdh/apps/finance-service/internal/services"
	"github.com/company/cdh/apps/finance-service/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestAPIIntegration_CompleteWorkflow tests the complete API workflow
func TestAPIIntegration_CompleteWorkflow(t *testing.T) {
	// Setup test server
	mockRepo := NewMockFinanceRepository()
	mockProducer := NewMockEventProducer()
	financeService := services.NewFinanceService(mockRepo, mockProducer)

	// Create test data
	testEntries := []*models.FinancialEntry{
		{
			ID:              1,
			OrderID:         "ORD-2024-001",
			TransactionID:   "TXN-ORD-2024-001-1234567890",
			RevenueAmount:   10000,
			TaxAmount:       600,
			Currency:        "MYR",
			PaymentMethod:   "credit_card",
			TransactionType: "sale",
			Description:     "Product purchase - Widget A",
			CreatedAt:       time.Now().Add(-2 * time.Hour),
			UpdatedAt:       time.Now().Add(-2 * time.Hour),
		},
		{
			ID:              2,
			OrderID:         "ORD-2024-002",
			TransactionID:   "TXN-ORD-2024-002-**********",
			RevenueAmount:   5000,
			TaxAmount:       300,
			Currency:        "MYR",
			PaymentMethod:   "bank_transfer",
			TransactionType: "sale",
			Description:     "Service payment - Consultation",
			CreatedAt:       time.Now().Add(-1 * time.Hour),
			UpdatedAt:       time.Now().Add(-1 * time.Hour),
		},
	}

	for _, entry := range testEntries {
		mockRepo.entries = append(mockRepo.entries, entry)
	}

	// Setup real server with full authentication and middleware
	cfg := config.ServerConfig{
		JWTSecret:    "test-jwt-secret",
		APIKeySecret: "test-api-key-secret",
		Port:         "8082",
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
	}

	// Create a nil database for testing (handlers will handle gracefully)
	var db *sql.DB = nil

	// Create the real server with all middleware
	server := handlers.NewServer(cfg, db, financeService)

	// Extract the handler from the server's HTTP server
	// We need to access the mux that was created in NewServer
	testServer := httptest.NewServer(server.Handler())
	defer testServer.Close()

	// Use the real API key that's configured in the server
	testAPIKey := "test-api-key-12345"

	t.Run("Complete API Workflow", func(t *testing.T) {
		// Step 1: Get financial records without authentication (should fail)
		t.Run("Unauthenticated Request", func(t *testing.T) {
			req, err := http.NewRequest("GET", testServer.URL+"/api/v1/financial-records", nil)
			require.NoError(t, err)

			resp, err := http.DefaultClient.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
		})

		// Step 2: Get financial records with authentication
		t.Run("Authenticated Request - Get Records", func(t *testing.T) {
			req, err := http.NewRequest("GET", testServer.URL+"/api/v1/financial-records?page=1&limit=10", nil)
			require.NoError(t, err)
			req.Header.Set("X-API-Key", testAPIKey)

			resp, err := http.DefaultClient.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, http.StatusOK, resp.StatusCode)

			var response models.FinancialRecordsListResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Len(t, response.Data, 2)
			assert.Equal(t, 2, response.Pagination.Total)
			assert.Equal(t, 1, response.Pagination.Page)
			assert.Equal(t, 10, response.Pagination.Limit)
		})

		// Step 3: Get financial records with filtering
		t.Run("Filtered Request - By Order Reference", func(t *testing.T) {
			req, err := http.NewRequest("GET", testServer.URL+"/api/v1/financial-records?order_reference=ORD-2024-001", nil)
			require.NoError(t, err)
			req.Header.Set("X-API-Key", testAPIKey)

			resp, err := http.DefaultClient.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, http.StatusOK, resp.StatusCode)

			var response models.FinancialRecordsListResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Len(t, response.Data, 1)
			assert.Equal(t, "ORD-2024-001", response.Data[0].OrderID)
		})

		// Step 4: Update financial record status
		t.Run("Update Record Status", func(t *testing.T) {
			updateRequest := models.StatusUpdateRequest{
				Status: "processed",
				Notes:  "Processed by integration test",
			}

			requestBody, err := json.Marshal(updateRequest)
			require.NoError(t, err)

			req, err := http.NewRequest("PATCH", testServer.URL+"/api/v1/financial-records/1/status", bytes.NewBuffer(requestBody))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("X-API-Key", testAPIKey)

			resp, err := http.DefaultClient.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, http.StatusOK, resp.StatusCode)

			var response models.StatusUpdateResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Equal(t, 1, response.ID)
			assert.Equal(t, "processed", response.Status)
			assert.NotEmpty(t, response.UpdatedAt)
			assert.Equal(t, "Processed by integration test", response.ProcessingNotes)
		})

		// Step 5: Verify status update was applied
		t.Run("Verify Status Update", func(t *testing.T) {
			// Since our current model doesn't have status field in database,
			// we just verify that we can still query records (status update was processed)
			req, err := http.NewRequest("GET", testServer.URL+"/api/v1/financial-records", nil)
			require.NoError(t, err)
			req.Header.Set("X-API-Key", testAPIKey)

			resp, err := http.DefaultClient.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, http.StatusOK, resp.StatusCode)

			var response models.FinancialRecordsListResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			// Verify we still have our test data
			assert.NotNil(t, response.Data)
			assert.Len(t, response.Data, 2)
		})
	})
}

// Additional integration tests can be added here as needed
