package producer

import (
	"encoding/json"
	"log"
	"time"

	"github.com/IBM/sarama"

	"github.com/company/cdh/apps/inventory-service/internal/config"
	"github.com/company/cdh/apps/inventory-service/internal/errors"
	"github.com/company/cdh/apps/inventory-service/models"
)

// InventoryFailureProducer defines the interface for publishing inventory failure events
type InventoryFailureProducer interface {
	PublishInventoryFailure(orderID, sku string, requestedQty, availableQty int, failureType string) error
	Close() error
}

// KafkaProducer represents a Kafka producer for inventory failure events
type KafkaProducer struct {
	producer sarama.SyncProducer
	config   config.KafkaConfig
}

// NewKafkaProducer creates a new Kafka producer
func NewKafkaProducer(cfg config.KafkaConfig) (*KafkaProducer, error) {
	config := sarama.NewConfig()
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 3
	config.Producer.Return.Successes = true

	producer, err := sarama.NewSyncProducer(cfg.Brokers, config)
	if err != nil {
		return nil, errors.NewKafkaError("create producer", err)
	}

	return &KafkaProducer{
		producer: producer,
		config:   cfg,
	}, nil
}

// PublishInventoryFailure publishes an inventory failure event
func (kp *KafkaProducer) PublishInventoryFailure(orderID, sku string, requestedQty, availableQty int, failureType string) error {
	event := models.InventoryFailureEvent{
		OrderID:      orderID,
		SKU:          sku,
		RequestedQty: requestedQty,
		AvailableQty: availableQty,
		FailureType:  failureType,
		Timestamp:    time.Now(),
	}

	// Marshal the event to JSON
	eventData, err := json.Marshal(event)
	if err != nil {
		return errors.NewKafkaError("marshal failure event", err)
	}

	// Create the Kafka message
	message := &sarama.ProducerMessage{
		Topic: kp.config.FailureTopic,
		Key:   sarama.StringEncoder(orderID),
		Value: sarama.ByteEncoder(eventData),
	}

	// Send the message
	partition, offset, err := kp.producer.SendMessage(message)
	if err != nil {
		log.Printf("Failed to publish inventory failure event: %v", err)
		return errors.NewKafkaError("send failure event", err)
	}

	log.Printf("Published inventory failure event to topic %s, partition %d, offset %d: OrderID=%s, SKU=%s, Type=%s",
		kp.config.FailureTopic, partition, offset, orderID, sku, failureType)

	return nil
}

// Close closes the Kafka producer
func (kp *KafkaProducer) Close() error {
	return kp.producer.Close()
}
