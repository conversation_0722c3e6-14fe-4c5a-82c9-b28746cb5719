package testutils

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestHelper provides common testing utilities for CDH services
type TestHelper struct {
	t *testing.T
}

// NewTestHelper creates a new test helper instance
func NewTestHelper(t *testing.T) *TestHelper {
	return &TestHelper{t: t}
}

// AssertNoError asserts that an error is nil
func (h *TestHelper) AssertNoError(err error, msgAndArgs ...any) {
	assert.NoError(h.t, err, msgAndArgs...)
}

// RequireNoError requires that an error is nil (fails test immediately if not)
func (h *TestHelper) RequireNoError(err error, msgAndArgs ...any) {
	require.NoError(h.t, err, msgAndArgs...)
}

// AssertEqual asserts that two values are equal
func (h *TestHelper) AssertEqual(expected, actual any, msgAndArgs ...any) {
	assert.Equal(h.t, expected, actual, msgAndArgs...)
}

// RequireEqual requires that two values are equal
func (h *TestHelper) RequireEqual(expected, actual any, msgAndArgs ...any) {
	require.Equal(h.t, expected, actual, msgAndArgs...)
}

// AssertNotNil asserts that a value is not nil
func (h *TestHelper) AssertNotNil(object any, msgAndArgs ...any) {
	assert.NotNil(h.t, object, msgAndArgs...)
}

// RequireNotNil requires that a value is not nil
func (h *TestHelper) RequireNotNil(object any, msgAndArgs ...any) {
	require.NotNil(h.t, object, msgAndArgs...)
}
