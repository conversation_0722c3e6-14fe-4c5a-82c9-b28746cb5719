package models

import (
	"strings"
	"time"

	"github.com/company/cdh/apps/inventory-service/internal/errors"
)

// Inventory represents an inventory item in the database
type Inventory struct {
	ID                int       `json:"id" db:"id"`
	SKU               string    `json:"sku" db:"sku"`
	ProductName       string    `json:"product_name" db:"product_name"`
	Description       string    `json:"description" db:"description"`
	StockQuantity     int       `json:"stock_quantity" db:"stock_quantity"`
	ReservedQuantity  int       `json:"reserved_quantity" db:"reserved_quantity"`
	AvailableQuantity int       `json:"available_quantity" db:"available_quantity"`
	UnitPrice         float64   `json:"unit_price" db:"unit_price"`
	CreatedAt         time.Time `json:"created_at" db:"created_at"`
	UpdatedAt         time.Time `json:"updated_at" db:"updated_at"`
}

// CalculateAvailableQuantity calculates the available quantity
func (i *Inventory) CalculateAvailableQuantity() {
	i.AvailableQuantity = i.StockQuantity - i.ReservedQuantity
}

// Validate validates the inventory item
func (i *Inventory) Validate() error {
	if strings.TrimSpace(i.SKU) == "" {
		return errors.NewValidationError("sku", "SKU cannot be empty")
	}

	if strings.TrimSpace(i.ProductName) == "" {
		return errors.NewValidationError("product_name", "Product name cannot be empty")
	}

	if i.StockQuantity < 0 {
		return errors.NewValidationError("stock_quantity", "Stock quantity cannot be negative")
	}

	if i.ReservedQuantity < 0 {
		return errors.NewValidationError("reserved_quantity", "Reserved quantity cannot be negative")
	}

	if i.ReservedQuantity > i.StockQuantity {
		return errors.NewValidationError("reserved_quantity", "Reserved quantity cannot exceed stock quantity")
	}

	if i.UnitPrice < 0 {
		return errors.NewValidationError("unit_price", "Unit price cannot be negative")
	}

	return nil
}

// OrderCreatedEvent represents the event structure from the order service
type OrderCreatedEvent struct {
	OrderID    string      `json:"order_id"`
	CustomerID string      `json:"customer_id"`
	Items      []OrderItem `json:"items"`
	Status     string      `json:"status"`
	CreatedAt  time.Time   `json:"created_at"`
}

// Validate validates the order created event
func (oce *OrderCreatedEvent) Validate() error {
	if strings.TrimSpace(oce.OrderID) == "" {
		return errors.NewValidationError("order_id", "Order ID cannot be empty")
	}

	if strings.TrimSpace(oce.CustomerID) == "" {
		return errors.NewValidationError("customer_id", "Customer ID cannot be empty")
	}

	if len(oce.Items) == 0 {
		return errors.NewValidationError("items", "Order must have at least one item")
	}

	for i, item := range oce.Items {
		if err := item.Validate(); err != nil {
			return errors.NewValidationError("items", "Invalid item at index "+string(rune(i))+": "+err.Error())
		}
	}

	return nil
}

// OrderItem represents an item in an order
type OrderItem struct {
	SKU      string  `json:"sku"`
	Quantity int     `json:"quantity"`
	Price    float64 `json:"price"`
}

// Validate validates the order item
func (oi *OrderItem) Validate() error {
	if strings.TrimSpace(oi.SKU) == "" {
		return errors.NewValidationError("sku", "SKU cannot be empty")
	}

	if oi.Quantity <= 0 {
		return errors.NewValidationError("quantity", "Quantity must be positive")
	}

	if oi.Price < 0 {
		return errors.NewValidationError("price", "Price cannot be negative")
	}

	return nil
}

// InventoryUpdate represents an inventory update operation
type InventoryUpdate struct {
	SKU      string `json:"sku"`
	Quantity int    `json:"quantity"`
	Type     string `json:"type"` // "reserve", "release", "deduct"
}

// InventoryFailureEvent represents a failure event when inventory operations fail
type InventoryFailureEvent struct {
	OrderID      string    `json:"order_id"`
	SKU          string    `json:"sku"`
	RequestedQty int       `json:"requested_quantity"`
	AvailableQty int       `json:"available_quantity"`
	FailureType  string    `json:"failure_type"` // "out_of_stock", "product_not_found"
	Timestamp    time.Time `json:"timestamp"`
}
