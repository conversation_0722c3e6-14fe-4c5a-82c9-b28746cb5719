package producer

import (
	"context"

	"github.com/company/cdh/apps/finance-service/models"
)

// FinancialEventProducerInterface defines the interface for publishing financial events
type FinancialEventProducerInterface interface {
	// PublishFinancialRecord publishes a single financial record event
	PublishFinancialRecord(ctx context.Context, event *models.FinancialRecordEvent) error
	
	// PublishFinancialRecordBatch publishes multiple financial record events in a batch
	PublishFinancialRecordBatch(ctx context.Context, events []*models.FinancialRecordEvent) error
	
	// Close closes the producer and releases resources
	Close() error
}
